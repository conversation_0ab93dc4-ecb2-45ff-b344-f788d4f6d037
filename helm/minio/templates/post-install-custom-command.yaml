{{- if .Values.customCommands }}
apiVersion: batch/v1
kind: Job
metadata:
  name: {{ template "minio.fullname" . }}-custom-command-job
  namespace: {{ .Release.Namespace | quote }}
  labels:
    app: {{ template "minio.name" . }}-custom-command-job
    chart: {{ template "minio.chart" . }}
    release: {{ .Release.Name }}
    heritage: {{ .Release.Service }}
  annotations:
    "helm.sh/hook": post-install,post-upgrade
    "helm.sh/hook-delete-policy": hook-succeeded,before-hook-creation
{{- with .Values.customCommandJob.annotations }}
{{ toYaml . | indent 4 }}
{{- end }}
spec:
  template:
    metadata:
      labels:
        app: {{ template "minio.name" . }}-job
        release: {{ .Release.Name }}
{{- if .Values.podLabels }}
{{ toYaml .Values.podLabels | indent 8 }}
{{- end }}
{{- if .Values.customCommandJob.podAnnotations }}
      annotations:
{{ toYaml .Values.customCommandJob.podAnnotations | indent 8 }}
{{- end }}
    spec:
      restartPolicy: OnFailure
{{- include "minio.imagePullSecrets" . | indent 6 }}
{{- if .Values.nodeSelector }}
      nodeSelector:
{{ toYaml .Values.customCommandJob.nodeSelector | indent 8 }}
{{- end }}
{{- with .Values.customCommandJob.affinity }}
      affinity:
{{ toYaml . | indent 8 }}
{{- end }}
{{- with .Values.customCommandJob.tolerations }}
      tolerations:
{{ toYaml . | indent 8 }}
{{- end }}
{{- if .Values.customCommandJob.securityContext.enabled }}
      securityContext:
        runAsUser: {{ .Values.customCommandJob.securityContext.runAsUser }}
        runAsGroup: {{ .Values.customCommandJob.securityContext.runAsGroup }}
        fsGroup: {{ .Values.customCommandJob.securityContext.fsGroup }}
{{- end }}
      volumes:
        - name: minio-configuration
          projected:
            sources:
            - configMap:
                name: {{ template "minio.fullname" . }}
            - secret:
                name: {{ template "minio.secretName" . }}
        {{- if .Values.tls.enabled }}
        - name: cert-secret-volume-mc
          secret:
            secretName: {{ .Values.tls.certSecret }}
            items:
            - key: {{ .Values.tls.publicCrt }}
              path: CAs/public.crt
        {{ end }}
      containers:
      - name: minio-mc
        image: "{{ .Values.mcImage.repository }}:{{ .Values.mcImage.tag }}"
        imagePullPolicy: {{ .Values.mcImage.pullPolicy }}
        command: ["/bin/sh", "/config/custom-command"]
        env:
          - name: MINIO_ENDPOINT
            value: {{ template "minio.fullname" . }}
          - name: MINIO_PORT
            value: {{ .Values.service.port | quote }}
        volumeMounts:
          - name: minio-configuration
            mountPath: /config
          {{- if .Values.tls.enabled }}
          - name: cert-secret-volume-mc
            mountPath: {{ .Values.configPathmc }}certs
          {{ end }}
        resources:
{{ toYaml .Values.customCommandJob.resources | indent 10 }}
{{- end }}
