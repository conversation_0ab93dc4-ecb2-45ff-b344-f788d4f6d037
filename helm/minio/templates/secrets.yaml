{{- if not .Values.existingSecret }}
apiVersion: v1
kind: Secret
metadata:
  name: {{ template "minio.secretName" . }}
  namespace: {{ .Release.Namespace | quote }}
  labels:
    app: {{ template "minio.name" . }}
    chart: {{ template "minio.chart" . }}
    release: {{ .Release.Name }}
    heritage: {{ .Release.Service }}
type: Opaque
data:
  rootUser: {{ if .Values.rootUser }}{{ .Values.rootUser | toString | b64enc | quote }}{{ else }}{{ randAlphaNum 20 | b64enc | quote }}{{ end }}
  rootPassword: {{ if .Values.rootPassword }}{{ .Values.rootPassword | toString | b64enc | quote }}{{ else }}{{ randAlphaNum 40 | b64enc | quote }}{{ end }}
  {{- if eq .Values.gateway.type "gcs" }}
  {{- if .Values.gateway.gcs.serviceAccountFile }}
  service-account-file.json: {{ .Values.gateway.gcs.serviceAccountFile | b64enc }}
  {{- end }}
  {{- end }}
  {{- if .Values.etcd.clientCert }}
  etcd_client.crt: {{ .Values.etcd.clientCert | toString | b64enc | quote }}
  {{- end }}
  {{- if .Values.etcd.clientCertKey }}
  etcd_client.key: {{ .Values.etcd.clientCertKey | toString | b64enc | quote }}
  {{- end }}
{{- end }}
