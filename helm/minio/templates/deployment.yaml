{{- if eq .Values.mode "standalone" }}
{{ $scheme := "http" }}
{{- if .Values.tls.enabled }}
{{ $scheme = "https" }}
{{ end }}
{{ $bucketRoot := or ($.Values.bucketRoot) ($.Values.mountPath) }}
apiVersion: {{ template "minio.deployment.apiVersion" . }}
kind: Deployment
metadata:
  name: {{ template "minio.fullname" . }}
  namespace: {{ .Release.Namespace | quote }}
  labels:
    app: {{ template "minio.name" . }}
    chart: {{ template "minio.chart" . }}
    release: {{ .Release.Name }}
    heritage: {{ .Release.Service }}
{{- if .Values.additionalLabels }}
{{ toYaml .Values.additionalLabels | trimSuffix "\n" | indent 4 }}
{{- end }}
{{- if .Values.additionalAnnotations }}
  annotations:
{{ toYaml .Values.additionalAnnotations | trimSuffix "\n" | indent 4 }}
{{- end }}
spec:
  strategy:
    type: {{ .Values.DeploymentUpdate.type }}
    {{- if eq .Values.DeploymentUpdate.type "RollingUpdate" }}
    rollingUpdate:
      maxSurge: {{ .Values.DeploymentUpdate.maxSurge }}
      maxUnavailable: {{ .Values.DeploymentUpdate.maxUnavailable }}
    {{- end}}
  replicas: 1
  selector:
    matchLabels:
      app: {{ template "minio.name" . }}
      release: {{ .Release.Name }}
  template:
    metadata:
      name: {{ template "minio.fullname" . }}
      labels:
        app: {{ template "minio.name" . }}
        release: {{ .Release.Name }}
{{- if .Values.podLabels }}
{{ toYaml .Values.podLabels | indent 8 }}
{{- end }}
      annotations:
{{- if not .Values.ignoreChartChecksums }}
        checksum/secrets: {{ include (print $.Template.BasePath "/secrets.yaml") . | sha256sum }}
        checksum/config: {{ include (print $.Template.BasePath "/configmap.yaml") . | sha256sum }}
{{- end }}
{{- if .Values.podAnnotations }}
{{ toYaml .Values.podAnnotations | trimSuffix "\n" | indent 8 }}
{{- end }}
    spec:
  {{- if .Values.priorityClassName }}
      priorityClassName: "{{ .Values.priorityClassName }}"
  {{- end }}
{{- if and .Values.securityContext.enabled .Values.persistence.enabled }}
      securityContext:
        runAsUser: {{ .Values.securityContext.runAsUser }}
        runAsGroup: {{ .Values.securityContext.runAsGroup }}
        fsGroup: {{ .Values.securityContext.fsGroup }}
{{- end }}
{{ if .Values.serviceAccount.create }}
      serviceAccountName: {{ .Values.serviceAccount.name }}
{{- end }}
      containers:
        - name: {{ .Chart.Name }}
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          command:
            - "/bin/sh"
            - "-ce"
            - "/usr/bin/docker-entrypoint.sh minio server {{ $bucketRoot }} -S {{ .Values.certsPath }} --address :{{ .Values.minioAPIPort }} --console-address :{{ .Values.minioConsolePort }} {{- template "minio.extraArgs" . }}"
          volumeMounts:
            - name: minio-user
              mountPath: "/tmp/credentials"
              readOnly: true
            {{- if .Values.persistence.enabled }}
            - name: export
              mountPath: {{ .Values.mountPath }}
              {{- if .Values.persistence.subPath }}
              subPath: "{{ .Values.persistence.subPath }}"
              {{- end }}
            {{- end }}
            {{- if .Values.extraSecret }}
            - name: extra-secret
              mountPath: "/tmp/minio-config-env"
            {{- end }}
            {{- include "minio.tlsKeysVolumeMount" . | indent 12 }}
          ports:
            - name: {{ $scheme }}
              containerPort: {{ .Values.minioAPIPort }}
            - name: {{ $scheme }}-console
              containerPort: {{ .Values.minioConsolePort }}
          env:
            - name: MINIO_ROOT_USER
              valueFrom:
                secretKeyRef:
                  name: {{ template "minio.secretName" . }}
                  key: rootUser
            - name: MINIO_ROOT_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: {{ template "minio.secretName" . }}
                  key: rootPassword
            {{- if .Values.extraSecret }}
            - name: MINIO_CONFIG_ENV_FILE
              value: "/tmp/minio-config-env/config.env"
            {{- end}}
            {{- if .Values.metrics.serviceMonitor.public }}
            - name: MINIO_PROMETHEUS_AUTH_TYPE
              value: "public"
            {{- end}}
            {{- if .Values.etcd.endpoints }}
            - name: MINIO_ETCD_ENDPOINTS
              value: {{ join "," .Values.etcd.endpoints | quote }}
            {{- if .Values.etcd.clientCert }}
            - name: MINIO_ETCD_CLIENT_CERT
              value: "/tmp/credentials/etcd_client_cert.pem"
            {{- end }}
            {{- if .Values.etcd.clientCertKey }}
            - name: MINIO_ETCD_CLIENT_CERT_KEY
              value: "/tmp/credentials/etcd_client_cert_key.pem"
            {{- end }}
            {{- if .Values.etcd.pathPrefix }}
            - name: MINIO_ETCD_PATH_PREFIX
              value: {{ .Values.etcd.pathPrefix }}
            {{- end }}
            {{- if .Values.etcd.corednsPathPrefix }}
            - name: MINIO_ETCD_COREDNS_PATH
              value: {{ .Values.etcd.corednsPathPrefix }}
            {{- end }}
            {{- end }}
            {{- range $key, $val := .Values.environment }}
            - name: {{ $key }}
              value: {{ $val | quote }}
            {{- end}}
          resources:
{{ toYaml .Values.resources | indent 12 }}
{{- with .Values.nodeSelector }}
      nodeSelector:
{{ toYaml . | indent 8 }}
{{- end }}
{{- include "minio.imagePullSecrets" . | indent 6 }}
{{- with .Values.affinity }}
      affinity:
{{ toYaml . | indent 8 }}
{{- end }}
{{- with .Values.tolerations }}
      tolerations:
{{ toYaml . | indent 8 }}
{{- end }}
      volumes:
        - name: export
        {{- if .Values.persistence.enabled }}
          persistentVolumeClaim:
            claimName: {{ .Values.persistence.existingClaim | default (include "minio.fullname" .) }}
        {{- else }}
          emptyDir: {}
        {{- end }}
        {{- if .Values.extraSecret }}
        - name: extra-secret
          secret:
            secretName: {{ .Values.extraSecret }}
        {{- end }}
        - name: minio-user
          secret:
            secretName: {{ template "minio.secretName" . }}
        {{- include "minio.tlsKeysVolume" . | indent 8 }}
{{- end }}
