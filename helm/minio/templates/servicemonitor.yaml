{{- if .Values.metrics.serviceMonitor.enabled }}
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: {{ template "minio.fullname" . }}
  {{- if .Values.metrics.serviceMonitor.namespace }}
  namespace: {{ .Values.metrics.serviceMonitor.namespace }}
  {{ else }}
  namespace: {{ .Release.Namespace | quote }}
  {{- end }}
  labels:
    app: {{ template "minio.name" . }}
    chart: {{ template "minio.chart" . }}
    release: {{ .Release.Name }}
    heritage: {{ .Release.Service }}
    {{- if .Values.metrics.serviceMonitor.additionalLabels }}
{{ toYaml .Values.metrics.serviceMonitor.additionalLabels | indent 4 }}
    {{- end }}
spec:
  endpoints:
    {{- if .Values.tls.enabled }}
    - port: https
      scheme: https
    {{ else }}
    - port: http
      scheme: http
    {{- end }}
      path: /minio/v2/metrics/cluster
      {{- if .Values.metrics.serviceMonitor.interval }}
      interval: {{ .Values.metrics.serviceMonitor.interval }}
      {{- end }}
      {{- if .Values.metrics.serviceMonitor.scrapeTimeout }}
      scrapeTimeout: {{ .Values.metrics.serviceMonitor.scrapeTimeout }}
      {{- end }}
      {{- if .Values.metrics.serviceMonitor.relabelConfigs }}
{{ toYaml .Values.metrics.serviceMonitor.relabelConfigs | indent 6 }}
      {{- end }}
      {{- if not .Values.metrics.serviceMonitor.public }}
      bearerTokenSecret:
        name: {{ template "minio.fullname" . }}-prometheus
        key: token
      {{- end }}
  namespaceSelector:
    matchNames:
      - {{ .Release.Namespace | quote }}
  selector:
    matchLabels:
      app: {{ include "minio.name" . }}
      release: {{ .Release.Name }}
      monitoring: "true"
{{- end }}
