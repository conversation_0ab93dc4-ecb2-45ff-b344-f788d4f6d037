{{- if .Values.buckets }}
apiVersion: batch/v1
kind: Job
metadata:
  name: {{ template "minio.fullname" . }}-make-bucket-job
  namespace: {{ .Release.Namespace | quote }}
  labels:
    app: {{ template "minio.name" . }}-make-bucket-job
    chart: {{ template "minio.chart" . }}
    release: {{ .Release.Name }}
    heritage: {{ .Release.Service }}
  annotations:
    "helm.sh/hook": post-install,post-upgrade
    "helm.sh/hook-delete-policy": hook-succeeded,before-hook-creation
{{- with .Values.makeBucketJob.annotations }}
{{ toYaml . | indent 4 }}
{{- end }}
spec:
  template:
    metadata:
      labels:
        app: {{ template "minio.name" . }}-job
        release: {{ .Release.Name }}
{{- if .Values.podLabels }}
{{ toYaml .Values.podLabels | indent 8 }}
{{- end }}
{{- if .Values.makeBucketJob.podAnnotations }}
      annotations:
{{ toYaml .Values.makeBucketJob.podAnnotations | indent 8 }}
{{- end }}
    spec:
      restartPolicy: OnFailure
{{- include "minio.imagePullSecrets" . | indent 6 }}
{{- if .Values.nodeSelector }}
      nodeSelector:
{{ toYaml .Values.makeBucketJob.nodeSelector | indent 8 }}
{{- end }}
{{- with .Values.makeBucketJob.affinity }}
      affinity:
{{ toYaml . | indent 8 }}
{{- end }}
{{- with .Values.makeBucketJob.tolerations }}
      tolerations:
{{ toYaml . | indent 8 }}
{{- end }}
{{- if .Values.makeBucketJob.securityContext.enabled }}
      securityContext:
        runAsUser: {{ .Values.makeBucketJob.securityContext.runAsUser }}
        runAsGroup: {{ .Values.makeBucketJob.securityContext.runAsGroup }}
        fsGroup: {{ .Values.makeBucketJob.securityContext.fsGroup }}
{{- end }}
      volumes:
        - name: minio-configuration
          projected:
            sources:
            - configMap:
                name: {{ template "minio.fullname" . }}
            - secret:
                name: {{ template "minio.secretName" . }}
        {{- if .Values.tls.enabled }}
        - name: cert-secret-volume-mc
          secret:
            secretName: {{ .Values.tls.certSecret }}
            items:
            - key: {{ .Values.tls.publicCrt }}
              path: CAs/public.crt
        {{ end }}
      containers:
      - name: minio-mc
        image: "{{ .Values.mcImage.repository }}:{{ .Values.mcImage.tag }}"
        imagePullPolicy: {{ .Values.mcImage.pullPolicy }}
        command: ["/bin/sh", "/config/initialize"]
        env:
          - name: MINIO_ENDPOINT
            value: {{ template "minio.fullname" . }}
          - name: MINIO_PORT
            value: {{ .Values.service.port | quote }}
        volumeMounts:
          - name: minio-configuration
            mountPath: /config
          {{- if .Values.tls.enabled }}
          - name: cert-secret-volume-mc
            mountPath: {{ .Values.configPathmc }}certs
          {{ end }}
        resources:
{{ toYaml .Values.makeBucketJob.resources | indent 10 }}
{{- end }}
