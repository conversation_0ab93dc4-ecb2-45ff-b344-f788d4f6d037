apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ template "minio.fullname" . }}
  namespace: {{ .Release.Namespace | quote }}
  labels:
    app: {{ template "minio.name" . }}
    chart: {{ template "minio.chart" . }}
    release: {{ .Release.Name }}
    heritage: {{ .Release.Service }}
data:
  initialize: |-
{{ include (print $.Template.BasePath "/_helper_create_bucket.txt") . | indent 4 }}
  add-user: |-
{{ include (print $.Template.BasePath "/_helper_create_user.txt") . | indent 4 }}
  add-policy: |-
{{ include (print $.Template.BasePath "/_helper_create_policy.txt") . | indent 4 }}
{{- range $idx, $policy := .Values.policies }}
  # {{ $policy.name }}
  policy_{{ $idx }}.json: |-
{{ include (print $.Template.BasePath "/_helper_policy.tpl") . | indent 4 }}
{{ end }}
  custom-command: |-
{{ include (print $.Template.BasePath "/_helper_custom_command.txt") . | indent 4 }}
