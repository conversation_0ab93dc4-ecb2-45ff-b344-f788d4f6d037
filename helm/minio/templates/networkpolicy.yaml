{{- if .Values.networkPolicy.enabled }}
kind: NetworkPolicy
apiVersion: {{ template "minio.networkPolicy.apiVersion" . }}
metadata:
  name: {{ template "minio.fullname" . }}
  namespace: {{ .Release.Namespace | quote }}
  labels:
    app: {{ template "minio.name" . }}
    chart: {{ template "minio.chart" . }}
    release: {{ .Release.Name }}
    heritage: {{ .Release.Service }}
spec:
  podSelector:
    matchLabels:
      app: {{ template "minio.name" . }}
      release: {{ .Release.Name }}
  ingress:
    - ports:
        - port: {{ .Values.service.port }}
      {{- if not .Values.networkPolicy.allowExternal }}
      from:
        - podSelector:
            matchLabels:
              {{ template "minio.name" . }}-client: "true"
      {{- end }}
{{- end }}
