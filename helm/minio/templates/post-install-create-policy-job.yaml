{{- if .Values.policies }}
apiVersion: batch/v1
kind: Job
metadata:
  name: {{ template "minio.fullname" . }}-make-policies-job
  namespace: {{ .Release.Namespace | quote }}
  labels:
    app: {{ template "minio.name" . }}-make-policies-job
    chart: {{ template "minio.chart" . }}
    release: {{ .Release.Name }}
    heritage: {{ .Release.Service }}
  annotations:
    "helm.sh/hook": post-install,post-upgrade
    "helm.sh/hook-delete-policy": hook-succeeded,before-hook-creation
{{- with .Values.makePolicyJob.annotations }}
{{ toYaml . | indent 4 }}
{{- end }}
spec:
  template:
    metadata:
      labels:
        app: {{ template "minio.name" . }}-job
        release: {{ .Release.Name }}
{{- if .Values.podLabels }}
{{ toYaml .Values.podLabels | indent 8 }}
{{- end }}
{{- if .Values.makePolicyJob.podAnnotations }}
      annotations:
{{ toYaml .Values.makePolicyJob.podAnnotations | indent 8 }}
{{- end }}
    spec:
      restartPolicy: OnFailure
{{- include "minio.imagePullSecrets" . | indent 6 }}
{{- if .Values.nodeSelector }}
      nodeSelector:
{{ toYaml .Values.makePolicyJob.nodeSelector | indent 8 }}
{{- end }}
{{- with .Values.makePolicyJob.affinity }}
      affinity:
{{ toYaml . | indent 8 }}
{{- end }}
{{- with .Values.makePolicyJob.tolerations }}
      tolerations:
{{ toYaml . | indent 8 }}
{{- end }}
{{- if .Values.makePolicyJob.securityContext.enabled }}
      securityContext:
        runAsUser: {{ .Values.makePolicyJob.securityContext.runAsUser }}
        runAsGroup: {{ .Values.makePolicyJob.securityContext.runAsGroup }}
        fsGroup: {{ .Values.makePolicyJob.securityContext.fsGroup }}
{{- end }}
      volumes:
        - name: minio-configuration
          projected:
            sources:
            - configMap:
                name: {{ template "minio.fullname" . }}
            - secret:
                name: {{ template "minio.secretName" . }}
        {{- if .Values.tls.enabled }}
        - name: cert-secret-volume-mc
          secret:
            secretName: {{ .Values.tls.certSecret }}
            items:
            - key: {{ .Values.tls.publicCrt }}
              path: CAs/public.crt
        {{ end }}
      containers:
      - name: minio-mc
        image: "{{ .Values.mcImage.repository }}:{{ .Values.mcImage.tag }}"
        imagePullPolicy: {{ .Values.mcImage.pullPolicy }}
        command: ["/bin/sh", "/config/add-policy"]
        env:
          - name: MINIO_ENDPOINT
            value: {{ template "minio.fullname" . }}
          - name: MINIO_PORT
            value: {{ .Values.service.port | quote }}
        volumeMounts:
          - name: minio-configuration
            mountPath: /config
          {{- if .Values.tls.enabled }}
          - name: cert-secret-volume-mc
            mountPath: {{ .Values.configPathmc }}certs
          {{ end }}
        resources:
{{ toYaml .Values.makePolicyJob.resources | indent 10 }}
{{- end }}
