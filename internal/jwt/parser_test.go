// Copyright (c) 2015-2021 MinIO, Inc.
//
// This file is part of MinIO Object Storage stack
//
// This program is free software: you can redistribute it and/or modify
// it under the terms of the GNU Affero General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// This program is distributed in the hope that it will be useful
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU Affero General Public License for more details.
//
// You should have received a copy of the GNU Affero General Public License
// along with this program.  If not, see <http://www.gnu.org/licenses/>.

package jwt

// This file is a re-implementation of the original code here with some
// additional allocation tweaks reproduced using GODEBUG=allocfreetrace=1
// original file https://github.com/golang-jwt/jwt/blob/main/parser.go
// borrowed under MIT License https://github.com/golang-jwt/jwt/blob/main/LICENSE

import (
	"fmt"
	"testing"
	"time"

	"github.com/golang-jwt/jwt/v4"
)

var (
	defaultKeyFunc = func(claim *MapClaims) ([]byte, error) { return []byte("HelloSecret"), nil }
	emptyKeyFunc   = func(claim *MapClaims) ([]byte, error) { return nil, nil }
	errorKeyFunc   = func(claim *MapClaims) ([]byte, error) { return nil, fmt.Errorf("error loading key") }
)

var jwtTestData = []struct {
	name        string
	tokenString string
	keyfunc     func(*MapClaims) ([]byte, error)
	claims      jwt.Claims
	valid       bool
	errors      int32
}{
	{
		"basic",
		"",
		defaultKeyFunc,
		&MapClaims{
			MapClaims: jwt.MapClaims{
				"foo": "bar",
			},
		},
		true,
		0,
	},
	{
		"basic expired",
		"", // autogen
		defaultKeyFunc,
		&MapClaims{
			MapClaims: jwt.MapClaims{
				"foo": "bar",
				"exp": float64(time.Now().Unix() - 100),
			},
		},
		false,
		-1,
	},
	{
		"basic nbf",
		"", // autogen
		defaultKeyFunc,
		&MapClaims{
			MapClaims: jwt.MapClaims{
				"foo": "bar",
				"nbf": float64(time.Now().Unix() + 100),
			},
		},
		false,
		-1,
	},
	{
		"expired and nbf",
		"", // autogen
		defaultKeyFunc,
		&MapClaims{
			MapClaims: jwt.MapClaims{
				"foo": "bar",
				"nbf": float64(time.Now().Unix() + 100),
				"exp": float64(time.Now().Unix() - 100),
			},
		},
		false,
		-1,
	},
	{
		"basic invalid",
		"**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
		defaultKeyFunc,
		&MapClaims{
			MapClaims: jwt.MapClaims{
				"foo": "bar",
			},
		},
		false,
		-1,
	},
	{
		"basic nokeyfunc",
		"**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
		nil,
		&MapClaims{
			MapClaims: jwt.MapClaims{
				"foo": "bar",
			},
		},
		false,
		-1,
	},
	{
		"basic nokey",
		"**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
		emptyKeyFunc,
		&MapClaims{
			MapClaims: jwt.MapClaims{
				"foo": "bar",
			},
		},
		false,
		-1,
	},
	{
		"basic errorkey",
		"**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
		errorKeyFunc,
		&MapClaims{
			MapClaims: jwt.MapClaims{
				"foo": "bar",
			},
		},
		false,
		-1,
	},
	{
		"Standard Claims",
		"",
		defaultKeyFunc,
		&StandardClaims{
			StandardClaims: jwt.StandardClaims{
				ExpiresAt: time.Now().Add(time.Second * 10).Unix(),
			},
		},
		true,
		0,
	},
}

func mapClaimsToken(claims *MapClaims) string {
	claims.SetAccessKey("test")
	j := jwt.NewWithClaims(jwt.SigningMethodHS512, claims)
	tk, _ := j.SignedString([]byte("HelloSecret"))
	return tk
}

func standardClaimsToken(claims *StandardClaims) string {
	claims.AccessKey = "test"
	claims.Subject = "test"
	j := jwt.NewWithClaims(jwt.SigningMethodHS512, claims)
	tk, _ := j.SignedString([]byte("HelloSecret"))
	return tk
}

func TestParserParse(t *testing.T) {
	// Iterate over test data set and run tests
	for _, data := range jwtTestData {
		data := data
		t.Run(data.name, func(t *testing.T) {
			// Parse the token
			var err error

			// Figure out correct claims type
			switch claims := data.claims.(type) {
			case *MapClaims:
				if data.tokenString == "" {
					data.tokenString = mapClaimsToken(claims)
				}
				err = ParseWithClaims(data.tokenString, &MapClaims{}, data.keyfunc)
			case *StandardClaims:
				if data.tokenString == "" {
					data.tokenString = standardClaimsToken(claims)
				}
				err = ParseWithStandardClaims(data.tokenString, &StandardClaims{}, []byte("HelloSecret"))
			}

			if data.valid && err != nil {
				t.Errorf("Error while verifying token: %T:%v", err, err)
			}

			if !data.valid && err == nil {
				t.Errorf("Invalid token passed validation")
			}

			if data.errors != 0 {
				_, ok := err.(*jwt.ValidationError)
				if !ok {
					t.Errorf("Expected *jwt.ValidationError, but got %#v instead", err)
				}
			}
		})
	}
}
