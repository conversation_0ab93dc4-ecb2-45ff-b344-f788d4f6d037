package dsync

// Code generated by github.com/tinylib/msgp DO NOT EDIT.

import (
	"github.com/tinylib/msgp/msgp"
)

// DecodeMsg implements msgp.Decodable
func (z *LockArgs) DecodeMsg(dc *msgp.Reader) (err error) {
	var field []byte
	_ = field
	var zb0001 uint32
	zb0001, err = dc.ReadMapHeader()
	if err != nil {
		err = msgp.WrapError(err)
		return
	}
	for zb0001 > 0 {
		zb0001--
		field, err = dc.ReadMapKeyPtr()
		if err != nil {
			err = msgp.WrapError(err)
			return
		}
		switch msgp.UnsafeString(field) {
		case "UID":
			z.UID, err = dc.ReadString()
			if err != nil {
				err = msgp.WrapError(err, "UID")
				return
			}
		case "Resources":
			var zb0002 uint32
			zb0002, err = dc.ReadArrayHeader()
			if err != nil {
				err = msgp.WrapError(err, "Resources")
				return
			}
			if cap(z.Resources) >= int(zb0002) {
				z.Resources = (z.Resources)[:zb0002]
			} else {
				z.Resources = make([]string, zb0002)
			}
			for za0001 := range z.Resources {
				z.Resources[za0001], err = dc.ReadString()
				if err != nil {
					err = msgp.WrapError(err, "Resources", za0001)
					return
				}
			}
		case "Source":
			z.Source, err = dc.ReadString()
			if err != nil {
				err = msgp.WrapError(err, "Source")
				return
			}
		case "Owner":
			z.Owner, err = dc.ReadString()
			if err != nil {
				err = msgp.WrapError(err, "Owner")
				return
			}
		case "Quorum":
			z.Quorum, err = dc.ReadInt()
			if err != nil {
				err = msgp.WrapError(err, "Quorum")
				return
			}
		default:
			err = dc.Skip()
			if err != nil {
				err = msgp.WrapError(err)
				return
			}
		}
	}
	return
}

// EncodeMsg implements msgp.Encodable
func (z *LockArgs) EncodeMsg(en *msgp.Writer) (err error) {
	// map header, size 5
	// write "UID"
	err = en.Append(0x85, 0xa3, 0x55, 0x49, 0x44)
	if err != nil {
		return
	}
	err = en.WriteString(z.UID)
	if err != nil {
		err = msgp.WrapError(err, "UID")
		return
	}
	// write "Resources"
	err = en.Append(0xa9, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x73)
	if err != nil {
		return
	}
	err = en.WriteArrayHeader(uint32(len(z.Resources)))
	if err != nil {
		err = msgp.WrapError(err, "Resources")
		return
	}
	for za0001 := range z.Resources {
		err = en.WriteString(z.Resources[za0001])
		if err != nil {
			err = msgp.WrapError(err, "Resources", za0001)
			return
		}
	}
	// write "Source"
	err = en.Append(0xa6, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65)
	if err != nil {
		return
	}
	err = en.WriteString(z.Source)
	if err != nil {
		err = msgp.WrapError(err, "Source")
		return
	}
	// write "Owner"
	err = en.Append(0xa5, 0x4f, 0x77, 0x6e, 0x65, 0x72)
	if err != nil {
		return
	}
	err = en.WriteString(z.Owner)
	if err != nil {
		err = msgp.WrapError(err, "Owner")
		return
	}
	// write "Quorum"
	err = en.Append(0xa6, 0x51, 0x75, 0x6f, 0x72, 0x75, 0x6d)
	if err != nil {
		return
	}
	err = en.WriteInt(z.Quorum)
	if err != nil {
		err = msgp.WrapError(err, "Quorum")
		return
	}
	return
}

// MarshalMsg implements msgp.Marshaler
func (z *LockArgs) MarshalMsg(b []byte) (o []byte, err error) {
	o = msgp.Require(b, z.Msgsize())
	// map header, size 5
	// string "UID"
	o = append(o, 0x85, 0xa3, 0x55, 0x49, 0x44)
	o = msgp.AppendString(o, z.UID)
	// string "Resources"
	o = append(o, 0xa9, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x73)
	o = msgp.AppendArrayHeader(o, uint32(len(z.Resources)))
	for za0001 := range z.Resources {
		o = msgp.AppendString(o, z.Resources[za0001])
	}
	// string "Source"
	o = append(o, 0xa6, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65)
	o = msgp.AppendString(o, z.Source)
	// string "Owner"
	o = append(o, 0xa5, 0x4f, 0x77, 0x6e, 0x65, 0x72)
	o = msgp.AppendString(o, z.Owner)
	// string "Quorum"
	o = append(o, 0xa6, 0x51, 0x75, 0x6f, 0x72, 0x75, 0x6d)
	o = msgp.AppendInt(o, z.Quorum)
	return
}

// UnmarshalMsg implements msgp.Unmarshaler
func (z *LockArgs) UnmarshalMsg(bts []byte) (o []byte, err error) {
	var field []byte
	_ = field
	var zb0001 uint32
	zb0001, bts, err = msgp.ReadMapHeaderBytes(bts)
	if err != nil {
		err = msgp.WrapError(err)
		return
	}
	for zb0001 > 0 {
		zb0001--
		field, bts, err = msgp.ReadMapKeyZC(bts)
		if err != nil {
			err = msgp.WrapError(err)
			return
		}
		switch msgp.UnsafeString(field) {
		case "UID":
			z.UID, bts, err = msgp.ReadStringBytes(bts)
			if err != nil {
				err = msgp.WrapError(err, "UID")
				return
			}
		case "Resources":
			var zb0002 uint32
			zb0002, bts, err = msgp.ReadArrayHeaderBytes(bts)
			if err != nil {
				err = msgp.WrapError(err, "Resources")
				return
			}
			if cap(z.Resources) >= int(zb0002) {
				z.Resources = (z.Resources)[:zb0002]
			} else {
				z.Resources = make([]string, zb0002)
			}
			for za0001 := range z.Resources {
				z.Resources[za0001], bts, err = msgp.ReadStringBytes(bts)
				if err != nil {
					err = msgp.WrapError(err, "Resources", za0001)
					return
				}
			}
		case "Source":
			z.Source, bts, err = msgp.ReadStringBytes(bts)
			if err != nil {
				err = msgp.WrapError(err, "Source")
				return
			}
		case "Owner":
			z.Owner, bts, err = msgp.ReadStringBytes(bts)
			if err != nil {
				err = msgp.WrapError(err, "Owner")
				return
			}
		case "Quorum":
			z.Quorum, bts, err = msgp.ReadIntBytes(bts)
			if err != nil {
				err = msgp.WrapError(err, "Quorum")
				return
			}
		default:
			bts, err = msgp.Skip(bts)
			if err != nil {
				err = msgp.WrapError(err)
				return
			}
		}
	}
	o = bts
	return
}

// Msgsize returns an upper bound estimate of the number of bytes occupied by the serialized message
func (z *LockArgs) Msgsize() (s int) {
	s = 1 + 4 + msgp.StringPrefixSize + len(z.UID) + 10 + msgp.ArrayHeaderSize
	for za0001 := range z.Resources {
		s += msgp.StringPrefixSize + len(z.Resources[za0001])
	}
	s += 7 + msgp.StringPrefixSize + len(z.Source) + 6 + msgp.StringPrefixSize + len(z.Owner) + 7 + msgp.IntSize
	return
}
