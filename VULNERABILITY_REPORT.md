# Vulnerability Management Policy

This document formally describes the process of addressing and managing a
reported vulnerability that has been found in the MinIO server code base,
any directly connected ecosystem component or a direct / indirect dependency
of the code base.

## Scope

The vulnerability management policy described in this document covers the
process of investigating, assessing and resolving a vulnerability report
opened by a MinIO employee or an external third party.

Therefore, it lists pre-conditions and actions that should be performed to
resolve and fix a reported vulnerability.

## Vulnerability Management Process

The vulnerability management process requires that the vulnerability report
contains the following information:

- The project / component that contains the reported vulnerability.
- A description of the vulnerability. In particular, the type of the
   reported vulnerability and how it might be exploited. Alternatively,
   a well-established vulnerability identifier, e.g. CVE number, can be
   used instead.

Based on the description mentioned above, a MinIO engineer or security team
member investigates:

- Whether the reported vulnerability exists.
- The conditions that are required such that the vulnerability can be exploited.
- The steps required to fix the vulnerability.

In general, if the vulnerability exists in one of the MinIO code bases
itself - not in a code dependency - then MinIO will, if possible, fix
the vulnerability or implement reasonable countermeasures such that the
vulnerability cannot be exploited anymore.
