name: Crosscompile

on:
  pull_request:
    branches:
    - master

# This ensures that previous jobs for the PR are canceled when the PR is
# updated.
concurrency: 
  group: ${{ github.workflow }}-${{ github.head_ref }}
  cancel-in-progress: true

jobs:
  build:
    name: Build Tests with Go ${{ matrix.go-version }} on ${{ matrix.os }}
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        go-version: [1.17.x]
        os: [ubuntu-20.04]
    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-go@v2
        with:
          go-version: ${{ matrix.go-version }}
      - name: Build on ${{ matrix.os }}
        if: matrix.os == 'ubuntu-20.04'
        env:
          CGO_ENABLED: 0
          GO111MODULE: on
        run: |
          sudo sysctl net.ipv6.conf.all.disable_ipv6=0
          sudo sysctl net.ipv6.conf.default.disable_ipv6=0
          make crosscompile
