name: Upgrade old version tests

on:
  pull_request:
    branches:
    - master

# This ensures that previous jobs for the PR are canceled when the PR is
# updated.
concurrency:
  group: ${{ github.workflow }}-${{ github.head_ref }}
  cancel-in-progress: true

jobs:
  build:
    name: Go ${{ matrix.go-version }} on ${{ matrix.os }}
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        go-version: [1.17.x]
        os: [ubuntu-20.04]

    steps:
      - uses: actions/checkout@v1
      - uses: actions/setup-go@v2
        with:
          go-version: ${{ matrix.go-version }}

      - name: Start upgrade tests
        run: |
          make test-upgrade
