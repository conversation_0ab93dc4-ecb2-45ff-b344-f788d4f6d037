name: Multi-site replication tests

on:
  pull_request:
    branches:
    - master

# This ensures that previous jobs for the PR are canceled when the PR is
# updated.
concurrency:
  group: ${{ github.workflow }}-${{ github.head_ref }}
  cancel-in-progress: true

jobs:
  replication-test:
    name: Replication Tests with Go ${{ matrix.go-version }}
    runs-on: ubuntu-20.04

    strategy:
      matrix:
        go-version: [1.17.x]

    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-go@v2
        with:
          go-version: ${{ matrix.go-version }}
      - uses: actions/cache@v2
        with:
          path: |
            ~/.cache/go-build
            ~/go/pkg/mod
          key: ${{ runner.os }}-${{ matrix.go-version }}-go-${{ hashFiles('**/go.sum') }}
          restore-keys: |
            ${{ runner.os }}-${{ matrix.go-version }}-go-
      - name: Test Replication
        run: |
          sudo sysctl net.ipv6.conf.all.disable_ipv6=0
          sudo sysctl net.ipv6.conf.default.disable_ipv6=0
          make test-replication
      - name: Test MinIO IDP for automatic site replication
        run: |
          sudo sysctl net.ipv6.conf.all.disable_ipv6=0
          sudo sysctl net.ipv6.conf.default.disable_ipv6=0
          make test-site-replication-minio

