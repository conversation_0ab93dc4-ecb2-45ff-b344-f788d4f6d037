name: Functional Tests

on:
  pull_request:
    branches:
    - master

# This ensures that previous jobs for the PR are canceled when the PR is
# updated.
concurrency:
  group: ${{ github.workflow }}-${{ github.head_ref }}
  cancel-in-progress: true

jobs:
  build:
    name: Go ${{ matrix.go-version }} on ${{ matrix.os }} - healing
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        go-version: [1.17.x]
        os: [ubuntu-20.04]
    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-go@v2
        with:
          go-version: ${{ matrix.go-version }}
      - uses: actions/cache@v2
        with:
          path: |
            ~/.cache/go-build
            ~/go/pkg/mod
          key: ${{ runner.os }}-${{ matrix.go-version }}-go-${{ hashFiles('**/go.sum') }}
          restore-keys: |
            ${{ runner.os }}-${{ matrix.go-version }}-go-
      - name: Build on ${{ matrix.os }}
        if: matrix.os == 'ubuntu-20.04'
        env:
          CGO_ENABLED: 0
          GO111MODULE: on
          MINIO_KMS_KES_CERT_FILE: /home/<USER>/work/minio/minio/.github/workflows/root.cert
          MINIO_KMS_KES_KEY_FILE: /home/<USER>/work/minio/minio/.github/workflows/root.key
          MINIO_KMS_KES_ENDPOINT: "https://play.min.io:7373"
          MINIO_KMS_KES_KEY_NAME: "my-minio-key"
          MINIO_KMS_AUTO_ENCRYPTION: on
        run: |
          sudo sysctl net.ipv6.conf.all.disable_ipv6=0
          sudo sysctl net.ipv6.conf.default.disable_ipv6=0
          make verify
