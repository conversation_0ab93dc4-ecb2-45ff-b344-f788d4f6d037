name: Linters and Tests

on:
  pull_request:
    branches:
    - master

# This ensures that previous jobs for the PR are canceled when the PR is
# updated.
concurrency: 
  group: ${{ github.workflow }}-${{ github.head_ref }}
  cancel-in-progress: true

jobs:
  build:
    name: Go ${{ matrix.go-version }} on ${{ matrix.os }}
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        go-version: [1.17.x]
        os: [ubuntu-20.04, windows-latest]
    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-go@v2
        with:
          go-version: ${{ matrix.go-version }}
      - uses: actions/cache@v2
        if: matrix.os == 'ubuntu-20.04'
        with:
          path: |
            ~/.cache/go-build
            ~/go/pkg/mod
          key: ${{ runner.os }}-${{ matrix.go-version }}-go-${{ hashFiles('**/go.sum') }}
          restore-keys: |
            ${{ runner.os }}-${{ matrix.go-version }}-go-
      - uses: actions/cache@v2
        if: matrix.os == 'windows-latest'
        with:
          path: |
            %LocalAppData%\go-build
            ~/go/pkg/mod
          key: ${{ runner.os }}-${{ matrix.go-version }}-go-${{ hashFiles('**/go.sum') }}
          restore-keys: |
            ${{ runner.os }}-${{ matrix.go-version }}-go-
      - name: Build on ${{ matrix.os }}
        if: matrix.os == 'windows-latest'
        env:
          CGO_ENABLED: 0
          GO111MODULE: on
        run: |
          go build --ldflags="-s -w" -o %GOPATH%\bin\minio.exe
          go test -v --timeout 50m ./...
      - name: Build on ${{ matrix.os }}
        if: matrix.os == 'ubuntu-20.04'
        env:
          CGO_ENABLED: 0
          GO111MODULE: on
        run: |
          sudo apt install jq -y
          sudo sysctl net.ipv6.conf.all.disable_ipv6=0
          sudo sysctl net.ipv6.conf.default.disable_ipv6=0
          nancy_version=$(curl --retry 10 -Ls -o /dev/null -w "%{url_effective}" https://github.com/sonatype-nexus-community/nancy/releases/latest | sed "s/https:\/\/github.com\/sonatype-nexus-community\/nancy\/releases\/tag\///")
          curl -L -o nancy https://github.com/sonatype-nexus-community/nancy/releases/download/${nancy_version}/nancy-${nancy_version}-linux-amd64 && chmod +x nancy
          go list -deps -json ./... | jq -s 'unique_by(.Module.Path)|.[]|select(has("Module"))|.Module' | ./nancy sleuth
          make
          make test
          make test-race
