name: <PERSON><PERSON> <PERSON><PERSON>

on:
  pull_request:
    branches:
    - master

# This ensures that previous jobs for the PR are canceled when the PR is
# updated.
concurrency: 
  group: ${{ github.workflow }}-${{ github.head_ref }}
  cancel-in-progress: true

jobs:
  lint:
    name: Lint all docs
    runs-on: ubuntu-20.04
    steps:
    - name: Check out code
      uses: actions/checkout@v2

    - name: Lint all docs
      run: |
        npm install -g markdownlint-cli
        markdownlint --fix '**/*.md' --disable MD013 MD040
