name: 'Lock Threads'

on:
  schedule:
    - cron: '0 0 * * *'
  workflow_dispatch:

permissions:
  issues: write

concurrency:
  group: lock

jobs:
  action:
    runs-on: ubuntu-20.04
    steps:
      - uses: dessant/lock-threads@v3
        with:
          github-token: ${{ github.token }}
          issue-inactive-days: '365'
          exclude-any-issue-labels: 'do-not-close'
          issue-lock-reason: 'resolved'
          log-output: true
