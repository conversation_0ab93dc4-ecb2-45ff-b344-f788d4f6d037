linters-settings:
  golint:
    min-confidence: 0

  misspell:
    locale: US

linters:
  disable-all: true
  enable:
    - typecheck
    - goimports
    - misspell
    - govet
    - revive
    - ineffassign
    - gosimple
    - deadcode
    - structcheck
    - gomodguard
    - gofmt
    - unused
    - structcheck
    - unconvert
    - varcheck
    - gocritic
    - gofumpt

linters-settings:
  gofumpt:
    lang-version: "1.17"

    # Choose whether or not to use the extra rules that are disabled
    # by default
    extra-rules: false

issues:
  exclude-use-default: false
  exclude:
      - should have a package comment
      - error strings should not be capitalized or end with punctuation or a newline
      # todo fix these when we get enough time.
      - "singleCaseSwitch: should rewrite switch statement to if statement"
      - "unlambda: replace"
      - "captLocal:"
      - "ifElse<PERSON>hain:"
      - "elseif:"

service:
  golangci-lint-version: 1.43.0 # use the fixed version to not introduce new linters unexpectedly
