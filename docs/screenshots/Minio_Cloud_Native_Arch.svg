<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 21.0.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
	 viewBox="0 0 1165.51 600" style="enable-background:new 0 0 1165.51 600;" xml:space="preserve">
<style type="text/css">
	.st0{fill:#FFFFFF;}
	.st1{fill:#343435;}
	.st2{fill:#494949;}
	.st3{fill:#C4C4C4;}
	.st4{fill:#21211F;}
	.st5{fill:#E6E6E6;}
	.st6{opacity:0.56;fill:url(#SVGID_1_);}
	.st7{fill:#39B54A;}
	.st8{opacity:0.56;fill:url(#SVGID_2_);}
	.st9{opacity:0.56;fill:url(#SVGID_3_);}
	.st10{opacity:0.56;fill:url(#SVGID_4_);}
	.st11{opacity:0.56;fill:url(#SVGID_5_);}
	.st12{opacity:0.56;fill:url(#SVGID_6_);}
	.st13{opacity:0.56;fill:url(#SVGID_7_);}
	.st14{opacity:0.56;fill:url(#SVGID_8_);}
	.st15{opacity:0.56;fill:url(#SVGID_9_);}
	.st16{opacity:0.56;fill:url(#SVGID_10_);}
	.st17{opacity:0.56;fill:url(#SVGID_11_);}
	.st18{opacity:0.56;fill:url(#SVGID_12_);}
	.st19{opacity:0.56;fill:url(#SVGID_13_);}
	.st20{opacity:0.56;fill:url(#SVGID_14_);}
	.st21{opacity:0.56;fill:url(#SVGID_15_);}
	.st22{opacity:0.56;fill:url(#SVGID_16_);}
	.st23{opacity:0.56;fill:url(#SVGID_17_);}
	.st24{opacity:0.56;fill:url(#SVGID_18_);}
	.st25{opacity:0.56;fill:url(#SVGID_19_);}
	.st26{opacity:0.56;fill:url(#SVGID_20_);}
	.st27{opacity:0.56;fill:url(#SVGID_21_);}
	.st28{opacity:0.56;fill:url(#SVGID_22_);}
	.st29{opacity:0.56;fill:url(#SVGID_23_);}
	.st30{opacity:0.56;fill:url(#SVGID_24_);}
	.st31{opacity:0.56;fill:url(#SVGID_25_);}
	.st32{opacity:0.56;fill:url(#SVGID_26_);}
	.st33{opacity:0.56;fill:url(#SVGID_27_);}
	.st34{opacity:0.56;fill:url(#SVGID_28_);}
	.st35{opacity:0.56;fill:url(#SVGID_29_);}
	.st36{opacity:0.56;fill:url(#SVGID_30_);}
	.st37{opacity:0.56;fill:url(#SVGID_31_);}
	.st38{opacity:0.56;fill:url(#SVGID_32_);}
	.st39{opacity:0.56;fill:url(#SVGID_33_);}
	.st40{fill:none;stroke:#000000;stroke-miterlimit:10;}
	.st41{fill:none;stroke:#CCCCCC;stroke-width:2;stroke-miterlimit:10;}
	.st42{fill:#CCCCCC;}
	.st43{fill:none;stroke:#666666;stroke-width:2;stroke-miterlimit:10;}
	.st44{fill:#666666;}
	.st45{fill:none;stroke:#EF524F;stroke-width:2;stroke-miterlimit:10;stroke-dasharray:12,12;}
	.st46{fill:#EF524F;}
	.st47{fill:#62757C;}
	.st48{fill:#23363A;}
	.st49{fill:#FEFBF6;}
	.st50{fill:#F7F7F7;stroke:#23363A;stroke-width:6;stroke-miterlimit:10;}
	.st51{fill:none;stroke:#23363A;stroke-width:3;stroke-miterlimit:10;}
	.st52{fill:#F7F7F7;}
	.st53{fill:#23363A;stroke:#F7F7F7;stroke-width:2;stroke-miterlimit:10;}
	.st54{fill:#37474F;}
	.st55{fill:#EDEAE2;}
	.st56{fill:#4C626D;}
	.st57{fill:#E8EDEF;}
	.st58{fill:#515151;}
	.st59{fill:#939393;}
	.st60{opacity:0.56;fill:url(#SVGID_34_);}
	.st61{opacity:0.56;fill:url(#SVGID_35_);}
	.st62{opacity:0.56;fill:url(#SVGID_36_);}
	.st63{opacity:0.56;fill:url(#SVGID_37_);}
	.st64{opacity:0.56;fill:url(#SVGID_38_);}
	.st65{opacity:0.56;fill:url(#SVGID_39_);}
	.st66{opacity:0.56;fill:url(#SVGID_40_);}
	.st67{opacity:0.56;fill:url(#SVGID_41_);}
	.st68{opacity:0.56;fill:url(#SVGID_42_);}
	.st69{opacity:0.56;fill:url(#SVGID_43_);}
	.st70{opacity:0.56;fill:url(#SVGID_44_);}
	.st71{opacity:0.56;fill:url(#SVGID_45_);}
	.st72{opacity:0.56;fill:url(#SVGID_46_);}
	.st73{opacity:0.56;fill:url(#SVGID_47_);}
	.st74{opacity:0.56;fill:url(#SVGID_48_);}
	.st75{opacity:0.56;fill:url(#SVGID_49_);}
	.st76{opacity:0.56;fill:url(#SVGID_50_);}
	.st77{opacity:0.56;fill:url(#SVGID_51_);}
	.st78{opacity:0.56;fill:url(#SVGID_52_);}
	.st79{opacity:0.56;fill:url(#SVGID_53_);}
	.st80{opacity:0.56;fill:url(#SVGID_54_);}
	.st81{opacity:0.56;fill:url(#SVGID_55_);}
	.st82{opacity:0.56;fill:url(#SVGID_56_);}
	.st83{opacity:0.56;fill:url(#SVGID_57_);}
	.st84{opacity:0.56;fill:url(#SVGID_58_);}
	.st85{opacity:0.56;fill:url(#SVGID_59_);}
	.st86{opacity:0.56;fill:url(#SVGID_60_);}
	.st87{opacity:0.56;fill:url(#SVGID_61_);}
	.st88{opacity:0.56;fill:url(#SVGID_62_);}
	.st89{opacity:0.56;fill:url(#SVGID_63_);}
	.st90{opacity:0.56;fill:url(#SVGID_64_);}
	.st91{opacity:0.56;fill:url(#SVGID_65_);}
	.st92{opacity:0.56;fill:url(#SVGID_66_);}
	.st93{fill:none;stroke:#898989;stroke-miterlimit:10;}
	.st94{fill:#898989;}
	.st95{fill:none;stroke:#666666;stroke-miterlimit:10;}
	.st96{fill:none;stroke:#EF524F;stroke-miterlimit:10;}
	.st97{fill:none;stroke:#EF524F;stroke-miterlimit:10;stroke-dasharray:12.0065,12.0065;}
	.st98{fill:#B2B2B2;}
	.st99{fill:#5A5C5E;}
	.st100{fill:#DBDBDB;}
	.st101{fill:#6A6B6B;}
	.st102{fill:none;stroke:#EF524F;stroke-miterlimit:10;stroke-dasharray:11.6391,11.6391;}
	.st103{fill:none;stroke:#EF524F;stroke-miterlimit:10;stroke-dasharray:12.7731,12.7731;}
	.st104{fill:none;stroke:#EF524F;stroke-miterlimit:10;stroke-dasharray:11.9372,11.9372;}
	.st105{opacity:0.56;fill:url(#SVGID_67_);}
	.st106{opacity:0.56;fill:url(#SVGID_68_);}
	.st107{opacity:0.56;fill:url(#SVGID_69_);}
	.st108{opacity:0.56;fill:url(#SVGID_70_);}
	.st109{opacity:0.56;fill:url(#SVGID_71_);}
	.st110{opacity:0.56;fill:url(#SVGID_72_);}
	.st111{opacity:0.56;fill:url(#SVGID_73_);}
	.st112{opacity:0.56;fill:url(#SVGID_74_);}
	.st113{opacity:0.56;fill:url(#SVGID_75_);}
	.st114{opacity:0.56;fill:url(#SVGID_76_);}
	.st115{opacity:0.56;fill:url(#SVGID_77_);}
	.st116{opacity:0.56;fill:url(#SVGID_78_);}
	.st117{opacity:0.56;fill:url(#SVGID_79_);}
	.st118{opacity:0.56;fill:url(#SVGID_80_);}
	.st119{opacity:0.56;fill:url(#SVGID_81_);}
	.st120{opacity:0.56;fill:url(#SVGID_82_);}
	.st121{opacity:0.56;fill:url(#SVGID_83_);}
	.st122{opacity:0.56;fill:url(#SVGID_84_);}
	.st123{opacity:0.56;fill:url(#SVGID_85_);}
	.st124{opacity:0.56;fill:url(#SVGID_86_);}
	.st125{opacity:0.56;fill:url(#SVGID_87_);}
	.st126{opacity:0.56;fill:url(#SVGID_88_);}
	.st127{opacity:0.56;fill:url(#SVGID_89_);}
	.st128{opacity:0.56;fill:url(#SVGID_90_);}
	.st129{opacity:0.56;fill:url(#SVGID_91_);}
	.st130{opacity:0.56;fill:url(#SVGID_92_);}
	.st131{opacity:0.56;fill:url(#SVGID_93_);}
	.st132{opacity:0.56;fill:url(#SVGID_94_);}
	.st133{opacity:0.56;fill:url(#SVGID_95_);}
	.st134{opacity:0.56;fill:url(#SVGID_96_);}
	.st135{opacity:0.56;fill:url(#SVGID_97_);}
	.st136{opacity:0.56;fill:url(#SVGID_98_);}
	.st137{opacity:0.56;fill:url(#SVGID_99_);}
	.st138{fill:none;stroke:#CCCCCC;stroke-width:1.778;stroke-miterlimit:10;}
	.st139{fill:none;stroke:#EF524F;stroke-width:1.778;stroke-miterlimit:10;stroke-dasharray:10.6678,10.6678;}
	.st140{fill:#E5E5E5;}
	.st141{fill:none;stroke:#666666;stroke-width:1.778;stroke-miterlimit:10;}
	.st142{fill:#F47B67;}
	.st143{fill:none;stroke:#EAEAEA;stroke-width:0.8726;stroke-miterlimit:10;}
	.st144{fill:#727272;}
	.st145{fill:none;stroke:#EAEAEA;stroke-width:0.75;stroke-miterlimit:10;}
	.st146{fill:#FFFFFF;stroke:#9B9B9B;stroke-miterlimit:10;}
	.st147{fill:none;stroke:#9B9B9B;stroke-miterlimit:10;}
	.st148{fill:#9B9B9B;}
	.st149{opacity:0.56;fill:url(#SVGID_100_);}
	.st150{opacity:0.56;fill:url(#SVGID_101_);}
	.st151{opacity:0.56;fill:url(#SVGID_102_);}
	.st152{opacity:0.56;fill:url(#SVGID_103_);}
	.st153{opacity:0.56;fill:url(#SVGID_104_);}
	.st154{opacity:0.56;fill:url(#SVGID_105_);}
	.st155{opacity:0.56;fill:url(#SVGID_106_);}
	.st156{opacity:0.56;fill:url(#SVGID_107_);}
	.st157{opacity:0.56;fill:url(#SVGID_108_);}
	.st158{opacity:0.56;fill:url(#SVGID_109_);}
	.st159{opacity:0.56;fill:url(#SVGID_110_);}
	.st160{opacity:0.56;fill:url(#SVGID_111_);}
	.st161{opacity:0.56;fill:url(#SVGID_112_);}
	.st162{opacity:0.56;fill:url(#SVGID_113_);}
	.st163{opacity:0.56;fill:url(#SVGID_114_);}
	.st164{opacity:0.56;fill:url(#SVGID_115_);}
	.st165{opacity:0.56;fill:url(#SVGID_116_);}
	.st166{opacity:0.56;fill:url(#SVGID_117_);}
	.st167{opacity:0.56;fill:url(#SVGID_118_);}
	.st168{opacity:0.56;fill:url(#SVGID_119_);}
	.st169{opacity:0.56;fill:url(#SVGID_120_);}
	.st170{opacity:0.56;fill:url(#SVGID_121_);}
	.st171{opacity:0.56;fill:url(#SVGID_122_);}
	.st172{opacity:0.56;fill:url(#SVGID_123_);}
	.st173{opacity:0.56;fill:url(#SVGID_124_);}
	.st174{opacity:0.56;fill:url(#SVGID_125_);}
	.st175{opacity:0.56;fill:url(#SVGID_126_);}
	.st176{opacity:0.56;fill:url(#SVGID_127_);}
	.st177{opacity:0.56;fill:url(#SVGID_128_);}
	.st178{opacity:0.56;fill:url(#SVGID_129_);}
	.st179{opacity:0.56;fill:url(#SVGID_130_);}
	.st180{opacity:0.56;fill:url(#SVGID_131_);}
	.st181{opacity:0.56;fill:url(#SVGID_132_);}
	.st182{fill:#E8E8E8;stroke:#C6C6C6;stroke-width:1.0826;stroke-miterlimit:10;}
	.st183{fill:#F15A24;}
	.st184{fill:#FFFFFF;stroke:#C6C6C6;stroke-width:1.0826;stroke-miterlimit:10;}
	.st185{fill:#636363;}
	.st186{fill:none;stroke:#636363;stroke-miterlimit:10;}
	.st187{fill:none;stroke:#EF524F;stroke-miterlimit:10;stroke-dasharray:12,12;}
	.st188{fill:none;stroke:#636363;stroke-width:0.6802;stroke-miterlimit:10;}
	.st189{fill:#FFFFFF;stroke:#636363;stroke-width:0.6802;stroke-miterlimit:10;}
	.st190{fill:none;stroke:#EF524F;stroke-miterlimit:10;stroke-dasharray:11.4666,11.4666;}
	.st191{fill:#E8E8E8;stroke:#898989;stroke-width:0.5488;stroke-miterlimit:10;}
	.st192{fill:#FFFFFF;stroke:#898989;stroke-width:0.5488;stroke-miterlimit:10;}
	.st193{fill:#22B573;}
	.st194{fill:#E8E8E8;}
	.st195{fill:#A8A8A8;}
	.st196{fill:#777777;}
	.st197{fill:#4C4C4C;}
	.st198{fill:none;stroke:#3F3F3F;stroke-miterlimit:10;}
	.st199{fill:#3F3F3F;}
	.st200{fill:none;stroke:#5B5B5B;stroke-miterlimit:10;}
	.st201{fill:#5B5B5B;}
	.st202{fill:none;stroke:#F47B67;stroke-miterlimit:10;}
	.st203{fill:#E8E8E8;stroke:#C6C6C6;stroke-miterlimit:10;}
	.st204{fill:#FFFFFF;stroke:#C6C6C6;stroke-miterlimit:10;}
	.st205{fill:#BFBFBF;}
	.st206{fill:none;stroke:#929292;stroke-width:1.0006;stroke-miterlimit:10;}
	.st207{fill:none;stroke:#939393;stroke-width:2;stroke-miterlimit:10;}
	.st208{fill:#E8E8E8;stroke:#C6C6C6;stroke-width:1.0171;stroke-miterlimit:10;}
	.st209{fill:#FFFFFF;stroke:#C6C6C6;stroke-width:1.0171;stroke-miterlimit:10;}
	.st210{fill:none;stroke:#C6C6C6;stroke-width:1.0171;stroke-miterlimit:10;}
	.st211{fill:none;stroke:#929292;stroke-width:0.9465;stroke-miterlimit:10;}
	.st212{fill:#FFFFFF;stroke:#C6C6C6;stroke-width:0.9459;stroke-miterlimit:10;}
	.st213{fill:none;stroke:#939393;stroke-width:1.8919;stroke-miterlimit:10;}
	.st214{fill:none;stroke:#EAEAEA;stroke-width:0.6078;stroke-miterlimit:10;}
	.st215{fill:none;stroke:#CCCCCC;stroke-width:1.5;stroke-miterlimit:10;}
	.st216{fill:#B3B3B3;}
	.st217{fill:#FFFFFF;stroke:#000000;stroke-width:0.25;stroke-miterlimit:10;}
	.st218{clip-path:url(#SVGID_134_);}
	.st219{clip-path:url(#SVGID_136_);}
	.st220{clip-path:url(#SVGID_138_);}
	.st221{fill:#F5F5F5;}
	.st222{fill:none;stroke:#CECECE;stroke-width:0.413;stroke-miterlimit:10;}
	.st223{fill:none;stroke:#CCCCCC;stroke-width:0.8606;stroke-miterlimit:10;}
	.st224{fill:none;stroke:#000000;stroke-width:0.5553;stroke-miterlimit:10;}
	.st225{fill:none;stroke:#3F3F3F;stroke-width:0.5553;stroke-miterlimit:10;}
	.st226{fill:none;stroke:#5B5B5B;stroke-width:0.5553;stroke-miterlimit:10;}
	.st227{fill:none;stroke:#F47B67;stroke-width:0.5553;stroke-miterlimit:10;}
	.st228{fill:#E8E8E8;stroke:#C6C6C6;stroke-width:0.5553;stroke-miterlimit:10;}
	.st229{fill:#FFFFFF;stroke:#C6C6C6;stroke-width:0.5499;stroke-miterlimit:10;}
	.st230{fill:none;stroke:#929292;stroke-width:0.5557;stroke-miterlimit:10;}
	.st231{fill:#FFFFFF;stroke:#C6C6C6;stroke-width:0.5553;stroke-miterlimit:10;}
	.st232{fill:none;stroke:#939393;stroke-width:1.1106;stroke-miterlimit:10;}
	.st233{fill:none;stroke:#3F3F3F;stroke-width:0.5501;stroke-miterlimit:10;}
	.st234{fill:#E8E8E8;stroke:#C6C6C6;stroke-width:0.5595;stroke-miterlimit:10;}
	.st235{fill:none;stroke:#C6C6C6;stroke-width:0.5595;stroke-miterlimit:10;}
	.st236{fill:none;stroke:#929292;stroke-width:0.5207;stroke-miterlimit:10;}
	.st237{fill:#FFFFFF;stroke:#C6C6C6;stroke-width:0.5204;stroke-miterlimit:10;}
	.st238{fill:none;stroke:#939393;stroke-width:1.0407;stroke-miterlimit:10;}
	.st239{fill:none;stroke:#F47B67;stroke-width:0.5501;stroke-miterlimit:10;}
	.st240{font-family:'Lato-Light';}
	.st241{font-size:17.0626px;}
	.st242{fill:none;stroke:#CECECE;stroke-width:0.3562;stroke-miterlimit:10;}
	.st243{fill:none;}
	.st244{font-size:23.0892px;}
	.st245{fill:#FFFFFF;stroke:#9B9B9B;stroke-width:0.474;stroke-miterlimit:10;}
	.st246{fill:#FFFFFF;stroke:#9B9B9B;stroke-width:0.4641;stroke-miterlimit:10;}
	.st247{fill:#FFFFFF;stroke:#9B9B9B;stroke-width:0.4611;stroke-miterlimit:10;}
	.st248{fill:#FFFFFF;stroke:#9B9B9B;stroke-width:0.4612;stroke-miterlimit:10;}
	.st249{fill:#FFFFFF;stroke:#9B9B9B;stroke-width:0.4783;stroke-miterlimit:10;}
	.st250{fill:#FFFFFF;stroke:#9B9B9B;stroke-width:0.4741;stroke-miterlimit:10;}
	.st251{fill:#FFFFFF;stroke:#9B9B9B;stroke-width:0.4764;stroke-miterlimit:10;}
	.st252{fill:#FFFFFF;stroke:#9B9B9B;stroke-width:0.4785;stroke-miterlimit:10;}
	.st253{fill:#FFFFFF;stroke:#9B9B9B;stroke-width:0.4207;stroke-miterlimit:10;}
	.st254{fill:#FFFFFF;stroke:#9B9B9B;stroke-width:0.4243;stroke-miterlimit:10;}
	.st255{fill:#FFFFFF;stroke:#9B9B9B;stroke-width:0.4302;stroke-miterlimit:10;}
	.st256{fill:#FFFFFF;stroke:#9B9B9B;stroke-width:0.4799;stroke-miterlimit:10;}
	.st257{fill:#FFFFFF;stroke:#9B9B9B;stroke-width:0.479;stroke-miterlimit:10;}
	.st258{fill:none;stroke:#9B9B9B;stroke-width:0.474;stroke-miterlimit:10;}
</style>
<g>
	
		<image style="overflow:visible;opacity:0.15;" width="807" height="89" xlink:href="5584C28F7EFCBB3B.png"  transform="matrix(1 0 0 1 264.587 332.3576)">
	</image>
	<g>
		<rect x="268.68" y="336.45" class="st142" width="791" height="73"/>
	</g>
</g>
<g>
	
		<image style="overflow:visible;opacity:0.15;" width="810" height="118" xlink:href="5584C28F7EFCBB39.png"  transform="matrix(1 0 0 1 263.4198 430.1904)">
	</image>
	<g>
		<rect x="268.68" y="435.45" class="st0" width="791" height="99"/>
		<rect x="268.68" y="435.45" class="st222" width="791" height="99"/>
	</g>
</g>
<text transform="matrix(1 0 0 1 127.4208 256.2921)" class="st144 st240 st241">Containers</text>
<text transform="matrix(1 0 0 1 127.4208 256.2921)" class="st144 st240 st241">Containers</text>
<text transform="matrix(1 0 0 1 106.6976 478.0939)" class="st144 st240 st241">Infrastructure</text>
<text transform="matrix(1 0 0 1 105.2318 378.5607)" class="st144 st240 st241">Orchestration</text>
<path class="st44" d="M434.68,252.45h-167V107.68c0-1.78,1.45-3.23,3.23-3.23h160.55c1.78,0,3.23,1.44,3.23,3.23V252.45z"/>
<g>
	
		<image style="overflow:visible;opacity:0.15;" width="183" height="74" xlink:href="5584C28F7EFCBB3F.png"  transform="matrix(1 0 0 1 263.5566 248.3272)">
	</image>
	<g>
		<rect x="267.68" y="252.45" class="st0" width="167" height="58"/>
		<rect x="267.68" y="252.45" class="st242" width="167" height="58"/>
	</g>
</g>
<path class="st44" d="M642.68,252.45h-167v-87.77c0-1.78,1.44-3.23,3.23-3.23h160.55c1.78,0,3.23,1.44,3.23,3.23V252.45z"/>
<g>
	
		<image style="overflow:visible;opacity:0.15;" width="183" height="74" xlink:href="5584C28F7EFCBB38.png"  transform="matrix(1 0 0 1 471.5566 248.3272)">
	</image>
	<g>
		<rect x="475.68" y="252.45" class="st0" width="167" height="58"/>
		<rect x="475.68" y="252.45" class="st242" width="167" height="58"/>
	</g>
</g>
<path class="st44" d="M849.68,252.45h-167v-87.77c0-1.78,1.44-3.23,3.23-3.23h160.55c1.78,0,3.23,1.44,3.23,3.23V252.45z"/>
<g>
	
		<image style="overflow:visible;opacity:0.15;" width="183" height="74" xlink:href="5584C28F7EFCBB36.png"  transform="matrix(1 0 0 1 678.5566 248.3272)">
	</image>
	<g>
		<rect x="682.68" y="252.45" class="st0" width="167" height="58"/>
		<rect x="682.68" y="252.45" class="st242" width="167" height="58"/>
	</g>
</g>
<path class="st44" d="M1057.68,252.45h-167v-87.77c0-1.78,1.44-3.23,3.23-3.23h160.55c1.78,0,3.23,1.44,3.23,3.23V252.45z"/>
<g>
	
		<image style="overflow:visible;opacity:0.15;" width="183" height="74" xlink:href="5584C28F7EFCBB37.png"  transform="matrix(1 0 0 1 886.5566 248.3272)">
	</image>
	<g>
		<rect x="890.68" y="252.45" class="st0" width="167" height="58"/>
		<rect x="890.68" y="252.45" class="st242" width="167" height="58"/>
	</g>
</g>
<rect x="683.92" y="65.11" class="st243" width="310.24" height="53.24"/>
<text transform="matrix(1 0 0 1 705.6503 81.9298)"><tspan x="0" y="0" class="st142 st240 st244">Provision object storage as </tspan><tspan x="79.33" y="35.07" class="st142 st240 st244">Containers</tspan></text>
<text transform="matrix(1 0 0 1 326.2503 226.3781)" class="st0 st240 st241">MINIO</text>
<text transform="matrix(1 0 0 1 531.6703 212.1583)" class="st0 st240 st241">MySQL</text>
<text transform="matrix(1 0 0 1 743.0184 212.1583)" class="st0 st240 st241">REDIS</text>
<text transform="matrix(1 0 0 1 939.0199 212.1583)" class="st0 st240 st241">NODE JS</text>
<g>
	<text transform="matrix(1 0 0 1 313.3045 287.3165)" class="st144 st240 st241">DOCKER</text>
</g>
<g>
	<text transform="matrix(1 0 0 1 520.6947 287.3165)" class="st144 st240 st241">DOCKER</text>
</g>
<g>
	<text transform="matrix(1 0 0 1 728.0853 287.3165)" class="st144 st240 st241">DOCKER</text>
</g>
<g>
	<text transform="matrix(1 0 0 1 935.475 287.3165)" class="st144 st240 st241">DOCKER</text>
</g>
<g>
	<text transform="matrix(1 0 0 1 365.8958 378.5607)" class="st0 st240 st241">DOCKER SWARM</text>
</g>
<g>
	<text transform="matrix(1 0 0 1 617.7948 378.5607)" class="st0 st240 st241">MESOSPHERE</text>
</g>
<g>
	<text transform="matrix(1 0 0 1 840.5619 378.5607)" class="st0 st240 st241">KUBERNETES</text>
</g>
<g>
	<path class="st245" d="M784.13,483.41c0-3.62,2.93-6.55,6.55-6.55v-17.01c0-1.87-1.52-3.39-3.39-3.39h-99.21
		c-1.87,0-3.39,1.52-3.39,3.39v17.01c3.62,0,6.55,2.93,6.55,6.55c0,3.62-2.93,6.55-6.55,6.55v20.1c0,1.87,1.52,3.39,3.39,3.39h99.21
		c1.87,0,3.39-1.52,3.39-3.39v-20.1C787.07,489.96,784.13,487.02,784.13,483.41z"/>
	<path class="st245" d="M784.13,483.41c0-3.62,2.93-6.55,6.55-6.55v-17.01c0-1.87-1.52-3.39-3.39-3.39h-99.21
		c-1.87,0-3.39,1.52-3.39,3.39v17.01c3.62,0,6.55,2.93,6.55,6.55c0,3.62-2.93,6.55-6.55,6.55v20.1c0,1.87,1.52,3.39,3.39,3.39h99.21
		c1.87,0,3.39-1.52,3.39-3.39v-20.1C787.07,489.96,784.13,487.02,784.13,483.41z"/>
	<rect x="697.68" y="470.45" class="st246" width="10" height="23"/>
	<rect x="694.68" y="473.45" class="st247" width="3" height="3"/>
	<rect x="694.68" y="480.45" class="st247" width="3" height="3"/>
	<rect x="694.68" y="487.45" class="st247" width="3" height="3"/>
	<rect x="694.68" y="473.45" class="st248" width="16" height="3"/>
	<rect x="694.68" y="487.45" class="st248" width="16" height="3"/>
	<rect x="694.68" y="480.45" class="st248" width="16" height="3"/>
	<rect x="718.68" y="473.45" class="st248" width="16" height="3"/>
	<rect x="718.68" y="487.45" class="st248" width="16" height="3"/>
	<rect x="718.68" y="480.45" class="st248" width="16" height="3"/>
	<rect x="741.68" y="473.45" class="st248" width="16" height="3"/>
	<rect x="741.68" y="487.45" class="st248" width="16" height="3"/>
	<rect x="741.68" y="480.45" class="st248" width="16" height="3"/>
	<rect x="764.68" y="473.45" class="st248" width="16" height="3"/>
	<rect x="764.68" y="487.45" class="st248" width="16" height="3"/>
	<rect x="764.68" y="480.45" class="st248" width="16" height="3"/>
	<rect x="691.68" y="502.45" class="st249" width="3" height="11"/>
	<rect x="694.68" y="502.45" class="st249" width="3" height="11"/>
	<rect x="701.68" y="502.45" class="st249" width="3" height="11"/>
	<rect x="704.68" y="502.45" class="st249" width="3" height="11"/>
	<rect x="711.68" y="502.45" class="st249" width="3" height="11"/>
	<rect x="714.68" y="502.45" class="st249" width="3" height="11"/>
	<rect x="721.68" y="502.45" class="st249" width="3" height="11"/>
	<rect x="724.68" y="502.45" class="st249" width="3" height="11"/>
	<rect x="738.68" y="502.45" class="st249" width="3" height="11"/>
	<rect x="741.68" y="502.45" class="st249" width="3" height="11"/>
	<rect x="748.68" y="502.45" class="st249" width="3" height="11"/>
	<rect x="751.68" y="502.45" class="st249" width="3" height="11"/>
	<rect x="758.68" y="502.45" class="st249" width="3" height="11"/>
	<rect x="761.68" y="502.45" class="st249" width="3" height="11"/>
	<rect x="768.68" y="502.45" class="st249" width="3" height="11"/>
	<rect x="771.68" y="502.45" class="st249" width="3" height="11"/>
	<rect x="778.68" y="502.45" class="st249" width="3" height="11"/>
	<rect x="781.68" y="502.45" class="st249" width="3" height="11"/>
	<rect x="697.68" y="470.45" class="st246" width="10" height="23"/>
	<rect x="721.68" y="470.45" class="st246" width="10" height="23"/>
	<rect x="744.68" y="470.45" class="st246" width="10" height="23"/>
	<rect x="767.68" y="470.45" class="st246" width="10" height="23"/>
</g>
<g>
	<g>
		<path class="st245" d="M594.7,454.39l-0.02,0.02c-0.6-0.59-1.43-0.96-2.35-0.96h-57.31c-1.85,0-3.34,1.5-3.34,3.34v57.34
			c0,0.93,0.38,1.76,0.98,2.37l0.02-0.02c0.6,0.59,1.43,0.96,2.35,0.96h57.31c1.85,0,3.34-1.5,3.34-3.34v-57.34
			C595.68,455.84,595.3,455,594.7,454.39z"/>
		<path class="st245" d="M583.95,465.19L583.95,465.19C583.95,465.19,583.94,465.19,583.95,465.19
			C583.94,465.19,583.94,465.19,583.95,465.19L583.95,465.19c-0.46-0.46-1.09-0.74-1.78-0.74H545.2c-1.39,0-2.52,1.13-2.52,2.52
			v37.07c0,0.67,0.27,1.27,0.71,1.7l0,0c0,0,0,0,0,0c0,0,0,0,0,0l0,0c0.44,0.44,1.04,0.71,1.7,0.71h9.76c0.43,0,0.85-0.17,1.16-0.47
			l1.12-1.07c0.31-0.3,0.73-0.47,1.16-0.47h9.06c0.43,0,0.85,0.17,1.16,0.47l1.12,1.07c0.31,0.3,0.73,0.47,1.16,0.47h11.37
			c1.39,0,2.52-1.13,2.52-2.52v-36.97C584.68,466.27,584.4,465.65,583.95,465.19z"/>
		<path class="st245" d="M577.99,471.15L577.99,471.15C577.99,471.15,577.99,471.15,577.99,471.15
			C577.99,471.14,577.99,471.14,577.99,471.15L577.99,471.15c-1.05-1.05-2.49-1.7-4.09-1.7h-20.37c-3.23,0-5.85,2.62-5.85,5.85
			v20.37c0,1.6,0.65,3.04,1.69,4.09l0,0c0,0,0,0,0,0c0,0,0,0,0,0l0,0c1.05,1.05,2.49,1.69,4.09,1.69h20.37
			c3.23,0,5.85-2.62,5.85-5.85v-20.37C579.68,473.64,579.03,472.19,577.99,471.15z"/>
		<path class="st245" d="M575.87,473.27L575.87,473.27C575.87,473.27,575.86,473.27,575.87,473.27
			C575.86,473.27,575.86,473.27,575.87,473.27L575.87,473.27c-0.51-0.51-1.2-0.82-1.97-0.82h-20.38c-1.56,0-2.83,1.27-2.83,2.83
			v20.38c0,0.77,0.31,1.46,0.81,1.97l0,0c0,0,0,0,0,0c0,0,0,0,0,0l0,0c0.5,0.5,1.2,0.81,1.97,0.81h20.38c1.56,0,2.83-1.27,2.83-2.83
			v-20.38C576.68,474.47,576.37,473.77,575.87,473.27z"/>
		<path class="st245" d="M529.46,461.45c-1.45,0-2.62,1.24-2.49,2.72c0.11,1.32,1.34,2.28,2.67,2.28h8.04v-2.5v-2.5H529.46z"/>
		<path class="st245" d="M529.46,472.45c-1.45,0-2.62,1.24-2.49,2.72c0.11,1.32,1.34,2.28,2.67,2.28h8.04v-2.5v-2.5H529.46z"/>
		<path class="st245" d="M529.46,482.45c-1.45,0-2.62,1.24-2.49,2.72c0.11,1.32,1.34,2.28,2.67,2.28h8.04v-2.5v-2.5H529.46z"/>
		<path class="st245" d="M529.46,493.45c-1.45,0-2.62,1.24-2.49,2.72c0.11,1.32,1.34,2.28,2.67,2.28h8.04v-2.5v-2.5H529.46z"/>
		<path class="st245" d="M529.46,504.45c-1.45,0-2.62,1.24-2.49,2.72c0.11,1.32,1.34,2.28,2.67,2.28h8.04v-2.5v-2.5H529.46z"/>
		<path class="st245" d="M542.18,511.45h-2.5v8.93c0,1.12,0.65,2.19,1.71,2.54c1.72,0.57,3.29-0.8,3.29-2.57v-8.89H542.18z"/>
		<path class="st245" d="M553.18,511.45h-2.5v8.93c0,1.12,0.65,2.19,1.71,2.54c1.72,0.57,3.29-0.8,3.29-2.57v-8.89H553.18z"/>
		<path class="st245" d="M563.18,511.45h-2.5v8.93c0,1.12,0.65,2.19,1.71,2.54c1.72,0.57,3.29-0.8,3.29-2.57v-8.89H563.18z"/>
		<path class="st245" d="M574.18,511.45h-2.5v8.93c0,1.12,0.65,2.19,1.71,2.54c1.72,0.57,3.29-0.8,3.29-2.57v-8.89H574.18z"/>
		<path class="st245" d="M585.18,511.45h-2.5v8.93c0,1.12,0.65,2.19,1.71,2.54c1.72,0.57,3.29-0.8,3.29-2.57v-8.89H585.18z"/>
		<path class="st245" d="M539.68,450.53v8.93h2.5h2.5v-8.89c0-1.77-1.57-3.14-3.29-2.57C540.33,448.33,539.68,449.41,539.68,450.53z
			"/>
		<path class="st245" d="M553.18,447.85L553.18,447.85c-1.38,0-2.5,1.21-2.5,2.7v8.89h2.5h2.5v-8.89
			C555.68,449.06,554.56,447.85,553.18,447.85z"/>
		<path class="st245" d="M563.18,447.85L563.18,447.85c-1.38,0-2.5,1.21-2.5,2.7v8.89h2.5h2.5v-8.89
			C565.68,449.06,564.56,447.85,563.18,447.85z"/>
		<path class="st245" d="M574.18,447.85L574.18,447.85c-1.38,0-2.5,1.21-2.5,2.7v8.89h2.5h2.5v-8.89
			C576.68,449.06,575.56,447.85,574.18,447.85z"/>
		<path class="st245" d="M585.18,447.85L585.18,447.85c-1.38,0-2.5,1.21-2.5,2.7v8.89h2.5h2.5v-8.89
			C587.68,449.06,586.56,447.85,585.18,447.85z"/>
		<path class="st245" d="M597.9,461.45h-8.22v2.5v2.5h8.22c1.38,0,2.5-1.12,2.5-2.5l0,0C600.4,462.57,599.28,461.45,597.9,461.45z"
			/>
		<path class="st245" d="M597.9,472.45h-8.22v2.5v2.5h8.22c1.38,0,2.5-1.12,2.5-2.5l0,0C600.4,473.57,599.28,472.45,597.9,472.45z"
			/>
		<path class="st245" d="M597.9,482.45h-8.22v2.5v2.5h8.22c1.38,0,2.5-1.12,2.5-2.5l0,0C600.4,483.57,599.28,482.45,597.9,482.45z"
			/>
		<path class="st245" d="M597.9,493.45h-8.22v2.5v2.5h8.22c1.38,0,2.5-1.12,2.5-2.5l0,0C600.4,494.57,599.28,493.45,597.9,493.45z"
			/>
		<path class="st245" d="M597.9,503.45h-8.22v2.5v2.5h8.22c1.38,0,2.5-1.12,2.5-2.5l0,0C600.4,504.57,599.28,503.45,597.9,503.45z"
			/>
	</g>
</g>
<g>
	<polygon class="st245" points="881.68,455.45 881.68,508.45 886.68,508.45 886.68,500.45 899.68,500.45 899.68,508.45 
		952.68,508.45 952.68,455.45 	"/>
	<path class="st250" d="M943.69,492.45h-23.02c-0.55,0-0.99-0.44-0.99-0.99v-26.02c0-0.55,0.44-0.99,0.99-0.99h23.02
		c0.55,0,0.99,0.44,0.99,0.99v26.02C944.68,492.01,944.24,492.45,943.69,492.45z"/>
	<path class="st251" d="M911.68,477.45h-18.01c-0.55,0-1-0.45-1-1v-11.01c0-0.55,0.45-1,1-1h18.01c0.55,0,1,0.45,1,1v11.01
		C912.68,477,912.23,477.45,911.68,477.45z"/>
	<path class="st252" d="M911.68,495.45h-18c-0.55,0-1-0.45-1-1v-11c0-0.55,0.45-1,1-1h18c0.55,0,1,0.45,1,1v11
		C912.68,495,912.23,495.45,911.68,495.45z"/>
	<rect x="917.68" y="468.45" class="st253" width="5" height="2"/>
	<rect x="917.68" y="473.45" class="st253" width="5" height="2"/>
	<rect x="917.68" y="478.45" class="st253" width="5" height="2"/>
	<rect x="917.68" y="483.45" class="st253" width="5" height="2"/>
	<rect x="917.68" y="488.45" class="st253" width="5" height="2"/>
	<rect x="942.68" y="468.45" class="st253" width="5" height="2"/>
	<rect x="942.68" y="473.45" class="st253" width="5" height="2"/>
	<rect x="942.68" y="478.45" class="st253" width="5" height="2"/>
	<rect x="942.68" y="483.45" class="st253" width="5" height="2"/>
	<rect x="942.68" y="488.45" class="st253" width="5" height="2"/>
	<g>
		<rect x="920.68" y="509.45" class="st254" width="2" height="6"/>
	</g>
	<g>
		<rect x="922.68" y="509.45" class="st254" width="2" height="6"/>
	</g>
	<g>
		
			<rect x="935.68" y="515.45" transform="matrix(-1.836970e-16 1 -1 -1.836970e-16 1456.1321 -417.2294)" class="st255" width="2" height="8"/>
	</g>
	<g>
		
			<rect x="935.68" y="517.45" transform="matrix(-1.836970e-16 1 -1 -1.836970e-16 1458.1321 -415.2294)" class="st255" width="2" height="8"/>
	</g>
	<g>
		
			<rect x="907.68" y="515.45" transform="matrix(-1.836970e-16 1 -1 -1.836970e-16 1428.1321 -389.2294)" class="st255" width="2" height="8"/>
	</g>
	<g>
		
			<rect x="907.68" y="517.45" transform="matrix(-1.836970e-16 1 -1 -1.836970e-16 1430.1321 -387.2294)" class="st255" width="2" height="8"/>
	</g>
	<path class="st245" d="M875.18,444.45h-3.5v6h4.02c0.72-0.26,0.98,1.04,0.98,1.04v68.96h5v-69.5
		C881.68,447.36,878.77,444.45,875.18,444.45z"/>
	<path class="st256" d="M931,525.45h-16.65c-0.93,0-1.68-0.75-1.68-1.68v-6.65c0-0.93,0.75-1.68,1.68-1.68H931
		c0.93,0,1.68,0.75,1.68,1.68v6.65C932.68,524.7,931.93,525.45,931,525.45z"/>
	<rect x="904.68" y="500.45" class="st257" width="5" height="5"/>
	<rect x="904.68" y="505.45" class="st257" width="5" height="5"/>
	<rect x="912.68" y="500.45" class="st257" width="5" height="5"/>
	<rect x="912.68" y="505.45" class="st257" width="5" height="5"/>
	<rect x="919.68" y="500.45" class="st257" width="5" height="5"/>
	<rect x="919.68" y="505.45" class="st257" width="5" height="5"/>
	<rect x="927.68" y="500.45" class="st257" width="5" height="5"/>
	<rect x="927.68" y="505.45" class="st257" width="5" height="5"/>
	<rect x="935.68" y="500.45" class="st257" width="5" height="5"/>
	<rect x="935.68" y="505.45" class="st257" width="5" height="5"/>
	<rect x="942.68" y="500.45" class="st257" width="5" height="5"/>
	<rect x="942.68" y="505.45" class="st257" width="5" height="5"/>
</g>
<g>
	<path class="st245" d="M417.68,449.57c0-1.17-0.95-2.12-2.12-2.12h-26.38H362.8c-1.17,0-2.12,0.95-2.12,2.12v71.76
		c0,1.17,0.95,2.12,2.12,2.12h26.38h26.38c1.17,0,2.12-0.95,2.12-2.12V449.57z"/>
	<path class="st258" d="M389.68,523.45l0-11.52c2.51-0.07,4.94-0.46,7.25-1.14l5.89,4.17c0.12,0.09,0.24,0.18,0.37,0.27l0.04,0.03
		l0,0c1.65,1.06,3.7,1.57,5.89,1.26c3.56-0.5,6.52-3.27,7.09-6.81c0.57-3.56-1.19-6.77-4.03-8.42c0.18-0.24,3.63-4.76,3.94-5.84
		h1.57"/>
	<g>
		<path class="st0" d="M389.55,508.72c-14.27,0-25.87-11.61-25.87-25.87c0-14.27,11.61-25.87,25.87-25.87s25.87,11.61,25.87,25.87
			C415.42,497.11,403.82,508.72,389.55,508.72z"/>
		<path class="st148" d="M389.55,457.21c14.14,0,25.64,11.5,25.64,25.64s-11.5,25.64-25.64,25.64s-25.64-11.5-25.64-25.64
			S375.41,457.21,389.55,457.21 M389.55,456.74c-14.42,0-26.11,11.69-26.11,26.11s11.69,26.11,26.11,26.11s26.11-11.69,26.11-26.11
			S403.97,456.74,389.55,456.74L389.55,456.74z"/>
	</g>
	<g>
		<circle class="st0" cx="365.18" cy="450.95" r="1.29"/>
		<path class="st148" d="M365.18,449.87c0.6,0,1.09,0.49,1.09,1.09s-0.49,1.09-1.09,1.09s-1.09-0.49-1.09-1.09
			S364.58,449.87,365.18,449.87 M365.18,449.45c-0.83,0-1.5,0.67-1.5,1.5s0.67,1.5,1.5,1.5s1.5-0.67,1.5-1.5
			S366.01,449.45,365.18,449.45L365.18,449.45z"/>
	</g>
	<g>
		<circle class="st0" cx="414.18" cy="450.95" r="1.29"/>
		<path class="st148" d="M414.18,449.87c0.6,0,1.09,0.49,1.09,1.09s-0.49,1.09-1.09,1.09s-1.09-0.49-1.09-1.09
			S413.58,449.87,414.18,449.87 M414.18,449.45c-0.83,0-1.5,0.67-1.5,1.5s0.67,1.5,1.5,1.5s1.5-0.67,1.5-1.5
			S415.01,449.45,414.18,449.45L414.18,449.45z"/>
	</g>
	<g>
		<circle class="st0" cx="365.18" cy="518.95" r="1.29"/>
		<path class="st148" d="M365.18,517.87c0.6,0,1.09,0.49,1.09,1.09s-0.49,1.09-1.09,1.09s-1.09-0.49-1.09-1.09
			S364.58,517.87,365.18,517.87 M365.18,517.45c-0.83,0-1.5,0.67-1.5,1.5s0.67,1.5,1.5,1.5s1.5-0.67,1.5-1.5
			S366.01,517.45,365.18,517.45L365.18,517.45z"/>
	</g>
	<g>
		<circle class="st0" cx="414.18" cy="518.95" r="1.29"/>
		<path class="st148" d="M414.18,517.87c0.6,0,1.09,0.49,1.09,1.09s-0.49,1.09-1.09,1.09s-1.09-0.49-1.09-1.09
			S413.58,517.87,414.18,517.87 M414.18,517.45c-0.83,0-1.5,0.67-1.5,1.5s0.67,1.5,1.5,1.5s1.5-0.67,1.5-1.5
			S415.01,517.45,414.18,517.45L414.18,517.45z"/>
	</g>
	<g>
		<circle class="st0" cx="389.45" cy="482.85" r="6.84"/>
		<path class="st148" d="M389.45,476.25c3.64,0,6.6,2.96,6.6,6.6s-2.96,6.6-6.6,6.6s-6.6-2.96-6.6-6.6S385.82,476.25,389.45,476.25
			 M389.45,475.77c-3.91,0-7.07,3.17-7.07,7.07s3.17,7.07,7.07,7.07s7.07-3.17,7.07-7.07S393.36,475.77,389.45,475.77L389.45,475.77
			z"/>
	</g>
	<g>
		<path class="st0" d="M407.35,513.65c-1.13,0-2.22-0.33-3.16-0.95l-23.02-16.75l-0.07-0.05c-0.26-0.2-0.41-0.5-0.41-0.82
			c0-0.57,0.46-1.04,1.04-1.04c0.08,0,0.16,0.01,0.26,0.04l27.61,8.48c2.13,0.9,3.52,2.99,3.52,5.32
			C413.13,511.06,410.54,513.65,407.35,513.65z"/>
		<path class="st148" d="M381.73,494.28c0.06,0,0.12,0.01,0.18,0.03l27.58,8.46c2.06,0.87,3.4,2.87,3.4,5.11
			c0,3.05-2.48,5.54-5.54,5.54c-1.08,0-2.13-0.31-3.03-0.91l-0.04-0.02l-22.88-16.66l-0.15-0.11c-0.2-0.15-0.32-0.38-0.32-0.63
			C380.93,494.64,381.29,494.28,381.73,494.28 M381.73,493.81c-0.7,0-1.27,0.57-1.27,1.27c0,0.41,0.2,0.77,0.5,1.01l0,0l23.1,16.81
			l0,0c0.95,0.62,2.08,0.99,3.29,0.99c3.32,0,6.01-2.69,6.01-6.01c0-2.5-1.52-4.63-3.69-5.54l-27.62-8.48
			C381.95,493.83,381.85,493.81,381.73,493.81L381.73,493.81z"/>
	</g>
	<g>
		<circle class="st0" cx="407.68" cy="507.45" r="3.75"/>
		<path class="st148" d="M407.68,503.94c1.93,0,3.51,1.57,3.51,3.51s-1.57,3.51-3.51,3.51s-3.51-1.57-3.51-3.51
			S405.75,503.94,407.68,503.94 M407.68,503.45c-2.21,0-4,1.79-4,4s1.79,4,4,4s4-1.79,4-4S409.89,503.45,407.68,503.45
			L407.68,503.45z"/>
	</g>
</g>
<g>
	<g>
		<g>
			<path class="st221" d="M352.68,199.54l-5,2.84v-29.3c-0.16-0.16-0.62-0.32-1.05-0.46c-6.33-2.11-10.37-5.69-11.36-10.07
				c-0.81-3.55,0.49-7.3,3.56-10.27c2.8-2.71,6.72-5.83,9.88-8.34c1.14-0.91,2.13-1.69,2.92-2.34c0.73-0.62,0.51-1.57,0.18-1.97
				c-0.55-0.66-1.46-0.55-2.08-0.1c-0.03,0.02-1.79,1.4-1.79,1.4l-2.72-3.67c0,0,1.78-1.31,1.85-1.36c2.66-1.95,6.19-1.58,8.22,0.86
				c1.01,1.22,1.48,2.76,1.33,4.33c-0.15,1.57-0.9,2.99-2.12,3.99c-0.82,0.68-1.82,1.47-2.98,2.39c-3.08,2.45-6.91,5.5-9.55,8.06
				c-1.94,1.88-2.76,4.02-2.3,6.03c0.63,2.77,3.59,5.18,8.02,6.77v-14.65c13.09,4.32,22.11,19.4,25.11,27.77
				c0,0-13.23-4.49-20.11-6.86V199.54z M352.68,169.85c3.93,1.32,8.57,2.89,12.03,4.06c-3.8-6.72-8.82-10.81-12.03-12.92V169.85z"/>
		</g>
	</g>
	<polygon class="st42" points="352.68,177.55 347.68,176.04 347.68,173.09 352.68,174.6 	"/>
	<path class="st183" d="M330.75,151.86c-0.07,0.05-0.17,0.05-0.23-0.02c-0.06-0.07-0.04-0.17,0.02-0.23
		c1.56-1.43,7.75-7.56,14.68-14.34l2.72,3.67C347.94,140.93,330.97,151.71,330.75,151.86z"/>
</g>
</svg>
