<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 21.0.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
	 viewBox="0 0 1024 440" style="enable-background:new 0 0 1024 440;" xml:space="preserve">
<style type="text/css">
	.st0{fill:#FFFFFF;}
	.st1{fill:#343435;}
	.st2{fill:#494949;}
	.st3{fill:#C4C4C4;}
	.st4{fill:#21211F;}
	.st5{fill:#E6E6E6;}
	.st6{opacity:0.56;fill:url(#SVGID_1_);}
	.st7{fill:#39B54A;}
	.st8{opacity:0.56;fill:url(#SVGID_2_);}
	.st9{opacity:0.56;fill:url(#SVGID_3_);}
	.st10{opacity:0.56;fill:url(#SVGID_4_);}
	.st11{opacity:0.56;fill:url(#SVGID_5_);}
	.st12{opacity:0.56;fill:url(#SVGID_6_);}
	.st13{opacity:0.56;fill:url(#SVGID_7_);}
	.st14{opacity:0.56;fill:url(#SVGID_8_);}
	.st15{opacity:0.56;fill:url(#SVGID_9_);}
	.st16{opacity:0.56;fill:url(#SVGID_10_);}
	.st17{opacity:0.56;fill:url(#SVGID_11_);}
	.st18{opacity:0.56;fill:url(#SVGID_12_);}
	.st19{opacity:0.56;fill:url(#SVGID_13_);}
	.st20{opacity:0.56;fill:url(#SVGID_14_);}
	.st21{opacity:0.56;fill:url(#SVGID_15_);}
	.st22{opacity:0.56;fill:url(#SVGID_16_);}
	.st23{opacity:0.56;fill:url(#SVGID_17_);}
	.st24{opacity:0.56;fill:url(#SVGID_18_);}
	.st25{opacity:0.56;fill:url(#SVGID_19_);}
	.st26{opacity:0.56;fill:url(#SVGID_20_);}
	.st27{opacity:0.56;fill:url(#SVGID_21_);}
	.st28{opacity:0.56;fill:url(#SVGID_22_);}
	.st29{opacity:0.56;fill:url(#SVGID_23_);}
	.st30{opacity:0.56;fill:url(#SVGID_24_);}
	.st31{opacity:0.56;fill:url(#SVGID_25_);}
	.st32{opacity:0.56;fill:url(#SVGID_26_);}
	.st33{opacity:0.56;fill:url(#SVGID_27_);}
	.st34{opacity:0.56;fill:url(#SVGID_28_);}
	.st35{opacity:0.56;fill:url(#SVGID_29_);}
	.st36{opacity:0.56;fill:url(#SVGID_30_);}
	.st37{opacity:0.56;fill:url(#SVGID_31_);}
	.st38{opacity:0.56;fill:url(#SVGID_32_);}
	.st39{opacity:0.56;fill:url(#SVGID_33_);}
	.st40{fill:none;stroke:#000000;stroke-miterlimit:10;}
	.st41{fill:none;stroke:#CCCCCC;stroke-width:2;stroke-miterlimit:10;}
	.st42{fill:#CCCCCC;}
	.st43{fill:none;stroke:#666666;stroke-width:2;stroke-miterlimit:10;}
	.st44{fill:#666666;}
	.st45{fill:none;stroke:#EF524F;stroke-width:2;stroke-miterlimit:10;stroke-dasharray:12,12;}
	.st46{fill:#EF524F;}
	.st47{fill:#62757C;}
	.st48{fill:#23363A;}
	.st49{fill:#FEFBF6;}
	.st50{fill:#F7F7F7;stroke:#23363A;stroke-width:6;stroke-miterlimit:10;}
	.st51{fill:none;stroke:#23363A;stroke-width:3;stroke-miterlimit:10;}
	.st52{fill:#F7F7F7;}
	.st53{fill:#23363A;stroke:#F7F7F7;stroke-width:2;stroke-miterlimit:10;}
	.st54{fill:#37474F;}
	.st55{fill:#EDEAE2;}
	.st56{fill:#4C626D;}
	.st57{fill:#E8EDEF;}
	.st58{fill:#515151;}
	.st59{fill:#939393;}
	.st60{opacity:0.56;fill:url(#SVGID_34_);}
	.st61{opacity:0.56;fill:url(#SVGID_35_);}
	.st62{opacity:0.56;fill:url(#SVGID_36_);}
	.st63{opacity:0.56;fill:url(#SVGID_37_);}
	.st64{opacity:0.56;fill:url(#SVGID_38_);}
	.st65{opacity:0.56;fill:url(#SVGID_39_);}
	.st66{opacity:0.56;fill:url(#SVGID_40_);}
	.st67{opacity:0.56;fill:url(#SVGID_41_);}
	.st68{opacity:0.56;fill:url(#SVGID_42_);}
	.st69{opacity:0.56;fill:url(#SVGID_43_);}
	.st70{opacity:0.56;fill:url(#SVGID_44_);}
	.st71{opacity:0.56;fill:url(#SVGID_45_);}
	.st72{opacity:0.56;fill:url(#SVGID_46_);}
	.st73{opacity:0.56;fill:url(#SVGID_47_);}
	.st74{opacity:0.56;fill:url(#SVGID_48_);}
	.st75{opacity:0.56;fill:url(#SVGID_49_);}
	.st76{opacity:0.56;fill:url(#SVGID_50_);}
	.st77{opacity:0.56;fill:url(#SVGID_51_);}
	.st78{opacity:0.56;fill:url(#SVGID_52_);}
	.st79{opacity:0.56;fill:url(#SVGID_53_);}
	.st80{opacity:0.56;fill:url(#SVGID_54_);}
	.st81{opacity:0.56;fill:url(#SVGID_55_);}
	.st82{opacity:0.56;fill:url(#SVGID_56_);}
	.st83{opacity:0.56;fill:url(#SVGID_57_);}
	.st84{opacity:0.56;fill:url(#SVGID_58_);}
	.st85{opacity:0.56;fill:url(#SVGID_59_);}
	.st86{opacity:0.56;fill:url(#SVGID_60_);}
	.st87{opacity:0.56;fill:url(#SVGID_61_);}
	.st88{opacity:0.56;fill:url(#SVGID_62_);}
	.st89{opacity:0.56;fill:url(#SVGID_63_);}
	.st90{opacity:0.56;fill:url(#SVGID_64_);}
	.st91{opacity:0.56;fill:url(#SVGID_65_);}
	.st92{opacity:0.56;fill:url(#SVGID_66_);}
	.st93{fill:none;stroke:#898989;stroke-miterlimit:10;}
	.st94{fill:#898989;}
	.st95{fill:none;stroke:#666666;stroke-miterlimit:10;}
	.st96{fill:none;stroke:#EF524F;stroke-miterlimit:10;}
	.st97{fill:none;stroke:#EF524F;stroke-miterlimit:10;stroke-dasharray:12.0065,12.0065;}
	.st98{fill:#B2B2B2;}
	.st99{fill:#5A5C5E;}
	.st100{fill:#DBDBDB;}
	.st101{fill:#6A6B6B;}
	.st102{fill:none;stroke:#EF524F;stroke-miterlimit:10;stroke-dasharray:11.6391,11.6391;}
	.st103{fill:none;stroke:#EF524F;stroke-miterlimit:10;stroke-dasharray:12.7731,12.7731;}
	.st104{fill:none;stroke:#EF524F;stroke-miterlimit:10;stroke-dasharray:11.9372,11.9372;}
	.st105{opacity:0.56;fill:url(#SVGID_67_);}
	.st106{opacity:0.56;fill:url(#SVGID_68_);}
	.st107{opacity:0.56;fill:url(#SVGID_69_);}
	.st108{opacity:0.56;fill:url(#SVGID_70_);}
	.st109{opacity:0.56;fill:url(#SVGID_71_);}
	.st110{opacity:0.56;fill:url(#SVGID_72_);}
	.st111{opacity:0.56;fill:url(#SVGID_73_);}
	.st112{opacity:0.56;fill:url(#SVGID_74_);}
	.st113{opacity:0.56;fill:url(#SVGID_75_);}
	.st114{opacity:0.56;fill:url(#SVGID_76_);}
	.st115{opacity:0.56;fill:url(#SVGID_77_);}
	.st116{opacity:0.56;fill:url(#SVGID_78_);}
	.st117{opacity:0.56;fill:url(#SVGID_79_);}
	.st118{opacity:0.56;fill:url(#SVGID_80_);}
	.st119{opacity:0.56;fill:url(#SVGID_81_);}
	.st120{opacity:0.56;fill:url(#SVGID_82_);}
	.st121{opacity:0.56;fill:url(#SVGID_83_);}
	.st122{opacity:0.56;fill:url(#SVGID_84_);}
	.st123{opacity:0.56;fill:url(#SVGID_85_);}
	.st124{opacity:0.56;fill:url(#SVGID_86_);}
	.st125{opacity:0.56;fill:url(#SVGID_87_);}
	.st126{opacity:0.56;fill:url(#SVGID_88_);}
	.st127{opacity:0.56;fill:url(#SVGID_89_);}
	.st128{opacity:0.56;fill:url(#SVGID_90_);}
	.st129{opacity:0.56;fill:url(#SVGID_91_);}
	.st130{opacity:0.56;fill:url(#SVGID_92_);}
	.st131{opacity:0.56;fill:url(#SVGID_93_);}
	.st132{opacity:0.56;fill:url(#SVGID_94_);}
	.st133{opacity:0.56;fill:url(#SVGID_95_);}
	.st134{opacity:0.56;fill:url(#SVGID_96_);}
	.st135{opacity:0.56;fill:url(#SVGID_97_);}
	.st136{opacity:0.56;fill:url(#SVGID_98_);}
	.st137{opacity:0.56;fill:url(#SVGID_99_);}
	.st138{fill:none;stroke:#CCCCCC;stroke-width:1.778;stroke-miterlimit:10;}
	.st139{fill:none;stroke:#EF524F;stroke-width:1.778;stroke-miterlimit:10;stroke-dasharray:10.6678,10.6678;}
	.st140{fill:#E5E5E5;}
	.st141{fill:none;stroke:#666666;stroke-width:1.778;stroke-miterlimit:10;}
	.st142{fill:#F47B67;}
	.st143{fill:none;stroke:#EAEAEA;stroke-width:0.8726;stroke-miterlimit:10;}
	.st144{fill:#727272;}
	.st145{fill:none;stroke:#EAEAEA;stroke-width:0.75;stroke-miterlimit:10;}
	.st146{fill:#FFFFFF;stroke:#9B9B9B;stroke-miterlimit:10;}
	.st147{fill:none;stroke:#9B9B9B;stroke-miterlimit:10;}
	.st148{fill:#9B9B9B;}
	.st149{opacity:0.56;fill:url(#SVGID_100_);}
	.st150{opacity:0.56;fill:url(#SVGID_101_);}
	.st151{opacity:0.56;fill:url(#SVGID_102_);}
	.st152{opacity:0.56;fill:url(#SVGID_103_);}
	.st153{opacity:0.56;fill:url(#SVGID_104_);}
	.st154{opacity:0.56;fill:url(#SVGID_105_);}
	.st155{opacity:0.56;fill:url(#SVGID_106_);}
	.st156{opacity:0.56;fill:url(#SVGID_107_);}
	.st157{opacity:0.56;fill:url(#SVGID_108_);}
	.st158{opacity:0.56;fill:url(#SVGID_109_);}
	.st159{opacity:0.56;fill:url(#SVGID_110_);}
	.st160{opacity:0.56;fill:url(#SVGID_111_);}
	.st161{opacity:0.56;fill:url(#SVGID_112_);}
	.st162{opacity:0.56;fill:url(#SVGID_113_);}
	.st163{opacity:0.56;fill:url(#SVGID_114_);}
	.st164{opacity:0.56;fill:url(#SVGID_115_);}
	.st165{opacity:0.56;fill:url(#SVGID_116_);}
	.st166{opacity:0.56;fill:url(#SVGID_117_);}
	.st167{opacity:0.56;fill:url(#SVGID_118_);}
	.st168{opacity:0.56;fill:url(#SVGID_119_);}
	.st169{opacity:0.56;fill:url(#SVGID_120_);}
	.st170{opacity:0.56;fill:url(#SVGID_121_);}
	.st171{opacity:0.56;fill:url(#SVGID_122_);}
	.st172{opacity:0.56;fill:url(#SVGID_123_);}
	.st173{opacity:0.56;fill:url(#SVGID_124_);}
	.st174{opacity:0.56;fill:url(#SVGID_125_);}
	.st175{opacity:0.56;fill:url(#SVGID_126_);}
	.st176{opacity:0.56;fill:url(#SVGID_127_);}
	.st177{opacity:0.56;fill:url(#SVGID_128_);}
	.st178{opacity:0.56;fill:url(#SVGID_129_);}
	.st179{opacity:0.56;fill:url(#SVGID_130_);}
	.st180{opacity:0.56;fill:url(#SVGID_131_);}
	.st181{opacity:0.56;fill:url(#SVGID_132_);}
	.st182{fill:#E8E8E8;stroke:#C6C6C6;stroke-width:1.0826;stroke-miterlimit:10;}
	.st183{fill:#F15A24;}
	.st184{fill:#FFFFFF;stroke:#C6C6C6;stroke-width:1.0826;stroke-miterlimit:10;}
	.st185{fill:#636363;}
	.st186{fill:none;stroke:#636363;stroke-miterlimit:10;}
	.st187{fill:none;stroke:#EF524F;stroke-miterlimit:10;stroke-dasharray:12,12;}
	.st188{fill:none;stroke:#636363;stroke-width:0.6802;stroke-miterlimit:10;}
	.st189{fill:#FFFFFF;stroke:#636363;stroke-width:0.6802;stroke-miterlimit:10;}
	.st190{fill:none;stroke:#EF524F;stroke-miterlimit:10;stroke-dasharray:11.4666,11.4666;}
	.st191{fill:#E8E8E8;stroke:#898989;stroke-width:0.5488;stroke-miterlimit:10;}
	.st192{fill:#FFFFFF;stroke:#898989;stroke-width:0.5488;stroke-miterlimit:10;}
	.st193{fill:#22B573;}
	.st194{fill:#E8E8E8;}
	.st195{fill:#A8A8A8;}
	.st196{fill:#777777;}
	.st197{fill:#4C4C4C;}
	.st198{fill:none;stroke:#3F3F3F;stroke-miterlimit:10;}
	.st199{fill:#3F3F3F;}
	.st200{fill:none;stroke:#5B5B5B;stroke-miterlimit:10;}
	.st201{fill:#5B5B5B;}
	.st202{fill:none;stroke:#F47B67;stroke-miterlimit:10;}
	.st203{fill:#E8E8E8;stroke:#C6C6C6;stroke-miterlimit:10;}
	.st204{fill:#FFFFFF;stroke:#C6C6C6;stroke-miterlimit:10;}
	.st205{fill:#BFBFBF;}
	.st206{fill:none;stroke:#929292;stroke-width:1.0006;stroke-miterlimit:10;}
	.st207{fill:none;stroke:#939393;stroke-width:2;stroke-miterlimit:10;}
	.st208{fill:#E8E8E8;stroke:#C6C6C6;stroke-width:1.0171;stroke-miterlimit:10;}
	.st209{fill:#FFFFFF;stroke:#C6C6C6;stroke-width:1.0171;stroke-miterlimit:10;}
	.st210{fill:none;stroke:#C6C6C6;stroke-width:1.0171;stroke-miterlimit:10;}
	.st211{fill:none;stroke:#929292;stroke-width:0.9465;stroke-miterlimit:10;}
	.st212{fill:#FFFFFF;stroke:#C6C6C6;stroke-width:0.9459;stroke-miterlimit:10;}
	.st213{fill:none;stroke:#939393;stroke-width:1.8919;stroke-miterlimit:10;}
	.st214{fill:none;stroke:#EAEAEA;stroke-width:0.6078;stroke-miterlimit:10;}
	.st215{fill:none;stroke:#CCCCCC;stroke-width:1.5;stroke-miterlimit:10;}
	.st216{fill:#B3B3B3;}
	.st217{fill:#FFFFFF;stroke:#000000;stroke-width:0.25;stroke-miterlimit:10;}
	.st218{clip-path:url(#SVGID_134_);}
	.st219{clip-path:url(#SVGID_136_);}
	.st220{clip-path:url(#SVGID_138_);}
	.st221{fill:#F5F5F5;}
	.st222{font-family:'Lato-Light';}
	.st223{font-size:8.3545px;}
	.st224{fill:none;stroke:#CECECE;stroke-width:0.413;stroke-miterlimit:10;}
	.st225{font-family:'Lato-Regular';}
	.st226{font-size:15.4912px;}
	.st227{font-size:14.3437px;}
	.st228{fill:none;stroke:#CCCCCC;stroke-width:0.8606;stroke-miterlimit:10;}
	.st229{font-size:16.8114px;}
	.st230{font-size:19.4027px;}
	.st231{fill:none;stroke:#000000;stroke-width:0.5553;stroke-miterlimit:10;}
	.st232{fill:none;stroke:#3F3F3F;stroke-width:0.5553;stroke-miterlimit:10;}
	.st233{fill:none;stroke:#5B5B5B;stroke-width:0.5553;stroke-miterlimit:10;}
	.st234{fill:none;stroke:#F47B67;stroke-width:0.5553;stroke-miterlimit:10;}
	.st235{fill:#E8E8E8;stroke:#C6C6C6;stroke-width:0.5553;stroke-miterlimit:10;}
	.st236{fill:#FFFFFF;stroke:#C6C6C6;stroke-width:0.5499;stroke-miterlimit:10;}
	.st237{fill:none;stroke:#929292;stroke-width:0.5557;stroke-miterlimit:10;}
	.st238{fill:#FFFFFF;stroke:#C6C6C6;stroke-width:0.5553;stroke-miterlimit:10;}
	.st239{fill:none;stroke:#939393;stroke-width:1.1106;stroke-miterlimit:10;}
	.st240{fill:none;stroke:#3F3F3F;stroke-width:0.5501;stroke-miterlimit:10;}
	.st241{fill:#E8E8E8;stroke:#C6C6C6;stroke-width:0.5595;stroke-miterlimit:10;}
	.st242{fill:none;stroke:#C6C6C6;stroke-width:0.5595;stroke-miterlimit:10;}
	.st243{fill:none;stroke:#929292;stroke-width:0.5207;stroke-miterlimit:10;}
	.st244{fill:#FFFFFF;stroke:#C6C6C6;stroke-width:0.5204;stroke-miterlimit:10;}
	.st245{fill:none;stroke:#939393;stroke-width:1.0407;stroke-miterlimit:10;}
	.st246{fill:none;stroke:#F47B67;stroke-width:0.5501;stroke-miterlimit:10;}
	.st247{fill:none;stroke:#CECECE;stroke-width:0.3562;stroke-miterlimit:10;}
	.st248{fill:#FFFFFF;stroke:#9B9B9B;stroke-width:0.474;stroke-miterlimit:10;}
	.st249{fill:#FFFFFF;stroke:#9B9B9B;stroke-width:0.4641;stroke-miterlimit:10;}
	.st250{fill:#FFFFFF;stroke:#9B9B9B;stroke-width:0.4611;stroke-miterlimit:10;}
	.st251{fill:#FFFFFF;stroke:#9B9B9B;stroke-width:0.4612;stroke-miterlimit:10;}
	.st252{fill:#FFFFFF;stroke:#9B9B9B;stroke-width:0.4783;stroke-miterlimit:10;}
	.st253{fill:#FFFFFF;stroke:#9B9B9B;stroke-width:0.4741;stroke-miterlimit:10;}
	.st254{fill:#FFFFFF;stroke:#9B9B9B;stroke-width:0.4764;stroke-miterlimit:10;}
	.st255{fill:#FFFFFF;stroke:#9B9B9B;stroke-width:0.4785;stroke-miterlimit:10;}
	.st256{fill:#FFFFFF;stroke:#9B9B9B;stroke-width:0.4207;stroke-miterlimit:10;}
	.st257{fill:#FFFFFF;stroke:#9B9B9B;stroke-width:0.4243;stroke-miterlimit:10;}
	.st258{fill:#FFFFFF;stroke:#9B9B9B;stroke-width:0.4302;stroke-miterlimit:10;}
	.st259{fill:#FFFFFF;stroke:#9B9B9B;stroke-width:0.4799;stroke-miterlimit:10;}
	.st260{fill:#FFFFFF;stroke:#9B9B9B;stroke-width:0.479;stroke-miterlimit:10;}
	.st261{fill:none;stroke:#9B9B9B;stroke-width:0.474;stroke-miterlimit:10;}
</style>
<path class="st142" d="M603.77,329.21h-53.55c-1.78,0-3.22-1.44-3.22-3.22v-64.55c0-1.78,1.44-3.22,3.22-3.22h53.55
	c1.78,0,3.22,1.44,3.22,3.22v64.55C607,327.77,605.55,329.21,603.77,329.21z"/>
<path class="st142" d="M677.77,329.21h-53.55c-1.78,0-3.22-1.44-3.22-3.22v-64.55c0-1.78,1.44-3.22,3.22-3.22h53.55
	c1.78,0,3.22,1.44,3.22,3.22v64.55C681,327.77,679.55,329.21,677.77,329.21z"/>
<path class="st142" d="M751.77,329.21h-53.55c-1.78,0-3.22-1.44-3.22-3.22v-64.55c0-1.78,1.44-3.22,3.22-3.22h53.55
	c1.78,0,3.22,1.44,3.22,3.22v64.55C755,327.77,753.55,329.21,751.77,329.21z"/>
<path class="st142" d="M893.77,329.21h-53.55c-1.78,0-3.22-1.44-3.22-3.22v-64.55c0-1.78,1.44-3.22,3.22-3.22h53.55
	c1.78,0,3.22,1.44,3.22,3.22v64.55C897,327.77,895.55,329.21,893.77,329.21z"/>
<g>
	<circle class="st142" cx="772" cy="321.21" r="3"/>
	<circle class="st142" cx="784" cy="321.21" r="3"/>
	<circle class="st142" cx="796" cy="321.21" r="3"/>
	<circle class="st142" cx="808" cy="321.21" r="3"/>
	<circle class="st142" cx="820" cy="321.21" r="3"/>
</g>
<g>
	<text transform="matrix(1 0 0 1 554.5013 343.7041)" class="st222 st223">CHECKSUM</text>
</g>
<g>
	<text transform="matrix(1 0 0 1 628.3676 343.7041)" class="st222 st223">CHECKSUM</text>
</g>
<g>
	<text transform="matrix(1 0 0 1 702.2328 343.7041)" class="st222 st223">CHECKSUM</text>
</g>
<g>
	<text transform="matrix(1 0 0 1 844.1859 343.7041)" class="st222 st223">CHECKSUM</text>
</g>
<g>
	
		<image style="overflow:visible;opacity:0.15;" width="781" height="82" xlink:href="1E90E58D910B0AAD.png"  transform="matrix(1 0 0 1 127.0155 83.2323)">
	</image>
	<g>
		<rect x="131" y="87.21" class="st0" width="766" height="67"/>
		<rect x="131" y="87.21" class="st224" width="766" height="67"/>
	</g>
</g>
<text transform="matrix(1 0 0 1 356.722 116.5439)"><tspan x="0" y="0" class="st225 st226">OBJECT ERASURE-CODED OVER 16 DRIVES</tspan><tspan x="53.21" y="17.21" class="st222 st227">Tolerates up to any 8 disk failures</tspan></text>
<path class="st228" d="M131.22,228.58c0-15.39,12.48-27.87,27.87-27.87h294.34c15.39,0,27.87,12.48,27.87,27.87"/>
<path class="st216" d="M187.77,331.21h-53.55c-1.78,0-3.22-1.44-3.22-3.22v-64.55c0-1.78,1.44-3.22,3.22-3.22h53.55
	c1.78,0,3.22,1.44,3.22,3.22v64.55C191,329.77,189.55,331.21,187.77,331.21z"/>
<path class="st216" d="M261.77,331.21h-53.55c-1.78,0-3.22-1.44-3.22-3.22v-64.55c0-1.78,1.44-3.22,3.22-3.22h53.55
	c1.78,0,3.22,1.44,3.22,3.22v64.55C265,329.77,263.55,331.21,261.77,331.21z"/>
<path class="st216" d="M335.77,331.21h-53.55c-1.78,0-3.22-1.44-3.22-3.22v-64.55c0-1.78,1.44-3.22,3.22-3.22h53.55
	c1.78,0,3.22,1.44,3.22,3.22v64.55C339,329.77,337.55,331.21,335.77,331.21z"/>
<path class="st216" d="M477.77,331.21h-53.55c-1.78,0-3.22-1.44-3.22-3.22v-64.55c0-1.78,1.44-3.22,3.22-3.22h53.55
	c1.78,0,3.22,1.44,3.22,3.22v64.55C481,329.77,479.55,331.21,477.77,331.21z"/>
<g>
	<text transform="matrix(1 0 0 1 254.4618 235.8027)" class="st222 st229">DATA BLOCK</text>
</g>
<g>
	<circle class="st216" cx="356" cy="323.21" r="3"/>
	<circle class="st216" cx="368" cy="323.21" r="3"/>
	<circle class="st216" cx="380" cy="323.21" r="3"/>
	<circle class="st216" cx="392" cy="323.21" r="3"/>
	<circle class="st216" cx="405" cy="323.21" r="3"/>
</g>
<text transform="matrix(1 0 0 1 155.787 301.1699)" class="st0 st225 st230">1</text>
<text transform="matrix(1 0 0 1 229.6527 301.1699)" class="st0 st225 st230">2</text>
<text transform="matrix(1 0 0 1 303.5179 301.1699)" class="st0 st225 st230">3</text>
<text transform="matrix(1 0 0 1 445.472 301.1699)" class="st0 st225 st230">8</text>
<text transform="matrix(1 0 0 1 138.5419 345.1348)" class="st222 st223">CHECKSUM</text>
<text transform="matrix(1 0 0 1 212.4046 345.1348)" class="st222 st223">CHECKSUM</text>
<text transform="matrix(1 0 0 1 286.2714 345.1348)" class="st222 st223">CHECKSUM</text>
<text transform="matrix(1 0 0 1 428.225 345.1348)" class="st222 st223">CHECKSUM</text>
<path class="st228" d="M547.18,228.58c0-15.39,12.48-27.87,27.86-27.87h294.34c15.39,0,27.87,12.48,27.87,27.87"/>
<g>
	<text transform="matrix(1 0 0 1 665.1688 235.8027)" class="st222 st229">PARITY BLOCK</text>
</g>
<text transform="matrix(1 0 0 1 566.4081 301.1699)" class="st0 st225 st230">1P</text>
<text transform="matrix(1 0 0 1 640.2733 301.1699)" class="st0 st225 st230">2P</text>
<text transform="matrix(1 0 0 1 714.1405 301.1699)" class="st0 st225 st230">3P</text>
<text transform="matrix(1 0 0 1 856.0927 301.1699)" class="st0 st225 st230">8P</text>
</svg>
