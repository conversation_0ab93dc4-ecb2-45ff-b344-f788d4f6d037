{"__inputs": [{"name": "DS_PROMETHEUS", "label": "Prometheus", "description": "", "type": "datasource", "pluginId": "prometheus", "pluginName": "Prometheus"}], "__requires": [{"type": "panel", "id": "bargauge", "name": "Bar gauge", "version": ""}, {"type": "panel", "id": "gauge", "name": "Gauge", "version": ""}, {"type": "grafana", "id": "grafana", "name": "<PERSON><PERSON>", "version": "8.0.6"}, {"type": "panel", "id": "graph", "name": "Graph", "version": ""}, {"type": "datasource", "id": "prometheus", "name": "Prometheus", "version": "1.0.0"}, {"type": "panel", "id": "stat", "name": "Stat", "version": ""}], "annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "description": "MinIO dashboard - https://min.io/", "editable": true, "gnetId": 13502, "graphTooltip": 0, "id": null, "iteration": 1629787190164, "links": [{"icon": "external link", "includeVars": true, "keepTime": true, "tags": ["minio"], "type": "dashboards"}], "panels": [{"cacheTimeout": null, "datasource": "${DS_PROMETHEUS}", "description": "", "fieldConfig": {"defaults": {"mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "percentage", "steps": [{"color": "green", "value": null}]}, "unit": "dtdurations"}, "overrides": []}, "gridPos": {"h": 6, "w": 3, "x": 0, "y": 0}, "id": 1, "interval": null, "links": [], "maxDataPoints": 100, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["mean"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "8.0.6", "targets": [{"exemplar": true, "expr": "time() - max(minio_node_process_starttime_seconds{job=\"$scrape_jobs\"})", "format": "time_series", "instant": true, "interval": "", "intervalFactor": 1, "legendFormat": "{{instance}}", "metric": "process_start_time_seconds", "refId": "A", "step": 60}], "timeFrom": null, "timeShift": null, "title": "Uptime", "type": "stat"}, {"cacheTimeout": null, "datasource": "${DS_PROMETHEUS}", "description": "", "fieldConfig": {"defaults": {"mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 3, "w": 3, "x": 3, "y": 0}, "id": 65, "interval": null, "links": [], "maxDataPoints": 100, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["last"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "8.0.6", "targets": [{"exemplar": true, "expr": "sum by (instance) (minio_s3_traffic_received_bytes{job=\"$scrape_jobs\"})", "format": "table", "hide": false, "instant": false, "interval": "", "intervalFactor": 1, "legendFormat": "{{instance}}", "metric": "process_start_time_seconds", "refId": "A", "step": 60}], "timeFrom": null, "timeShift": null, "title": "Total S3 Traffic Inbound", "type": "stat"}, {"cacheTimeout": null, "datasource": "${DS_PROMETHEUS}", "fieldConfig": {"defaults": {"mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "rgba(255, 255, 255, 0.97)", "value": null}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 6, "w": 3, "x": 6, "y": 0}, "id": 50, "interval": "1m", "links": [], "maxDataPoints": 100, "options": {"orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true, "text": {}}, "pluginVersion": "8.0.6", "targets": [{"exemplar": true, "expr": "topk(1, sum(minio_cluster_capacity_usable_free_bytes{job=\"$scrape_jobs\"}) by (instance))", "format": "time_series", "instant": false, "interval": "1m", "intervalFactor": 1, "legendFormat": "", "refId": "A", "step": 300}], "timeFrom": null, "timeShift": null, "title": "Current Usable Capacity", "type": "gauge"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${DS_PROMETHEUS}", "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 7, "x": 9, "y": 0}, "hiddenSeries": false, "id": 68, "legend": {"avg": false, "current": true, "max": false, "min": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.0.6", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "sum(minio_bucket_usage_total_bytes{job=\"$scrape_jobs\"}) by (instance)", "interval": "", "legendFormat": "Used Capacity", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Data Usage Growth", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:419", "format": "bytes", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:420", "format": "bytes", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"datasource": "${DS_PROMETHEUS}", "fieldConfig": {"defaults": {"mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "semi-dark-red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 6, "w": 5, "x": 16, "y": 0}, "id": 52, "links": [], "options": {"displayMode": "basic", "orientation": "horizontal", "reduceOptions": {"calcs": ["mean"], "fields": "", "values": false}, "showUnfilled": false, "text": {}}, "pluginVersion": "8.0.6", "targets": [{"exemplar": true, "expr": "max by (range) (minio_bucket_objects_size_distribution{job=\"$scrape_jobs\"})", "format": "time_series", "instant": false, "interval": "", "intervalFactor": 1, "legendFormat": "{{range}}", "refId": "A", "step": 300}], "timeFrom": null, "timeShift": null, "title": "Object size distribution", "type": "bargauge"}, {"cacheTimeout": null, "datasource": "${DS_PROMETHEUS}", "description": "", "fieldConfig": {"defaults": {"mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 3, "w": 3, "x": 21, "y": 0}, "id": 61, "interval": null, "links": [], "maxDataPoints": 100, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["last"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "8.0.6", "targets": [{"exemplar": true, "expr": "sum (minio_node_file_descriptor_open_total{job=\"$scrape_jobs\"})", "format": "table", "hide": false, "instant": false, "interval": "", "intervalFactor": 1, "legendFormat": "", "metric": "process_start_time_seconds", "refId": "A", "step": 60}], "timeFrom": null, "timeShift": null, "title": "Total Open FDs", "type": "stat"}, {"cacheTimeout": null, "datasource": "${DS_PROMETHEUS}", "description": "", "fieldConfig": {"defaults": {"mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 3, "w": 3, "x": 3, "y": 3}, "id": 64, "interval": null, "links": [], "maxDataPoints": 100, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["last"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "8.0.6", "targets": [{"exemplar": true, "expr": "sum by (instance) (minio_s3_traffic_sent_bytes{job=\"$scrape_jobs\"})", "format": "table", "hide": false, "instant": false, "interval": "", "intervalFactor": 1, "legendFormat": "", "metric": "process_start_time_seconds", "refId": "A", "step": 60}], "timeFrom": null, "timeShift": null, "title": "Total S3 Traffic Outbound", "type": "stat"}, {"cacheTimeout": null, "datasource": "${DS_PROMETHEUS}", "description": "", "fieldConfig": {"defaults": {"mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 3, "w": 3, "x": 21, "y": 3}, "id": 62, "interval": null, "links": [], "maxDataPoints": 100, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["last"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "8.0.6", "targets": [{"exemplar": true, "expr": "sum without (server,instance) (minio_node_go_routine_total{job=\"$scrape_jobs\"})", "format": "table", "hide": false, "instant": false, "interval": "", "intervalFactor": 1, "legendFormat": "", "metric": "process_start_time_seconds", "refId": "A", "step": 60}], "timeFrom": null, "timeShift": null, "title": "Total Goroutines", "type": "stat"}, {"cacheTimeout": null, "datasource": "${DS_PROMETHEUS}", "description": "", "fieldConfig": {"defaults": {"mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 2, "w": 3, "x": 0, "y": 6}, "id": 53, "interval": null, "links": [], "maxDataPoints": 100, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["mean"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "8.0.6", "targets": [{"exemplar": true, "expr": "minio_cluster_nodes_online_total{job=\"$scrape_jobs\"}", "format": "table", "hide": false, "instant": true, "interval": "", "intervalFactor": 1, "legendFormat": "", "metric": "process_start_time_seconds", "refId": "A", "step": 60}], "timeFrom": null, "timeShift": null, "title": "Total Online Servers", "type": "stat"}, {"cacheTimeout": null, "datasource": "${DS_PROMETHEUS}", "description": "", "fieldConfig": {"defaults": {"mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 2, "w": 3, "x": 3, "y": 6}, "id": 9, "interval": null, "links": [], "maxDataPoints": 100, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["mean"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "8.0.6", "targets": [{"exemplar": true, "expr": "minio_cluster_disk_online_total{job=\"$scrape_jobs\"}", "format": "table", "hide": false, "instant": true, "interval": "", "intervalFactor": 1, "legendFormat": "Total online disks in MinIO Cluster", "metric": "process_start_time_seconds", "refId": "A", "step": 60}], "timeFrom": null, "timeShift": null, "title": "Total Online Disks", "type": "stat"}, {"cacheTimeout": null, "datasource": "${DS_PROMETHEUS}", "fieldConfig": {"defaults": {"mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "dark-yellow", "value": 75000000}, {"color": "dark-red", "value": 100000000}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 3, "w": 3, "x": 6, "y": 6}, "id": 66, "interval": null, "links": [], "maxDataPoints": 100, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "8.0.6", "targets": [{"exemplar": true, "expr": "count(count by (bucket) (minio_bucket_usage_total_bytes{job=\"$scrape_jobs\"}))", "format": "time_series", "instant": false, "interval": "1m", "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "title": "Number of Buckets", "type": "stat"}, {"aliasColors": {"S3 Errors": "light-red", "S3 Requests": "light-green"}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${DS_PROMETHEUS}", "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 7, "x": 9, "y": 6}, "hiddenSeries": false, "id": 63, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.0.6", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "sum by (server) (rate(minio_s3_traffic_received_bytes{job=\"$scrape_jobs\"}[$__rate_interval]))", "interval": "1m", "intervalFactor": 2, "legendFormat": "Data Received [{{server}}]", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "S3 API Data Received Rate ", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:331", "format": "bytes", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:332", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {"S3 Errors": "light-red", "S3 Requests": "light-green"}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${DS_PROMETHEUS}", "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 8, "x": 16, "y": 6}, "hiddenSeries": false, "id": 70, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.0.6", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "sum by (server) (rate(minio_s3_traffic_sent_bytes{job=\"$scrape_jobs\"}[$__rate_interval]))", "interval": "1m", "intervalFactor": 2, "legendFormat": "Data Sent [{{server}}]", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "S3 API Data Sent Rate ", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:331", "format": "bytes", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:332", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"cacheTimeout": null, "datasource": "${DS_PROMETHEUS}", "description": "", "fieldConfig": {"defaults": {"mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 2, "w": 3, "x": 0, "y": 8}, "id": 69, "interval": null, "links": [], "maxDataPoints": 100, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["mean"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "8.0.6", "targets": [{"exemplar": true, "expr": "minio_cluster_nodes_offline_total{job=\"$scrape_jobs\"}", "format": "table", "hide": false, "instant": true, "interval": "", "intervalFactor": 1, "legendFormat": "", "metric": "process_start_time_seconds", "refId": "A", "step": 60}], "timeFrom": null, "timeShift": null, "title": "Total Offline Servers", "type": "stat"}, {"cacheTimeout": null, "datasource": "${DS_PROMETHEUS}", "description": "", "fieldConfig": {"defaults": {"mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 2, "w": 3, "x": 3, "y": 8}, "id": 78, "interval": null, "links": [], "maxDataPoints": 100, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["mean"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "8.0.6", "targets": [{"exemplar": true, "expr": "minio_cluster_disk_offline_total{job=\"$scrape_jobs\"}", "format": "table", "hide": false, "instant": true, "interval": "", "intervalFactor": 1, "legendFormat": "", "metric": "process_start_time_seconds", "refId": "A", "step": 60}], "timeFrom": null, "timeShift": null, "title": "Total Offline Disks", "type": "stat"}, {"cacheTimeout": null, "datasource": "${DS_PROMETHEUS}", "fieldConfig": {"defaults": {"mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "dark-yellow", "value": 75000000}, {"color": "dark-red", "value": 100000000}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 3, "w": 3, "x": 6, "y": 9}, "id": 44, "interval": null, "links": [], "maxDataPoints": 100, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "8.0.6", "targets": [{"exemplar": true, "expr": "topk(1, sum(minio_bucket_usage_object_total{job=\"$scrape_jobs\"}) by (instance))", "format": "time_series", "instant": false, "interval": "1m", "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "title": "Number of Objects", "type": "stat"}, {"cacheTimeout": null, "datasource": "${DS_PROMETHEUS}", "description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "ns"}, "overrides": []}, "gridPos": {"h": 2, "w": 3, "x": 0, "y": 10}, "id": 80, "interval": null, "links": [], "maxDataPoints": 100, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["last"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "8.0.6", "targets": [{"exemplar": true, "expr": "minio_heal_time_last_activity_nano_seconds{job=\"$scrape_jobs\"}", "format": "time_series", "instant": true, "interval": "", "intervalFactor": 1, "legendFormat": "{{server}}", "metric": "process_start_time_seconds", "refId": "A", "step": 60}], "timeFrom": null, "timeShift": null, "title": "Time Since Last Heal Activity", "type": "stat"}, {"cacheTimeout": null, "datasource": "${DS_PROMETHEUS}", "description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "ns"}, "overrides": []}, "gridPos": {"h": 2, "w": 3, "x": 3, "y": 10}, "id": 81, "interval": null, "links": [], "maxDataPoints": 100, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["last"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "8.0.6", "targets": [{"exemplar": true, "expr": "minio_usage_last_activity_nano_seconds{job=\"$scrape_jobs\"}", "format": "time_series", "instant": true, "interval": "", "intervalFactor": 1, "legendFormat": "{{server}}", "metric": "process_start_time_seconds", "refId": "A", "step": 60}], "timeFrom": null, "timeShift": null, "title": "Time Since Last Scan Activity", "type": "stat"}, {"aliasColors": {"S3 Errors": "light-red", "S3 Requests": "light-green"}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${DS_PROMETHEUS}", "fill": 1, "fillGradient": 0, "gridPos": {"h": 10, "w": 12, "x": 0, "y": 12}, "hiddenSeries": false, "id": 60, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.0.6", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "sum by (server,api) (increase(minio_s3_requests_total{job=\"$scrape_jobs\"}[$__rate_interval]))", "interval": "1m", "intervalFactor": 2, "legendFormat": "{{server,api}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "S3 API Request Rate", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:331", "format": "none", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:332", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {"S3 Errors": "light-red", "S3 Requests": "light-green"}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${DS_PROMETHEUS}", "fill": 1, "fillGradient": 0, "gridPos": {"h": 10, "w": 12, "x": 12, "y": 12}, "hiddenSeries": false, "id": 71, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.0.6", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "sum by (server,api) (increase(minio_s3_requests_errors_total{job=\"$scrape_jobs\"}[$__rate_interval]))", "interval": "1m", "intervalFactor": 2, "legendFormat": "{{server,api}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "S3 API Request Error Rate", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:331", "format": "none", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:332", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {"**********:9000 DELETE": "red", "**********:9000 GET": "green", "**********:9000 POST": "blue"}, "bars": true, "dashLength": 10, "dashes": false, "datasource": "${DS_PROMETHEUS}", "description": "Total number of bytes received and sent among all MinIO server instances", "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 10, "fillGradient": 1, "gridPos": {"h": 9, "w": 12, "x": 0, "y": 22}, "hiddenSeries": false, "id": 17, "legend": {"avg": false, "current": false, "max": false, "min": false, "rightSide": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.0.6", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "rate(minio_inter_node_traffic_sent_bytes{job=\"$scrape_jobs\"}[$__rate_interval])", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "Internode Bytes Received [{{server}}]", "metric": "minio_http_requests_duration_seconds_count", "refId": "A", "step": 4}, {"exemplar": true, "expr": "rate(minio_inter_node_traffic_received_bytes{job=\"$scrape_jobs\"}[$__rate_interval])", "interval": "", "legendFormat": "Internode Bytes Sent [{{server}}]", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Internode Data Transfer", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:211", "format": "bytes", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:212", "format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${DS_PROMETHEUS}", "fill": 1, "fillGradient": 0, "gridPos": {"h": 9, "w": 12, "x": 12, "y": 22}, "hiddenSeries": false, "id": 84, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.0.6", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "sum by (instance) (minio_heal_objects_heal_total{job=\"$scrape_jobs\"})", "interval": "", "legendFormat": "Objects healed in current self heal run", "refId": "A"}, {"exemplar": true, "expr": "sum by (instance) (minio_heal_objects_error_total{job=\"$scrape_jobs\"})", "hide": false, "interval": "", "legendFormat": "Heal errors in current self heal run", "refId": "B"}, {"exemplar": true, "expr": "sum by (instance) (minio_heal_objects_total{job=\"$scrape_jobs\"}) ", "hide": false, "interval": "", "legendFormat": "Objects scanned in current self heal run", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Healing", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:846", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:847", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": "${DS_PROMETHEUS}", "fill": 1, "fillGradient": 0, "gridPos": {"h": 9, "w": 12, "x": 0, "y": 31}, "hiddenSeries": false, "id": 77, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.0.6", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "rate(minio_node_process_cpu_total_seconds{job=\"$scrape_jobs\"}[$__rate_interval])", "interval": "", "legendFormat": "CPU Usage Rate [{{server}}]", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Node CPU Usage", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1043", "format": "none", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:1044", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${DS_PROMETHEUS}", "fill": 1, "fillGradient": 0, "gridPos": {"h": 9, "w": 12, "x": 12, "y": 31}, "hiddenSeries": false, "id": 76, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.0.6", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "minio_node_process_resident_memory_bytes{job=\"$scrape_jobs\"}", "interval": "", "legendFormat": "Memory Used [{{server}}]", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Node Memory Usage", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1043", "format": "bytes", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:1044", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${DS_PROMETHEUS}", "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 40}, "hiddenSeries": false, "id": 74, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.0.6", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "minio_node_disk_used_bytes{job=\"$scrape_jobs\"}", "format": "time_series", "instant": false, "interval": "", "legendFormat": "Used Capacity [{{server}}:{{disk}}]", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Drive Used Capacity", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:381", "format": "bytes", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:382", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${DS_PROMETHEUS}", "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 40}, "hiddenSeries": false, "id": 82, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.0.6", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "minio_cluster_disk_free_inodes{job=\"$scrape_jobs\"}", "format": "time_series", "instant": false, "interval": "", "legendFormat": "Free Inodes [{{server}}:{{disk}}]", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Drives Free Inodes", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:381", "format": "none", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:382", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {"Offline **********:9000": "dark-red", "Total **********:9000": "blue"}, "bars": true, "cacheTimeout": null, "dashLength": 10, "dashes": false, "datasource": "${DS_PROMETHEUS}", "description": "Number of online disks per MinIO Server", "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 9, "w": 12, "x": 0, "y": 48}, "hiddenSeries": false, "id": 11, "legend": {"avg": false, "current": false, "max": false, "min": false, "rightSide": false, "show": true, "total": false, "values": false}, "lines": false, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.0.6", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "rate(minio_node_syscall_read_total{job=\"$scrape_jobs\"}[$__rate_interval])", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "Read Syscalls [{{server}}]", "metric": "process_start_time_seconds", "refId": "A", "step": 60}, {"exemplar": true, "expr": "rate(minio_node_syscall_write_total{job=\"$scrape_jobs\"}[$__rate_interval])", "interval": "", "legendFormat": "Write Syscalls [{{server}}]", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Node Syscalls", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:185", "decimals": 0, "format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:186", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {"available **********:9000": "green", "used **********:9000": "blue"}, "bars": false, "cacheTimeout": null, "dashLength": 10, "dashes": false, "datasource": "${DS_PROMETHEUS}", "description": "", "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 9, "w": 12, "x": 12, "y": 48}, "hiddenSeries": false, "id": 8, "legend": {"avg": false, "current": false, "max": false, "min": false, "rightSide": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.0.6", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "minio_node_file_descriptor_open_total{job=\"$scrape_jobs\"}", "interval": "", "legendFormat": "Open FDs [{{server}}]", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Node File Descriptors", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:212", "decimals": null, "format": "none", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:213", "format": "none", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": "${DS_PROMETHEUS}", "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 24, "x": 0, "y": 57}, "hiddenSeries": false, "id": 73, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.0.6", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "rate(minio_node_io_rchar_bytes{job=\"$scrape_jobs\"}[$__rate_interval])", "format": "time_series", "instant": false, "interval": "", "legendFormat": "Node RChar [{{server}}]", "refId": "A"}, {"exemplar": true, "expr": "rate(minio_node_io_wchar_bytes{job=\"$scrape_jobs\"}[$__rate_interval])", "interval": "", "legendFormat": "Node WChar [{{server}}]", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Node IO", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:381", "format": "bytes", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:382", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}], "refresh": "10s", "schemaVersion": 30, "style": "dark", "tags": ["minio"], "templating": {"list": [{"allValue": null, "current": {}, "datasource": "${DS_PROMETHEUS}", "definition": "label_values(job)", "description": null, "error": null, "hide": 0, "includeAll": true, "label": null, "multi": true, "name": "scrape_jobs", "options": [], "query": {"query": "label_values(job)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}]}, "time": {"from": "now-3h", "to": "now"}, "timepicker": {"refresh_intervals": ["10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"], "time_options": ["5m", "15m", "1h", "6h", "12h", "24h", "2d", "7d", "30d"]}, "timezone": "", "title": "MinIO Overview", "uid": "TgmJnqnnk", "version": 18}