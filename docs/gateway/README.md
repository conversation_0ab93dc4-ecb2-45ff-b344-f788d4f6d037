# MinIO Gateway [![Slack](https://slack.min.io/slack?type=svg)](https://slack.min.io)

## Support

Gateway implementations are frozen and are not accepting any new features. Please reports any bugs at <https://github.com/minio/minio/issues> . If you are an existing customer please login to <https://subnet.min.io> for production support.

## Implementations

MinIO Gateway adds Amazon S3 compatibility layer to third party NAS and Cloud Storage vendors. MinIO Gateway is implemented to facilitate migration of existing from your existing legacy or cloud vendors to MinIO distributed server deployments.

- [NAS](https://github.com/minio/minio/blob/master/docs/gateway/nas.md)
- [S3](https://github.com/minio/minio/blob/master/docs/gateway/s3.md)
- [Microsoft Azure Blob Storage](https://github.com/minio/minio/blob/master/docs/gateway/azure.md)
- [Google Cloud Storage](https://github.com/minio/minio/blob/master/docs/gateway/gcs.md)
- [HDFS](https://github.com/minio/minio/blob/master/docs/gateway/hdfs.md)
