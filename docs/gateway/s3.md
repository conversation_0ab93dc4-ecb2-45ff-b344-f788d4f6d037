# MinIO S3 Gateway [![Slack](https://slack.min.io/slack?type=svg)](https://slack.min.io)

MinIO S3 Gateway adds MinIO features like MinIO Console and disk caching to AWS S3 or any other AWS S3 compatible service.

## Support

Gateway implementations are frozen and are not accepting any new features. Please reports any bugs at <https://github.com/minio/minio/issues> . If you are an existing customer please login to <https://subnet.min.io> for production support.

## Run MinIO Gateway for AWS S3

As a prerequisite to run MinIO S3 gateway, you need valid AWS S3 access key and secret key by default. Optionally you can also set custom access/secret key, when you have rotating AWS IAM credentials or AWS credentials through environment variables (i.e. AWS_ACCESS_KEY_ID)

### Using Docker

```
podman run \
 -p 9000:9000 \
 -p 9001:9001 \
 --name minio-s3 \
 -e "MINIO_ROOT_USER=aws_s3_access_key" \
 -e "MINIO_ROOT_PASSWORD=aws_s3_secret_key" \
 quay.io/minio/minio gateway s3 --console-address ":9001"
```

### Using Binary

```
export MINIO_ROOT_USER=aws_s3_access_key
export MINIO_ROOT_PASSWORD=aws_s3_secret_key
minio gateway s3
```

### Using Binary in EC2

Using IAM rotating credentials for AWS S3

If you are using an S3 enabled IAM role on an EC2 instance for S3 access, MinIO will still require env vars MINIO_ROOT_USER and MINIO_ROOT_PASSWORD to be set for its internal use. These may be set to any value which meets the length requirements. Access key length should be at least 3, and secret key length at least 8 characters.

```
export MINIO_ROOT_USER=custom_access_key
export MINIO_ROOT_PASSWORD=custom_secret_key
minio gateway s3
```

MinIO gateway will automatically look for list of credential styles in following order, if your backend URL is AWS S3.

- AWS env vars (i.e. AWS_ACCESS_KEY_ID)
- AWS creds file (i.e. AWS_SHARED_CREDENTIALS_FILE or ~/.aws/credentials)
- IAM profile based credentials. (performs an HTTP call to a pre-defined endpoint, only valid inside configured ec2 instances)

Minimum permissions required if you wish to provide restricted access with your AWS credentials, please make sure you have following IAM policies attached for your AWS user or roles.

```json
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "s3:GetBucketLocation"
             ],
             "Resource": [
                 "arn:aws:s3:::*"
             ]
        },
        {
            "Effect": "Allow",
            "Action": [
                "s3:PutObject",
                "s3:GetObject",
                "s3:ListBucket",
                "s3:DeleteObject",
                "s3:GetBucketAcl",
            ],
            "Resource": [
                "arn:aws:s3:::mybucket",
                "arn:aws:s3:::mybucket/*"
            ]
        }
    ]
}
```

## Run MinIO Gateway for AWS S3 compatible services

As a prerequisite to run MinIO S3 gateway on an AWS S3 compatible service, you need valid access key, secret key and service endpoint.

## Run MinIO Gateway with double-encryption

MinIO gateway to S3 supports encryption of data at rest. Three types of encryption modes are supported

- encryption can be set to ``pass-through`` to backend only for SSE-S3, SSE-C is not allowed passthrough.
- ``single encryption`` (at the gateway)
- ``double encryption`` (single encryption at gateway and pass through to backend)

This can be specified by setting MINIO_GATEWAY_SSE environment variable. If MINIO_GATEWAY_SSE and KMS are not setup, all encryption headers are passed through to the backend. If KMS environment variables are set up, ``single encryption`` is automatically performed at the gateway and encrypted object is saved at the backend.

To specify ``double encryption``, MINIO_GATEWAY_SSE environment variable needs to be set to "s3" for sse-s3
and "c" for sse-c encryption. More than one encryption option can be set, delimited by ";". Objects are encrypted at the gateway and the gateway also does a pass-through to backend. Note that in the case of SSE-C encryption, gateway derives a unique SSE-C key for pass through from the SSE-C client key using a key derivation function (KDF).

```sh
curl -sSL --tlsv1.2 \
     -O 'https://raw.githubusercontent.com/minio/kes/master/root.key' \
     -O 'https://raw.githubusercontent.com/minio/kes/master/root.cert'
```

```sh
export MINIO_GATEWAY_SSE="s3;c"
export MINIO_KMS_KES_ENDPOINT=https://play.min.io:7373
export MINIO_KMS_KES_KEY_FILE=root.key
export MINIO_KMS_KES_CERT_FILE=root.cert
export MINIO_KMS_KES_KEY_NAME=my-minio-key
minio gateway s3
```

### Using Docker (double encryption)

```
podman run -p 9000:9000 --name minio-s3 \
 -e "MINIO_ROOT_USER=access_key" \
 -e "MINIO_ROOT_PASSWORD=secret_key" \
 quay.io/minio/minio gateway s3 https://s3_compatible_service_endpoint:port
```

### Using Binary (double encryption)

```
export MINIO_ROOT_USER=access_key
export MINIO_ROOT_PASSWORD=secret_key
minio gateway s3 https://s3_compatible_service_endpoint:port
```

## MinIO Caching

MinIO edge caching allows storing content closer to the applications. Frequently accessed objects are stored in a local disk based cache. Edge caching with MinIO gateway feature allows

- Dramatic improvements for time to first byte for any object.
- Avoid S3 [data transfer charges](https://aws.amazon.com/s3/pricing/).

Refer [this document](https://docs.min.io/docs/minio-disk-cache-guide.html) to get started with MinIO Caching.

## MinIO Console

MinIO Gateway comes with an embedded web based object browser. Point your web browser to <http://127.0.0.1:9000> to ensure that your server has started successfully.

| Dashboard                                                                                   | Creating a bucket                                                                           |
| -------------                                                                               | -------------                                                                               |
| ![Dashboard](https://github.com/minio/minio/blob/master/docs/screenshots/pic1.png?raw=true) | ![Dashboard](https://github.com/minio/minio/blob/master/docs/screenshots/pic2.png?raw=true) |

With MinIO S3 gateway, you can use MinIO Console to explore AWS S3 based objects.

### Known limitations

- Bucket Notification APIs are not supported.
- Bucket Locking APIs are not supported.
- Versioned buckets on AWS S3 are not supported from gateway layer. Gateway is meant to be used with non-versioned buckets.

## Explore Further

- [`mc` command-line interface](https://docs.min.io/docs/minio-client-quickstart-guide)
- [`aws` command-line interface](https://docs.min.io/docs/aws-cli-with-minio)
- [`minio-go` Go SDK](https://docs.min.io/docs/golang-client-quickstart-guide)
