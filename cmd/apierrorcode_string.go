// Code generated by "stringer -type=APIErrorCode -trimprefix=Err api-errors.go"; DO NOT EDIT.

package cmd

import "strconv"

func _() {
	// An "invalid array index" compiler error signifies that the constant values have changed.
	// Re-run the stringer command to generate them again.
	var x [1]struct{}
	_ = x[ErrNone-0]
	_ = x[ErrAccessDenied-1]
	_ = x[ErrBadDigest-2]
	_ = x[ErrEntityTooSmall-3]
	_ = x[ErrEntityTooLarge-4]
	_ = x[ErrPolicyTooLarge-5]
	_ = x[ErrIncompleteBody-6]
	_ = x[ErrInternalError-7]
	_ = x[ErrInvalidAccessKeyID-8]
	_ = x[ErrAccessKeyDisabled-9]
	_ = x[ErrInvalidBucketName-10]
	_ = x[ErrInvalidDigest-11]
	_ = x[ErrInvalidRange-12]
	_ = x[ErrInvalidRangePartNumber-13]
	_ = x[ErrInvalidCopyPartRange-14]
	_ = x[ErrInvalidCopyPartRangeSource-15]
	_ = x[ErrInvalidMaxKeys-16]
	_ = x[ErrInvalidEncodingMethod-17]
	_ = x[ErrInvalidMaxUploads-18]
	_ = x[ErrInvalidMaxParts-19]
	_ = x[ErrInvalidPartNumberMarker-20]
	_ = x[ErrInvalidPartNumber-21]
	_ = x[ErrInvalidRequestBody-22]
	_ = x[ErrInvalidCopySource-23]
	_ = x[ErrInvalidMetadataDirective-24]
	_ = x[ErrInvalidCopyDest-25]
	_ = x[ErrInvalidPolicyDocument-26]
	_ = x[ErrInvalidObjectState-27]
	_ = x[ErrMalformedXML-28]
	_ = x[ErrMissingContentLength-29]
	_ = x[ErrMissingContentMD5-30]
	_ = x[ErrMissingRequestBodyError-31]
	_ = x[ErrMissingSecurityHeader-32]
	_ = x[ErrNoSuchBucket-33]
	_ = x[ErrNoSuchBucketPolicy-34]
	_ = x[ErrNoSuchBucketLifecycle-35]
	_ = x[ErrNoSuchLifecycleConfiguration-36]
	_ = x[ErrInvalidLifecycleWithObjectLock-37]
	_ = x[ErrNoSuchBucketSSEConfig-38]
	_ = x[ErrNoSuchCORSConfiguration-39]
	_ = x[ErrNoSuchWebsiteConfiguration-40]
	_ = x[ErrReplicationConfigurationNotFoundError-41]
	_ = x[ErrRemoteDestinationNotFoundError-42]
	_ = x[ErrReplicationDestinationMissingLock-43]
	_ = x[ErrRemoteTargetNotFoundError-44]
	_ = x[ErrReplicationRemoteConnectionError-45]
	_ = x[ErrReplicationBandwidthLimitError-46]
	_ = x[ErrBucketRemoteIdenticalToSource-47]
	_ = x[ErrBucketRemoteAlreadyExists-48]
	_ = x[ErrBucketRemoteLabelInUse-49]
	_ = x[ErrBucketRemoteArnTypeInvalid-50]
	_ = x[ErrBucketRemoteArnInvalid-51]
	_ = x[ErrBucketRemoteRemoveDisallowed-52]
	_ = x[ErrRemoteTargetNotVersionedError-53]
	_ = x[ErrReplicationSourceNotVersionedError-54]
	_ = x[ErrReplicationNeedsVersioningError-55]
	_ = x[ErrReplicationBucketNeedsVersioningError-56]
	_ = x[ErrReplicationDenyEditError-57]
	_ = x[ErrReplicationNoMatchingRuleError-58]
	_ = x[ErrObjectRestoreAlreadyInProgress-59]
	_ = x[ErrNoSuchKey-60]
	_ = x[ErrNoSuchUpload-61]
	_ = x[ErrInvalidVersionID-62]
	_ = x[ErrNoSuchVersion-63]
	_ = x[ErrNotImplemented-64]
	_ = x[ErrPreconditionFailed-65]
	_ = x[ErrRequestTimeTooSkewed-66]
	_ = x[ErrSignatureDoesNotMatch-67]
	_ = x[ErrMethodNotAllowed-68]
	_ = x[ErrInvalidPart-69]
	_ = x[ErrInvalidPartOrder-70]
	_ = x[ErrAuthorizationHeaderMalformed-71]
	_ = x[ErrMalformedPOSTRequest-72]
	_ = x[ErrPOSTFileRequired-73]
	_ = x[ErrSignatureVersionNotSupported-74]
	_ = x[ErrBucketNotEmpty-75]
	_ = x[ErrAllAccessDisabled-76]
	_ = x[ErrMalformedPolicy-77]
	_ = x[ErrMissingFields-78]
	_ = x[ErrMissingCredTag-79]
	_ = x[ErrCredMalformed-80]
	_ = x[ErrInvalidRegion-81]
	_ = x[ErrInvalidServiceS3-82]
	_ = x[ErrInvalidServiceSTS-83]
	_ = x[ErrInvalidRequestVersion-84]
	_ = x[ErrMissingSignTag-85]
	_ = x[ErrMissingSignHeadersTag-86]
	_ = x[ErrMalformedDate-87]
	_ = x[ErrMalformedPresignedDate-88]
	_ = x[ErrMalformedCredentialDate-89]
	_ = x[ErrMalformedCredentialRegion-90]
	_ = x[ErrMalformedExpires-91]
	_ = x[ErrNegativeExpires-92]
	_ = x[ErrAuthHeaderEmpty-93]
	_ = x[ErrExpiredPresignRequest-94]
	_ = x[ErrRequestNotReadyYet-95]
	_ = x[ErrUnsignedHeaders-96]
	_ = x[ErrMissingDateHeader-97]
	_ = x[ErrInvalidQuerySignatureAlgo-98]
	_ = x[ErrInvalidQueryParams-99]
	_ = x[ErrBucketAlreadyOwnedByYou-100]
	_ = x[ErrInvalidDuration-101]
	_ = x[ErrBucketAlreadyExists-102]
	_ = x[ErrMetadataTooLarge-103]
	_ = x[ErrUnsupportedMetadata-104]
	_ = x[ErrMaximumExpires-105]
	_ = x[ErrSlowDown-106]
	_ = x[ErrInvalidPrefixMarker-107]
	_ = x[ErrBadRequest-108]
	_ = x[ErrKeyTooLongError-109]
	_ = x[ErrInvalidBucketObjectLockConfiguration-110]
	_ = x[ErrObjectLockConfigurationNotFound-111]
	_ = x[ErrObjectLockConfigurationNotAllowed-112]
	_ = x[ErrNoSuchObjectLockConfiguration-113]
	_ = x[ErrObjectLocked-114]
	_ = x[ErrInvalidRetentionDate-115]
	_ = x[ErrPastObjectLockRetainDate-116]
	_ = x[ErrUnknownWORMModeDirective-117]
	_ = x[ErrBucketTaggingNotFound-118]
	_ = x[ErrObjectLockInvalidHeaders-119]
	_ = x[ErrInvalidTagDirective-120]
	_ = x[ErrInvalidEncryptionMethod-121]
	_ = x[ErrInsecureSSECustomerRequest-122]
	_ = x[ErrSSEMultipartEncrypted-123]
	_ = x[ErrSSEEncryptedObject-124]
	_ = x[ErrInvalidEncryptionParameters-125]
	_ = x[ErrInvalidSSECustomerAlgorithm-126]
	_ = x[ErrInvalidSSECustomerKey-127]
	_ = x[ErrMissingSSECustomerKey-128]
	_ = x[ErrMissingSSECustomerKeyMD5-129]
	_ = x[ErrSSECustomerKeyMD5Mismatch-130]
	_ = x[ErrInvalidSSECustomerParameters-131]
	_ = x[ErrIncompatibleEncryptionMethod-132]
	_ = x[ErrKMSNotConfigured-133]
	_ = x[ErrKMSKeyNotFoundException-134]
	_ = x[ErrNoAccessKey-135]
	_ = x[ErrInvalidToken-136]
	_ = x[ErrEventNotification-137]
	_ = x[ErrARNNotification-138]
	_ = x[ErrRegionNotification-139]
	_ = x[ErrOverlappingFilterNotification-140]
	_ = x[ErrFilterNameInvalid-141]
	_ = x[ErrFilterNamePrefix-142]
	_ = x[ErrFilterNameSuffix-143]
	_ = x[ErrFilterValueInvalid-144]
	_ = x[ErrOverlappingConfigs-145]
	_ = x[ErrUnsupportedNotification-146]
	_ = x[ErrContentSHA256Mismatch-147]
	_ = x[ErrReadQuorum-148]
	_ = x[ErrWriteQuorum-149]
	_ = x[ErrStorageFull-150]
	_ = x[ErrRequestBodyParse-151]
	_ = x[ErrObjectExistsAsDirectory-152]
	_ = x[ErrInvalidObjectName-153]
	_ = x[ErrInvalidObjectNamePrefixSlash-154]
	_ = x[ErrInvalidResourceName-155]
	_ = x[ErrServerNotInitialized-156]
	_ = x[ErrOperationTimedOut-157]
	_ = x[ErrClientDisconnected-158]
	_ = x[ErrOperationMaxedOut-159]
	_ = x[ErrInvalidRequest-160]
	_ = x[ErrTransitionStorageClassNotFoundError-161]
	_ = x[ErrInvalidStorageClass-162]
	_ = x[ErrBackendDown-163]
	_ = x[ErrMalformedJSON-164]
	_ = x[ErrAdminNoSuchUser-165]
	_ = x[ErrAdminNoSuchGroup-166]
	_ = x[ErrAdminGroupNotEmpty-167]
	_ = x[ErrAdminNoSuchPolicy-168]
	_ = x[ErrAdminInvalidArgument-169]
	_ = x[ErrAdminInvalidAccessKey-170]
	_ = x[ErrAdminInvalidSecretKey-171]
	_ = x[ErrAdminConfigNoQuorum-172]
	_ = x[ErrAdminConfigTooLarge-173]
	_ = x[ErrAdminConfigBadJSON-174]
	_ = x[ErrAdminConfigDuplicateKeys-175]
	_ = x[ErrAdminCredentialsMismatch-176]
	_ = x[ErrInsecureClientRequest-177]
	_ = x[ErrObjectTampered-178]
	_ = x[ErrSiteReplicationInvalidRequest-179]
	_ = x[ErrSiteReplicationPeerResp-180]
	_ = x[ErrSiteReplicationBackendIssue-181]
	_ = x[ErrSiteReplicationServiceAccountError-182]
	_ = x[ErrSiteReplicationBucketConfigError-183]
	_ = x[ErrSiteReplicationBucketMetaError-184]
	_ = x[ErrSiteReplicationIAMError-185]
	_ = x[ErrAdminBucketQuotaExceeded-186]
	_ = x[ErrAdminNoSuchQuotaConfiguration-187]
	_ = x[ErrHealNotImplemented-188]
	_ = x[ErrHealNoSuchProcess-189]
	_ = x[ErrHealInvalidClientToken-190]
	_ = x[ErrHealMissingBucket-191]
	_ = x[ErrHealAlreadyRunning-192]
	_ = x[ErrHealOverlappingPaths-193]
	_ = x[ErrIncorrectContinuationToken-194]
	_ = x[ErrEmptyRequestBody-195]
	_ = x[ErrUnsupportedFunction-196]
	_ = x[ErrInvalidExpressionType-197]
	_ = x[ErrBusy-198]
	_ = x[ErrUnauthorizedAccess-199]
	_ = x[ErrExpressionTooLong-200]
	_ = x[ErrIllegalSQLFunctionArgument-201]
	_ = x[ErrInvalidKeyPath-202]
	_ = x[ErrInvalidCompressionFormat-203]
	_ = x[ErrInvalidFileHeaderInfo-204]
	_ = x[ErrInvalidJSONType-205]
	_ = x[ErrInvalidQuoteFields-206]
	_ = x[ErrInvalidRequestParameter-207]
	_ = x[ErrInvalidDataType-208]
	_ = x[ErrInvalidTextEncoding-209]
	_ = x[ErrInvalidDataSource-210]
	_ = x[ErrInvalidTableAlias-211]
	_ = x[ErrMissingRequiredParameter-212]
	_ = x[ErrObjectSerializationConflict-213]
	_ = x[ErrUnsupportedSQLOperation-214]
	_ = x[ErrUnsupportedSQLStructure-215]
	_ = x[ErrUnsupportedSyntax-216]
	_ = x[ErrUnsupportedRangeHeader-217]
	_ = x[ErrLexerInvalidChar-218]
	_ = x[ErrLexerInvalidOperator-219]
	_ = x[ErrLexerInvalidLiteral-220]
	_ = x[ErrLexerInvalidIONLiteral-221]
	_ = x[ErrParseExpectedDatePart-222]
	_ = x[ErrParseExpectedKeyword-223]
	_ = x[ErrParseExpectedTokenType-224]
	_ = x[ErrParseExpected2TokenTypes-225]
	_ = x[ErrParseExpectedNumber-226]
	_ = x[ErrParseExpectedRightParenBuiltinFunctionCall-227]
	_ = x[ErrParseExpectedTypeName-228]
	_ = x[ErrParseExpectedWhenClause-229]
	_ = x[ErrParseUnsupportedToken-230]
	_ = x[ErrParseUnsupportedLiteralsGroupBy-231]
	_ = x[ErrParseExpectedMember-232]
	_ = x[ErrParseUnsupportedSelect-233]
	_ = x[ErrParseUnsupportedCase-234]
	_ = x[ErrParseUnsupportedCaseClause-235]
	_ = x[ErrParseUnsupportedAlias-236]
	_ = x[ErrParseUnsupportedSyntax-237]
	_ = x[ErrParseUnknownOperator-238]
	_ = x[ErrParseMissingIdentAfterAt-239]
	_ = x[ErrParseUnexpectedOperator-240]
	_ = x[ErrParseUnexpectedTerm-241]
	_ = x[ErrParseUnexpectedToken-242]
	_ = x[ErrParseUnexpectedKeyword-243]
	_ = x[ErrParseExpectedExpression-244]
	_ = x[ErrParseExpectedLeftParenAfterCast-245]
	_ = x[ErrParseExpectedLeftParenValueConstructor-246]
	_ = x[ErrParseExpectedLeftParenBuiltinFunctionCall-247]
	_ = x[ErrParseExpectedArgumentDelimiter-248]
	_ = x[ErrParseCastArity-249]
	_ = x[ErrParseInvalidTypeParam-250]
	_ = x[ErrParseEmptySelect-251]
	_ = x[ErrParseSelectMissingFrom-252]
	_ = x[ErrParseExpectedIdentForGroupName-253]
	_ = x[ErrParseExpectedIdentForAlias-254]
	_ = x[ErrParseUnsupportedCallWithStar-255]
	_ = x[ErrParseNonUnaryAgregateFunctionCall-256]
	_ = x[ErrParseMalformedJoin-257]
	_ = x[ErrParseExpectedIdentForAt-258]
	_ = x[ErrParseAsteriskIsNotAloneInSelectList-259]
	_ = x[ErrParseCannotMixSqbAndWildcardInSelectList-260]
	_ = x[ErrParseInvalidContextForWildcardInSelectList-261]
	_ = x[ErrIncorrectSQLFunctionArgumentType-262]
	_ = x[ErrValueParseFailure-263]
	_ = x[ErrEvaluatorInvalidArguments-264]
	_ = x[ErrIntegerOverflow-265]
	_ = x[ErrLikeInvalidInputs-266]
	_ = x[ErrCastFailed-267]
	_ = x[ErrInvalidCast-268]
	_ = x[ErrEvaluatorInvalidTimestampFormatPattern-269]
	_ = x[ErrEvaluatorInvalidTimestampFormatPatternSymbolForParsing-270]
	_ = x[ErrEvaluatorTimestampFormatPatternDuplicateFields-271]
	_ = x[ErrEvaluatorTimestampFormatPatternHourClockAmPmMismatch-272]
	_ = x[ErrEvaluatorUnterminatedTimestampFormatPatternToken-273]
	_ = x[ErrEvaluatorInvalidTimestampFormatPatternToken-274]
	_ = x[ErrEvaluatorInvalidTimestampFormatPatternSymbol-275]
	_ = x[ErrEvaluatorBindingDoesNotExist-276]
	_ = x[ErrMissingHeaders-277]
	_ = x[ErrInvalidColumnIndex-278]
	_ = x[ErrAdminConfigNotificationTargetsFailed-279]
	_ = x[ErrAdminProfilerNotEnabled-280]
	_ = x[ErrInvalidDecompressedSize-281]
	_ = x[ErrAddUserInvalidArgument-282]
	_ = x[ErrAdminAccountNotEligible-283]
	_ = x[ErrAccountNotEligible-284]
	_ = x[ErrAdminServiceAccountNotFound-285]
	_ = x[ErrPostPolicyConditionInvalidFormat-286]
}

const _APIErrorCode_name = "NoneAccessDeniedBadDigestEntityTooSmallEntityTooLargePolicyTooLargeIncompleteBodyInternalErrorInvalidAccessKeyIDAccessKeyDisabledInvalidBucketNameInvalidDigestInvalidRangeInvalidRangePartNumberInvalidCopyPartRangeInvalidCopyPartRangeSourceInvalidMaxKeysInvalidEncodingMethodInvalidMaxUploadsInvalidMaxPartsInvalidPartNumberMarkerInvalidPartNumberInvalidRequestBodyInvalidCopySourceInvalidMetadataDirectiveInvalidCopyDestInvalidPolicyDocumentInvalidObjectStateMalformedXMLMissingContentLengthMissingContentMD5MissingRequestBodyErrorMissingSecurityHeaderNoSuchBucketNoSuchBucketPolicyNoSuchBucketLifecycleNoSuchLifecycleConfigurationInvalidLifecycleWithObjectLockNoSuchBucketSSEConfigNoSuchCORSConfigurationNoSuchWebsiteConfigurationReplicationConfigurationNotFoundErrorRemoteDestinationNotFoundErrorReplicationDestinationMissingLockRemoteTargetNotFoundErrorReplicationRemoteConnectionErrorReplicationBandwidthLimitErrorBucketRemoteIdenticalToSourceBucketRemoteAlreadyExistsBucketRemoteLabelInUseBucketRemoteArnTypeInvalidBucketRemoteArnInvalidBucketRemoteRemoveDisallowedRemoteTargetNotVersionedErrorReplicationSourceNotVersionedErrorReplicationNeedsVersioningErrorReplicationBucketNeedsVersioningErrorReplicationDenyEditErrorReplicationNoMatchingRuleErrorObjectRestoreAlreadyInProgressNoSuchKeyNoSuchUploadInvalidVersionIDNoSuchVersionNotImplementedPreconditionFailedRequestTimeTooSkewedSignatureDoesNotMatchMethodNotAllowedInvalidPartInvalidPartOrderAuthorizationHeaderMalformedMalformedPOSTRequestPOSTFileRequiredSignatureVersionNotSupportedBucketNotEmptyAllAccessDisabledMalformedPolicyMissingFieldsMissingCredTagCredMalformedInvalidRegionInvalidServiceS3InvalidServiceSTSInvalidRequestVersionMissingSignTagMissingSignHeadersTagMalformedDateMalformedPresignedDateMalformedCredentialDateMalformedCredentialRegionMalformedExpiresNegativeExpiresAuthHeaderEmptyExpiredPresignRequestRequestNotReadyYetUnsignedHeadersMissingDateHeaderInvalidQuerySignatureAlgoInvalidQueryParamsBucketAlreadyOwnedByYouInvalidDurationBucketAlreadyExistsMetadataTooLargeUnsupportedMetadataMaximumExpiresSlowDownInvalidPrefixMarkerBadRequestKeyTooLongErrorInvalidBucketObjectLockConfigurationObjectLockConfigurationNotFoundObjectLockConfigurationNotAllowedNoSuchObjectLockConfigurationObjectLockedInvalidRetentionDatePastObjectLockRetainDateUnknownWORMModeDirectiveBucketTaggingNotFoundObjectLockInvalidHeadersInvalidTagDirectiveInvalidEncryptionMethodInsecureSSECustomerRequestSSEMultipartEncryptedSSEEncryptedObjectInvalidEncryptionParametersInvalidSSECustomerAlgorithmInvalidSSECustomerKeyMissingSSECustomerKeyMissingSSECustomerKeyMD5SSECustomerKeyMD5MismatchInvalidSSECustomerParametersIncompatibleEncryptionMethodKMSNotConfiguredKMSKeyNotFoundExceptionNoAccessKeyInvalidTokenEventNotificationARNNotificationRegionNotificationOverlappingFilterNotificationFilterNameInvalidFilterNamePrefixFilterNameSuffixFilterValueInvalidOverlappingConfigsUnsupportedNotificationContentSHA256MismatchReadQuorumWriteQuorumStorageFullRequestBodyParseObjectExistsAsDirectoryInvalidObjectNameInvalidObjectNamePrefixSlashInvalidResourceNameServerNotInitializedOperationTimedOutClientDisconnectedOperationMaxedOutInvalidRequestTransitionStorageClassNotFoundErrorInvalidStorageClassBackendDownMalformedJSONAdminNoSuchUserAdminNoSuchGroupAdminGroupNotEmptyAdminNoSuchPolicyAdminInvalidArgumentAdminInvalidAccessKeyAdminInvalidSecretKeyAdminConfigNoQuorumAdminConfigTooLargeAdminConfigBadJSONAdminConfigDuplicateKeysAdminCredentialsMismatchInsecureClientRequestObjectTamperedSiteReplicationInvalidRequestSiteReplicationPeerRespSiteReplicationBackendIssueSiteReplicationServiceAccountErrorSiteReplicationBucketConfigErrorSiteReplicationBucketMetaErrorSiteReplicationIAMErrorAdminBucketQuotaExceededAdminNoSuchQuotaConfigurationHealNotImplementedHealNoSuchProcessHealInvalidClientTokenHealMissingBucketHealAlreadyRunningHealOverlappingPathsIncorrectContinuationTokenEmptyRequestBodyUnsupportedFunctionInvalidExpressionTypeBusyUnauthorizedAccessExpressionTooLongIllegalSQLFunctionArgumentInvalidKeyPathInvalidCompressionFormatInvalidFileHeaderInfoInvalidJSONTypeInvalidQuoteFieldsInvalidRequestParameterInvalidDataTypeInvalidTextEncodingInvalidDataSourceInvalidTableAliasMissingRequiredParameterObjectSerializationConflictUnsupportedSQLOperationUnsupportedSQLStructureUnsupportedSyntaxUnsupportedRangeHeaderLexerInvalidCharLexerInvalidOperatorLexerInvalidLiteralLexerInvalidIONLiteralParseExpectedDatePartParseExpectedKeywordParseExpectedTokenTypeParseExpected2TokenTypesParseExpectedNumberParseExpectedRightParenBuiltinFunctionCallParseExpectedTypeNameParseExpectedWhenClauseParseUnsupportedTokenParseUnsupportedLiteralsGroupByParseExpectedMemberParseUnsupportedSelectParseUnsupportedCaseParseUnsupportedCaseClauseParseUnsupportedAliasParseUnsupportedSyntaxParseUnknownOperatorParseMissingIdentAfterAtParseUnexpectedOperatorParseUnexpectedTermParseUnexpectedTokenParseUnexpectedKeywordParseExpectedExpressionParseExpectedLeftParenAfterCastParseExpectedLeftParenValueConstructorParseExpectedLeftParenBuiltinFunctionCallParseExpectedArgumentDelimiterParseCastArityParseInvalidTypeParamParseEmptySelectParseSelectMissingFromParseExpectedIdentForGroupNameParseExpectedIdentForAliasParseUnsupportedCallWithStarParseNonUnaryAgregateFunctionCallParseMalformedJoinParseExpectedIdentForAtParseAsteriskIsNotAloneInSelectListParseCannotMixSqbAndWildcardInSelectListParseInvalidContextForWildcardInSelectListIncorrectSQLFunctionArgumentTypeValueParseFailureEvaluatorInvalidArgumentsIntegerOverflowLikeInvalidInputsCastFailedInvalidCastEvaluatorInvalidTimestampFormatPatternEvaluatorInvalidTimestampFormatPatternSymbolForParsingEvaluatorTimestampFormatPatternDuplicateFieldsEvaluatorTimestampFormatPatternHourClockAmPmMismatchEvaluatorUnterminatedTimestampFormatPatternTokenEvaluatorInvalidTimestampFormatPatternTokenEvaluatorInvalidTimestampFormatPatternSymbolEvaluatorBindingDoesNotExistMissingHeadersInvalidColumnIndexAdminConfigNotificationTargetsFailedAdminProfilerNotEnabledInvalidDecompressedSizeAddUserInvalidArgumentAdminAccountNotEligibleAccountNotEligibleAdminServiceAccountNotFoundPostPolicyConditionInvalidFormat"

var _APIErrorCode_index = [...]uint16{0, 4, 16, 25, 39, 53, 67, 81, 94, 112, 129, 146, 159, 171, 193, 213, 239, 253, 274, 291, 306, 329, 346, 364, 381, 405, 420, 441, 459, 471, 491, 508, 531, 552, 564, 582, 603, 631, 661, 682, 705, 731, 768, 798, 831, 856, 888, 918, 947, 972, 994, 1020, 1042, 1070, 1099, 1133, 1164, 1201, 1225, 1255, 1285, 1294, 1306, 1322, 1335, 1349, 1367, 1387, 1408, 1424, 1435, 1451, 1479, 1499, 1515, 1543, 1557, 1574, 1589, 1602, 1616, 1629, 1642, 1658, 1675, 1696, 1710, 1731, 1744, 1766, 1789, 1814, 1830, 1845, 1860, 1881, 1899, 1914, 1931, 1956, 1974, 1997, 2012, 2031, 2047, 2066, 2080, 2088, 2107, 2117, 2132, 2168, 2199, 2232, 2261, 2273, 2293, 2317, 2341, 2362, 2386, 2405, 2428, 2454, 2475, 2493, 2520, 2547, 2568, 2589, 2613, 2638, 2666, 2694, 2710, 2733, 2744, 2756, 2773, 2788, 2806, 2835, 2852, 2868, 2884, 2902, 2920, 2943, 2964, 2974, 2985, 2996, 3012, 3035, 3052, 3080, 3099, 3119, 3136, 3154, 3171, 3185, 3220, 3239, 3250, 3263, 3278, 3294, 3312, 3329, 3349, 3370, 3391, 3410, 3429, 3447, 3471, 3495, 3516, 3530, 3559, 3582, 3609, 3643, 3675, 3705, 3728, 3752, 3781, 3799, 3816, 3838, 3855, 3873, 3893, 3919, 3935, 3954, 3975, 3979, 3997, 4014, 4040, 4054, 4078, 4099, 4114, 4132, 4155, 4170, 4189, 4206, 4223, 4247, 4274, 4297, 4320, 4337, 4359, 4375, 4395, 4414, 4436, 4457, 4477, 4499, 4523, 4542, 4584, 4605, 4628, 4649, 4680, 4699, 4721, 4741, 4767, 4788, 4810, 4830, 4854, 4877, 4896, 4916, 4938, 4961, 4992, 5030, 5071, 5101, 5115, 5136, 5152, 5174, 5204, 5230, 5258, 5291, 5309, 5332, 5367, 5407, 5449, 5481, 5498, 5523, 5538, 5555, 5565, 5576, 5614, 5668, 5714, 5766, 5814, 5857, 5901, 5929, 5943, 5961, 5997, 6020, 6043, 6065, 6088, 6106, 6133, 6165}

func (i APIErrorCode) String() string {
	if i < 0 || i >= APIErrorCode(len(_APIErrorCode_index)-1) {
		return "APIErrorCode(" + strconv.FormatInt(int64(i), 10) + ")"
	}
	return _APIErrorCode_name[_APIErrorCode_index[i]:_APIErrorCode_index[i+1]]
}
