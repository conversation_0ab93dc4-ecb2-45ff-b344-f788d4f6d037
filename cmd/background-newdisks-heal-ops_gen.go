package cmd

// Code generated by github.com/tinylib/msgp DO NOT EDIT.

import (
	"github.com/tinylib/msgp/msgp"
)

// DecodeMsg implements msgp.Decodable
func (z *healingTracker) DecodeMsg(dc *msgp.Reader) (err error) {
	var field []byte
	_ = field
	var zb0001 uint32
	zb0001, err = dc.ReadMapHeader()
	if err != nil {
		err = msgp.WrapError(err)
		return
	}
	for zb0001 > 0 {
		zb0001--
		field, err = dc.ReadMapKeyPtr()
		if err != nil {
			err = msgp.WrapError(err)
			return
		}
		switch msgp.UnsafeString(field) {
		case "ID":
			z.ID, err = dc.ReadString()
			if err != nil {
				err = msgp.WrapError(err, "ID")
				return
			}
		case "PoolIndex":
			z.PoolIndex, err = dc.ReadInt()
			if err != nil {
				err = msgp.WrapError(err, "PoolIndex")
				return
			}
		case "SetIndex":
			z.SetIndex, err = dc.ReadInt()
			if err != nil {
				err = msgp.WrapError(err, "SetIndex")
				return
			}
		case "DiskIndex":
			z.DiskIndex, err = dc.ReadInt()
			if err != nil {
				err = msgp.WrapError(err, "DiskIndex")
				return
			}
		case "Path":
			z.Path, err = dc.ReadString()
			if err != nil {
				err = msgp.WrapError(err, "Path")
				return
			}
		case "Endpoint":
			z.Endpoint, err = dc.ReadString()
			if err != nil {
				err = msgp.WrapError(err, "Endpoint")
				return
			}
		case "Started":
			z.Started, err = dc.ReadTime()
			if err != nil {
				err = msgp.WrapError(err, "Started")
				return
			}
		case "LastUpdate":
			z.LastUpdate, err = dc.ReadTime()
			if err != nil {
				err = msgp.WrapError(err, "LastUpdate")
				return
			}
		case "ObjectsTotalCount":
			z.ObjectsTotalCount, err = dc.ReadUint64()
			if err != nil {
				err = msgp.WrapError(err, "ObjectsTotalCount")
				return
			}
		case "ObjectsTotalSize":
			z.ObjectsTotalSize, err = dc.ReadUint64()
			if err != nil {
				err = msgp.WrapError(err, "ObjectsTotalSize")
				return
			}
		case "ItemsHealed":
			z.ItemsHealed, err = dc.ReadUint64()
			if err != nil {
				err = msgp.WrapError(err, "ItemsHealed")
				return
			}
		case "ItemsFailed":
			z.ItemsFailed, err = dc.ReadUint64()
			if err != nil {
				err = msgp.WrapError(err, "ItemsFailed")
				return
			}
		case "BytesDone":
			z.BytesDone, err = dc.ReadUint64()
			if err != nil {
				err = msgp.WrapError(err, "BytesDone")
				return
			}
		case "BytesFailed":
			z.BytesFailed, err = dc.ReadUint64()
			if err != nil {
				err = msgp.WrapError(err, "BytesFailed")
				return
			}
		case "Bucket":
			z.Bucket, err = dc.ReadString()
			if err != nil {
				err = msgp.WrapError(err, "Bucket")
				return
			}
		case "Object":
			z.Object, err = dc.ReadString()
			if err != nil {
				err = msgp.WrapError(err, "Object")
				return
			}
		case "ResumeItemsHealed":
			z.ResumeItemsHealed, err = dc.ReadUint64()
			if err != nil {
				err = msgp.WrapError(err, "ResumeItemsHealed")
				return
			}
		case "ResumeItemsFailed":
			z.ResumeItemsFailed, err = dc.ReadUint64()
			if err != nil {
				err = msgp.WrapError(err, "ResumeItemsFailed")
				return
			}
		case "ResumeBytesDone":
			z.ResumeBytesDone, err = dc.ReadUint64()
			if err != nil {
				err = msgp.WrapError(err, "ResumeBytesDone")
				return
			}
		case "ResumeBytesFailed":
			z.ResumeBytesFailed, err = dc.ReadUint64()
			if err != nil {
				err = msgp.WrapError(err, "ResumeBytesFailed")
				return
			}
		case "QueuedBuckets":
			var zb0002 uint32
			zb0002, err = dc.ReadArrayHeader()
			if err != nil {
				err = msgp.WrapError(err, "QueuedBuckets")
				return
			}
			if cap(z.QueuedBuckets) >= int(zb0002) {
				z.QueuedBuckets = (z.QueuedBuckets)[:zb0002]
			} else {
				z.QueuedBuckets = make([]string, zb0002)
			}
			for za0001 := range z.QueuedBuckets {
				z.QueuedBuckets[za0001], err = dc.ReadString()
				if err != nil {
					err = msgp.WrapError(err, "QueuedBuckets", za0001)
					return
				}
			}
		case "HealedBuckets":
			var zb0003 uint32
			zb0003, err = dc.ReadArrayHeader()
			if err != nil {
				err = msgp.WrapError(err, "HealedBuckets")
				return
			}
			if cap(z.HealedBuckets) >= int(zb0003) {
				z.HealedBuckets = (z.HealedBuckets)[:zb0003]
			} else {
				z.HealedBuckets = make([]string, zb0003)
			}
			for za0002 := range z.HealedBuckets {
				z.HealedBuckets[za0002], err = dc.ReadString()
				if err != nil {
					err = msgp.WrapError(err, "HealedBuckets", za0002)
					return
				}
			}
		default:
			err = dc.Skip()
			if err != nil {
				err = msgp.WrapError(err)
				return
			}
		}
	}
	return
}

// EncodeMsg implements msgp.Encodable
func (z *healingTracker) EncodeMsg(en *msgp.Writer) (err error) {
	// map header, size 22
	// write "ID"
	err = en.Append(0xde, 0x0, 0x16, 0xa2, 0x49, 0x44)
	if err != nil {
		return
	}
	err = en.WriteString(z.ID)
	if err != nil {
		err = msgp.WrapError(err, "ID")
		return
	}
	// write "PoolIndex"
	err = en.Append(0xa9, 0x50, 0x6f, 0x6f, 0x6c, 0x49, 0x6e, 0x64, 0x65, 0x78)
	if err != nil {
		return
	}
	err = en.WriteInt(z.PoolIndex)
	if err != nil {
		err = msgp.WrapError(err, "PoolIndex")
		return
	}
	// write "SetIndex"
	err = en.Append(0xa8, 0x53, 0x65, 0x74, 0x49, 0x6e, 0x64, 0x65, 0x78)
	if err != nil {
		return
	}
	err = en.WriteInt(z.SetIndex)
	if err != nil {
		err = msgp.WrapError(err, "SetIndex")
		return
	}
	// write "DiskIndex"
	err = en.Append(0xa9, 0x44, 0x69, 0x73, 0x6b, 0x49, 0x6e, 0x64, 0x65, 0x78)
	if err != nil {
		return
	}
	err = en.WriteInt(z.DiskIndex)
	if err != nil {
		err = msgp.WrapError(err, "DiskIndex")
		return
	}
	// write "Path"
	err = en.Append(0xa4, 0x50, 0x61, 0x74, 0x68)
	if err != nil {
		return
	}
	err = en.WriteString(z.Path)
	if err != nil {
		err = msgp.WrapError(err, "Path")
		return
	}
	// write "Endpoint"
	err = en.Append(0xa8, 0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74)
	if err != nil {
		return
	}
	err = en.WriteString(z.Endpoint)
	if err != nil {
		err = msgp.WrapError(err, "Endpoint")
		return
	}
	// write "Started"
	err = en.Append(0xa7, 0x53, 0x74, 0x61, 0x72, 0x74, 0x65, 0x64)
	if err != nil {
		return
	}
	err = en.WriteTime(z.Started)
	if err != nil {
		err = msgp.WrapError(err, "Started")
		return
	}
	// write "LastUpdate"
	err = en.Append(0xaa, 0x4c, 0x61, 0x73, 0x74, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65)
	if err != nil {
		return
	}
	err = en.WriteTime(z.LastUpdate)
	if err != nil {
		err = msgp.WrapError(err, "LastUpdate")
		return
	}
	// write "ObjectsTotalCount"
	err = en.Append(0xb1, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x43, 0x6f, 0x75, 0x6e, 0x74)
	if err != nil {
		return
	}
	err = en.WriteUint64(z.ObjectsTotalCount)
	if err != nil {
		err = msgp.WrapError(err, "ObjectsTotalCount")
		return
	}
	// write "ObjectsTotalSize"
	err = en.Append(0xb0, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x53, 0x69, 0x7a, 0x65)
	if err != nil {
		return
	}
	err = en.WriteUint64(z.ObjectsTotalSize)
	if err != nil {
		err = msgp.WrapError(err, "ObjectsTotalSize")
		return
	}
	// write "ItemsHealed"
	err = en.Append(0xab, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x48, 0x65, 0x61, 0x6c, 0x65, 0x64)
	if err != nil {
		return
	}
	err = en.WriteUint64(z.ItemsHealed)
	if err != nil {
		err = msgp.WrapError(err, "ItemsHealed")
		return
	}
	// write "ItemsFailed"
	err = en.Append(0xab, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x46, 0x61, 0x69, 0x6c, 0x65, 0x64)
	if err != nil {
		return
	}
	err = en.WriteUint64(z.ItemsFailed)
	if err != nil {
		err = msgp.WrapError(err, "ItemsFailed")
		return
	}
	// write "BytesDone"
	err = en.Append(0xa9, 0x42, 0x79, 0x74, 0x65, 0x73, 0x44, 0x6f, 0x6e, 0x65)
	if err != nil {
		return
	}
	err = en.WriteUint64(z.BytesDone)
	if err != nil {
		err = msgp.WrapError(err, "BytesDone")
		return
	}
	// write "BytesFailed"
	err = en.Append(0xab, 0x42, 0x79, 0x74, 0x65, 0x73, 0x46, 0x61, 0x69, 0x6c, 0x65, 0x64)
	if err != nil {
		return
	}
	err = en.WriteUint64(z.BytesFailed)
	if err != nil {
		err = msgp.WrapError(err, "BytesFailed")
		return
	}
	// write "Bucket"
	err = en.Append(0xa6, 0x42, 0x75, 0x63, 0x6b, 0x65, 0x74)
	if err != nil {
		return
	}
	err = en.WriteString(z.Bucket)
	if err != nil {
		err = msgp.WrapError(err, "Bucket")
		return
	}
	// write "Object"
	err = en.Append(0xa6, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74)
	if err != nil {
		return
	}
	err = en.WriteString(z.Object)
	if err != nil {
		err = msgp.WrapError(err, "Object")
		return
	}
	// write "ResumeItemsHealed"
	err = en.Append(0xb1, 0x52, 0x65, 0x73, 0x75, 0x6d, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x48, 0x65, 0x61, 0x6c, 0x65, 0x64)
	if err != nil {
		return
	}
	err = en.WriteUint64(z.ResumeItemsHealed)
	if err != nil {
		err = msgp.WrapError(err, "ResumeItemsHealed")
		return
	}
	// write "ResumeItemsFailed"
	err = en.Append(0xb1, 0x52, 0x65, 0x73, 0x75, 0x6d, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x46, 0x61, 0x69, 0x6c, 0x65, 0x64)
	if err != nil {
		return
	}
	err = en.WriteUint64(z.ResumeItemsFailed)
	if err != nil {
		err = msgp.WrapError(err, "ResumeItemsFailed")
		return
	}
	// write "ResumeBytesDone"
	err = en.Append(0xaf, 0x52, 0x65, 0x73, 0x75, 0x6d, 0x65, 0x42, 0x79, 0x74, 0x65, 0x73, 0x44, 0x6f, 0x6e, 0x65)
	if err != nil {
		return
	}
	err = en.WriteUint64(z.ResumeBytesDone)
	if err != nil {
		err = msgp.WrapError(err, "ResumeBytesDone")
		return
	}
	// write "ResumeBytesFailed"
	err = en.Append(0xb1, 0x52, 0x65, 0x73, 0x75, 0x6d, 0x65, 0x42, 0x79, 0x74, 0x65, 0x73, 0x46, 0x61, 0x69, 0x6c, 0x65, 0x64)
	if err != nil {
		return
	}
	err = en.WriteUint64(z.ResumeBytesFailed)
	if err != nil {
		err = msgp.WrapError(err, "ResumeBytesFailed")
		return
	}
	// write "QueuedBuckets"
	err = en.Append(0xad, 0x51, 0x75, 0x65, 0x75, 0x65, 0x64, 0x42, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x73)
	if err != nil {
		return
	}
	err = en.WriteArrayHeader(uint32(len(z.QueuedBuckets)))
	if err != nil {
		err = msgp.WrapError(err, "QueuedBuckets")
		return
	}
	for za0001 := range z.QueuedBuckets {
		err = en.WriteString(z.QueuedBuckets[za0001])
		if err != nil {
			err = msgp.WrapError(err, "QueuedBuckets", za0001)
			return
		}
	}
	// write "HealedBuckets"
	err = en.Append(0xad, 0x48, 0x65, 0x61, 0x6c, 0x65, 0x64, 0x42, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x73)
	if err != nil {
		return
	}
	err = en.WriteArrayHeader(uint32(len(z.HealedBuckets)))
	if err != nil {
		err = msgp.WrapError(err, "HealedBuckets")
		return
	}
	for za0002 := range z.HealedBuckets {
		err = en.WriteString(z.HealedBuckets[za0002])
		if err != nil {
			err = msgp.WrapError(err, "HealedBuckets", za0002)
			return
		}
	}
	return
}

// MarshalMsg implements msgp.Marshaler
func (z *healingTracker) MarshalMsg(b []byte) (o []byte, err error) {
	o = msgp.Require(b, z.Msgsize())
	// map header, size 22
	// string "ID"
	o = append(o, 0xde, 0x0, 0x16, 0xa2, 0x49, 0x44)
	o = msgp.AppendString(o, z.ID)
	// string "PoolIndex"
	o = append(o, 0xa9, 0x50, 0x6f, 0x6f, 0x6c, 0x49, 0x6e, 0x64, 0x65, 0x78)
	o = msgp.AppendInt(o, z.PoolIndex)
	// string "SetIndex"
	o = append(o, 0xa8, 0x53, 0x65, 0x74, 0x49, 0x6e, 0x64, 0x65, 0x78)
	o = msgp.AppendInt(o, z.SetIndex)
	// string "DiskIndex"
	o = append(o, 0xa9, 0x44, 0x69, 0x73, 0x6b, 0x49, 0x6e, 0x64, 0x65, 0x78)
	o = msgp.AppendInt(o, z.DiskIndex)
	// string "Path"
	o = append(o, 0xa4, 0x50, 0x61, 0x74, 0x68)
	o = msgp.AppendString(o, z.Path)
	// string "Endpoint"
	o = append(o, 0xa8, 0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74)
	o = msgp.AppendString(o, z.Endpoint)
	// string "Started"
	o = append(o, 0xa7, 0x53, 0x74, 0x61, 0x72, 0x74, 0x65, 0x64)
	o = msgp.AppendTime(o, z.Started)
	// string "LastUpdate"
	o = append(o, 0xaa, 0x4c, 0x61, 0x73, 0x74, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65)
	o = msgp.AppendTime(o, z.LastUpdate)
	// string "ObjectsTotalCount"
	o = append(o, 0xb1, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x43, 0x6f, 0x75, 0x6e, 0x74)
	o = msgp.AppendUint64(o, z.ObjectsTotalCount)
	// string "ObjectsTotalSize"
	o = append(o, 0xb0, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x53, 0x69, 0x7a, 0x65)
	o = msgp.AppendUint64(o, z.ObjectsTotalSize)
	// string "ItemsHealed"
	o = append(o, 0xab, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x48, 0x65, 0x61, 0x6c, 0x65, 0x64)
	o = msgp.AppendUint64(o, z.ItemsHealed)
	// string "ItemsFailed"
	o = append(o, 0xab, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x46, 0x61, 0x69, 0x6c, 0x65, 0x64)
	o = msgp.AppendUint64(o, z.ItemsFailed)
	// string "BytesDone"
	o = append(o, 0xa9, 0x42, 0x79, 0x74, 0x65, 0x73, 0x44, 0x6f, 0x6e, 0x65)
	o = msgp.AppendUint64(o, z.BytesDone)
	// string "BytesFailed"
	o = append(o, 0xab, 0x42, 0x79, 0x74, 0x65, 0x73, 0x46, 0x61, 0x69, 0x6c, 0x65, 0x64)
	o = msgp.AppendUint64(o, z.BytesFailed)
	// string "Bucket"
	o = append(o, 0xa6, 0x42, 0x75, 0x63, 0x6b, 0x65, 0x74)
	o = msgp.AppendString(o, z.Bucket)
	// string "Object"
	o = append(o, 0xa6, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74)
	o = msgp.AppendString(o, z.Object)
	// string "ResumeItemsHealed"
	o = append(o, 0xb1, 0x52, 0x65, 0x73, 0x75, 0x6d, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x48, 0x65, 0x61, 0x6c, 0x65, 0x64)
	o = msgp.AppendUint64(o, z.ResumeItemsHealed)
	// string "ResumeItemsFailed"
	o = append(o, 0xb1, 0x52, 0x65, 0x73, 0x75, 0x6d, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x46, 0x61, 0x69, 0x6c, 0x65, 0x64)
	o = msgp.AppendUint64(o, z.ResumeItemsFailed)
	// string "ResumeBytesDone"
	o = append(o, 0xaf, 0x52, 0x65, 0x73, 0x75, 0x6d, 0x65, 0x42, 0x79, 0x74, 0x65, 0x73, 0x44, 0x6f, 0x6e, 0x65)
	o = msgp.AppendUint64(o, z.ResumeBytesDone)
	// string "ResumeBytesFailed"
	o = append(o, 0xb1, 0x52, 0x65, 0x73, 0x75, 0x6d, 0x65, 0x42, 0x79, 0x74, 0x65, 0x73, 0x46, 0x61, 0x69, 0x6c, 0x65, 0x64)
	o = msgp.AppendUint64(o, z.ResumeBytesFailed)
	// string "QueuedBuckets"
	o = append(o, 0xad, 0x51, 0x75, 0x65, 0x75, 0x65, 0x64, 0x42, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x73)
	o = msgp.AppendArrayHeader(o, uint32(len(z.QueuedBuckets)))
	for za0001 := range z.QueuedBuckets {
		o = msgp.AppendString(o, z.QueuedBuckets[za0001])
	}
	// string "HealedBuckets"
	o = append(o, 0xad, 0x48, 0x65, 0x61, 0x6c, 0x65, 0x64, 0x42, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x73)
	o = msgp.AppendArrayHeader(o, uint32(len(z.HealedBuckets)))
	for za0002 := range z.HealedBuckets {
		o = msgp.AppendString(o, z.HealedBuckets[za0002])
	}
	return
}

// UnmarshalMsg implements msgp.Unmarshaler
func (z *healingTracker) UnmarshalMsg(bts []byte) (o []byte, err error) {
	var field []byte
	_ = field
	var zb0001 uint32
	zb0001, bts, err = msgp.ReadMapHeaderBytes(bts)
	if err != nil {
		err = msgp.WrapError(err)
		return
	}
	for zb0001 > 0 {
		zb0001--
		field, bts, err = msgp.ReadMapKeyZC(bts)
		if err != nil {
			err = msgp.WrapError(err)
			return
		}
		switch msgp.UnsafeString(field) {
		case "ID":
			z.ID, bts, err = msgp.ReadStringBytes(bts)
			if err != nil {
				err = msgp.WrapError(err, "ID")
				return
			}
		case "PoolIndex":
			z.PoolIndex, bts, err = msgp.ReadIntBytes(bts)
			if err != nil {
				err = msgp.WrapError(err, "PoolIndex")
				return
			}
		case "SetIndex":
			z.SetIndex, bts, err = msgp.ReadIntBytes(bts)
			if err != nil {
				err = msgp.WrapError(err, "SetIndex")
				return
			}
		case "DiskIndex":
			z.DiskIndex, bts, err = msgp.ReadIntBytes(bts)
			if err != nil {
				err = msgp.WrapError(err, "DiskIndex")
				return
			}
		case "Path":
			z.Path, bts, err = msgp.ReadStringBytes(bts)
			if err != nil {
				err = msgp.WrapError(err, "Path")
				return
			}
		case "Endpoint":
			z.Endpoint, bts, err = msgp.ReadStringBytes(bts)
			if err != nil {
				err = msgp.WrapError(err, "Endpoint")
				return
			}
		case "Started":
			z.Started, bts, err = msgp.ReadTimeBytes(bts)
			if err != nil {
				err = msgp.WrapError(err, "Started")
				return
			}
		case "LastUpdate":
			z.LastUpdate, bts, err = msgp.ReadTimeBytes(bts)
			if err != nil {
				err = msgp.WrapError(err, "LastUpdate")
				return
			}
		case "ObjectsTotalCount":
			z.ObjectsTotalCount, bts, err = msgp.ReadUint64Bytes(bts)
			if err != nil {
				err = msgp.WrapError(err, "ObjectsTotalCount")
				return
			}
		case "ObjectsTotalSize":
			z.ObjectsTotalSize, bts, err = msgp.ReadUint64Bytes(bts)
			if err != nil {
				err = msgp.WrapError(err, "ObjectsTotalSize")
				return
			}
		case "ItemsHealed":
			z.ItemsHealed, bts, err = msgp.ReadUint64Bytes(bts)
			if err != nil {
				err = msgp.WrapError(err, "ItemsHealed")
				return
			}
		case "ItemsFailed":
			z.ItemsFailed, bts, err = msgp.ReadUint64Bytes(bts)
			if err != nil {
				err = msgp.WrapError(err, "ItemsFailed")
				return
			}
		case "BytesDone":
			z.BytesDone, bts, err = msgp.ReadUint64Bytes(bts)
			if err != nil {
				err = msgp.WrapError(err, "BytesDone")
				return
			}
		case "BytesFailed":
			z.BytesFailed, bts, err = msgp.ReadUint64Bytes(bts)
			if err != nil {
				err = msgp.WrapError(err, "BytesFailed")
				return
			}
		case "Bucket":
			z.Bucket, bts, err = msgp.ReadStringBytes(bts)
			if err != nil {
				err = msgp.WrapError(err, "Bucket")
				return
			}
		case "Object":
			z.Object, bts, err = msgp.ReadStringBytes(bts)
			if err != nil {
				err = msgp.WrapError(err, "Object")
				return
			}
		case "ResumeItemsHealed":
			z.ResumeItemsHealed, bts, err = msgp.ReadUint64Bytes(bts)
			if err != nil {
				err = msgp.WrapError(err, "ResumeItemsHealed")
				return
			}
		case "ResumeItemsFailed":
			z.ResumeItemsFailed, bts, err = msgp.ReadUint64Bytes(bts)
			if err != nil {
				err = msgp.WrapError(err, "ResumeItemsFailed")
				return
			}
		case "ResumeBytesDone":
			z.ResumeBytesDone, bts, err = msgp.ReadUint64Bytes(bts)
			if err != nil {
				err = msgp.WrapError(err, "ResumeBytesDone")
				return
			}
		case "ResumeBytesFailed":
			z.ResumeBytesFailed, bts, err = msgp.ReadUint64Bytes(bts)
			if err != nil {
				err = msgp.WrapError(err, "ResumeBytesFailed")
				return
			}
		case "QueuedBuckets":
			var zb0002 uint32
			zb0002, bts, err = msgp.ReadArrayHeaderBytes(bts)
			if err != nil {
				err = msgp.WrapError(err, "QueuedBuckets")
				return
			}
			if cap(z.QueuedBuckets) >= int(zb0002) {
				z.QueuedBuckets = (z.QueuedBuckets)[:zb0002]
			} else {
				z.QueuedBuckets = make([]string, zb0002)
			}
			for za0001 := range z.QueuedBuckets {
				z.QueuedBuckets[za0001], bts, err = msgp.ReadStringBytes(bts)
				if err != nil {
					err = msgp.WrapError(err, "QueuedBuckets", za0001)
					return
				}
			}
		case "HealedBuckets":
			var zb0003 uint32
			zb0003, bts, err = msgp.ReadArrayHeaderBytes(bts)
			if err != nil {
				err = msgp.WrapError(err, "HealedBuckets")
				return
			}
			if cap(z.HealedBuckets) >= int(zb0003) {
				z.HealedBuckets = (z.HealedBuckets)[:zb0003]
			} else {
				z.HealedBuckets = make([]string, zb0003)
			}
			for za0002 := range z.HealedBuckets {
				z.HealedBuckets[za0002], bts, err = msgp.ReadStringBytes(bts)
				if err != nil {
					err = msgp.WrapError(err, "HealedBuckets", za0002)
					return
				}
			}
		default:
			bts, err = msgp.Skip(bts)
			if err != nil {
				err = msgp.WrapError(err)
				return
			}
		}
	}
	o = bts
	return
}

// Msgsize returns an upper bound estimate of the number of bytes occupied by the serialized message
func (z *healingTracker) Msgsize() (s int) {
	s = 3 + 3 + msgp.StringPrefixSize + len(z.ID) + 10 + msgp.IntSize + 9 + msgp.IntSize + 10 + msgp.IntSize + 5 + msgp.StringPrefixSize + len(z.Path) + 9 + msgp.StringPrefixSize + len(z.Endpoint) + 8 + msgp.TimeSize + 11 + msgp.TimeSize + 18 + msgp.Uint64Size + 17 + msgp.Uint64Size + 12 + msgp.Uint64Size + 12 + msgp.Uint64Size + 10 + msgp.Uint64Size + 12 + msgp.Uint64Size + 7 + msgp.StringPrefixSize + len(z.Bucket) + 7 + msgp.StringPrefixSize + len(z.Object) + 18 + msgp.Uint64Size + 18 + msgp.Uint64Size + 16 + msgp.Uint64Size + 18 + msgp.Uint64Size + 14 + msgp.ArrayHeaderSize
	for za0001 := range z.QueuedBuckets {
		s += msgp.StringPrefixSize + len(z.QueuedBuckets[za0001])
	}
	s += 14 + msgp.ArrayHeaderSize
	for za0002 := range z.HealedBuckets {
		s += msgp.StringPrefixSize + len(z.HealedBuckets[za0002])
	}
	return
}
