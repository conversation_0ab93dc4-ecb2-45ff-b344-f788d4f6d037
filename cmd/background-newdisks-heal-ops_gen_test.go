package cmd

// Code generated by github.com/tinylib/msgp DO NOT EDIT.

import (
	"bytes"
	"testing"

	"github.com/tinylib/msgp/msgp"
)

func TestMarshalUnmarshalhealingTracker(t *testing.T) {
	v := healingTracker{}
	bts, err := v.MarshalMsg(nil)
	if err != nil {
		t.<PERSON>al(err)
	}
	left, err := v.UnmarshalMsg(bts)
	if err != nil {
		t.<PERSON><PERSON>(err)
	}
	if len(left) > 0 {
		t.<PERSON><PERSON><PERSON>("%d bytes left over after UnmarshalMsg(): %q", len(left), left)
	}

	left, err = msgp.Skip(bts)
	if err != nil {
		t.<PERSON><PERSON>(err)
	}
	if len(left) > 0 {
		t.<PERSON><PERSON><PERSON>("%d bytes left over after Skip(): %q", len(left), left)
	}
}

func BenchmarkMarshalMsghealingTracker(b *testing.B) {
	v := healingTracker{}
	b.Report<PERSON>llocs()
	b.<PERSON>setTimer()
	for i := 0; i < b.N; i++ {
		v.MarshalMsg(nil)
	}
}

func BenchmarkAppendMsghealingTracker(b *testing.B) {
	v := healingTracker{}
	bts := make([]byte, 0, v.Msgsize())
	bts, _ = v.MarshalMsg(bts[0:0])
	b.SetBytes(int64(len(bts)))
	b.ReportAllocs()
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		bts, _ = v.MarshalMsg(bts[0:0])
	}
}

func BenchmarkUnmarshalhealingTracker(b *testing.B) {
	v := healingTracker{}
	bts, _ := v.MarshalMsg(nil)
	b.ReportAllocs()
	b.SetBytes(int64(len(bts)))
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := v.UnmarshalMsg(bts)
		if err != nil {
			b.Fatal(err)
		}
	}
}

func TestEncodeDecodehealingTracker(t *testing.T) {
	v := healingTracker{}
	var buf bytes.Buffer
	msgp.Encode(&buf, &v)

	m := v.Msgsize()
	if buf.Len() > m {
		t.Log("WARNING: TestEncodeDecodehealingTracker Msgsize() is inaccurate")
	}

	vn := healingTracker{}
	err := msgp.Decode(&buf, &vn)
	if err != nil {
		t.Error(err)
	}

	buf.Reset()
	msgp.Encode(&buf, &v)
	err = msgp.NewReader(&buf).Skip()
	if err != nil {
		t.Error(err)
	}
}

func BenchmarkEncodehealingTracker(b *testing.B) {
	v := healingTracker{}
	var buf bytes.Buffer
	msgp.Encode(&buf, &v)
	b.SetBytes(int64(buf.Len()))
	en := msgp.NewWriter(msgp.Nowhere)
	b.ReportAllocs()
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		v.EncodeMsg(en)
	}
	en.Flush()
}

func BenchmarkDecodehealingTracker(b *testing.B) {
	v := healingTracker{}
	var buf bytes.Buffer
	msgp.Encode(&buf, &v)
	b.SetBytes(int64(buf.Len()))
	rd := msgp.NewEndlessReader(buf.Bytes(), b)
	dc := msgp.NewReader(rd)
	b.ReportAllocs()
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		err := v.DecodeMsg(dc)
		if err != nil {
			b.Fatal(err)
		}
	}
}
