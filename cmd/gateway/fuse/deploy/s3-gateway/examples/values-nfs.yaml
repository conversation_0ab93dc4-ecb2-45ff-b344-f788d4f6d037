# Example values for NFS deployment
filesystem:
  type: "nfs"
  mountPath: "/data"

replicaCount: 1

image:
  repository: registry.cn-beijing.aliyuncs.com/kubegems/s3-gateway
  tag: "v0.0.1"
  pullPolicy: Always

service:
  type: ClusterIP
  port: 9000

# Filesystem configurations
filesystems:
  nfs:
    image:
      repository: registry.cn-beijing.aliyuncs.com/kubegems/nfs-mount
      tag: latest
      pullPolicy: IfNotPresent
    config:
      server: "nfs-server.kubegems-pai"
      exportPath: "/exported/path"
      version: "4"
      rsize: "1048576"
      wsize: "1048576"
      hard: true
      intr: true
      timeo: "600"

# Volume configuration
fuseVolume:
  hostPath:
    path: "/var/lib/kubegems/nfs"
    type: DirectoryOrCreate

# Readiness probe
readinessProbe:
  exec:
    command: 
    - /bin/sh
    - -c
    - mountpoint -q /data
  initialDelaySeconds: 10
  periodSeconds: 5
  failureThreshold: 3

# Service account
serviceAccount:
  create: true
  automount: true
  annotations: {}
  name: ""

# Pod security context
podSecurityContext: {}

# Container security context
securityContext: {}

# Resources
resources:
  limits:
    cpu: 500m
    memory: 512Mi
  requests:
    cpu: 100m
    memory: 128Mi

# Node selector
nodeSelector: {}

# Tolerations
tolerations: []

# Affinity
affinity: {}

# Autoscaling
autoscaling:
  enabled: false
  minReplicas: 1
  maxReplicas: 100
  targetCPUUtilizationPercentage: 80

# Ingress
ingress:
  enabled: false
  className: ""
  annotations: {}
  hosts:
    - host: s3-gateway.local
      paths:
        - path: /
          pathType: ImplementationSpecific
  tls: []
