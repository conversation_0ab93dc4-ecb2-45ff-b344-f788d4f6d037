# Example values for GlusterFS deployment
filesystem:
  type: "glusterfs"
  mountPath: "/data"

replicaCount: 1

image:
  repository: registry.cn-beijing.aliyuncs.com/kubegems/s3-gateway
  tag: "v0.0.1"
  pullPolicy: Always

service:
  type: ClusterIP
  port: 9000

# Filesystem configurations
filesystems:
  glusterfs:
    image:
      repository: registry.cn-beijing.aliyuncs.com/kubegems/glusterfs-mount
      tag: latest
      pullPolicy: IfNotPresent
    config:
      endpoints: "gluster-node1,gluster-node2,gluster-node3"
      volume: "gv0"
      backupVolfileServers: "gluster-node2,gluster-node3"
      logLevel: "WARNING"
      logFile: "/var/log/glusterfs.log"

# Volume configuration
fuseVolume:
  hostPath:
    path: "/var/lib/kubegems/glusterfs"
    type: DirectoryOrCreate

# Readiness probe
readinessProbe:
  exec:
    command: 
    - /bin/sh
    - -c
    - mountpoint -q /data
  initialDelaySeconds: 10
  periodSeconds: 5
  failureThreshold: 3

# Service account
serviceAccount:
  create: true
  automount: true
  annotations: {}
  name: ""

# Pod security context
podSecurityContext: {}

# Container security context
securityContext: {}

# Resources
resources:
  limits:
    cpu: 500m
    memory: 512Mi
  requests:
    cpu: 100m
    memory: 128Mi

# Node selector
nodeSelector: {}

# Tolerations
tolerations: []

# Affinity
affinity: {}

# Autoscaling
autoscaling:
  enabled: false
  minReplicas: 1
  maxReplicas: 100
  targetCPUUtilizationPercentage: 80

# Ingress
ingress:
  enabled: false
  className: ""
  annotations: {}
  hosts:
    - host: s3-gateway.local
      paths:
        - path: /
          pathType: ImplementationSpecific
  tls: []
