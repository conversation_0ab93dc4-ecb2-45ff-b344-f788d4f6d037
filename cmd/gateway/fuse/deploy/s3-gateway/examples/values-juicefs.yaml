# Example values for JuiceFS deployment
filesystem:
  type: "juicefs"
  mountPath: "/data"

replicaCount: 1

image:
  repository: registry.cn-beijing.aliyuncs.com/kubegems/s3-gateway
  tag: "v0.0.1"
  pullPolicy: Always

service:
  type: ClusterIP
  port: 9000

# JuiceFS specific configuration
juicefs:
  image:
    repository: registry.cn-beijing.aliyuncs.com/kubegems/juicefs-mount
    tag: ce-v1.1.0
    pullPolicy: Always
  name: "juicefs-tenant-default"
  redis: "juicefs-tenant-default-redis-master.juicefs-system/0"
  bucket: "http://xpai-minio.kubegems-pai:9000/pai-juicefs-tenant-default"
  accessKey: "minioadmin"
  secretKey: "minioadmin"
  capacity: "1000"

# Volume configuration
fuseVolume:
  hostPath:
    path: "/var/lib/kubegems/juicefs"
    type: DirectoryOrCreate

# Readiness probe
readinessProbe:
  exec:
    command: 
    - /bin/sh
    - -c
    - mountpoint -q /data
  initialDelaySeconds: 10
  periodSeconds: 5
  failureThreshold: 3

# Service account
serviceAccount:
  create: true
  automount: true
  annotations: {}
  name: ""

# Pod security context
podSecurityContext: {}

# Container security context
securityContext: {}

# Resources
resources:
  limits:
    cpu: 500m
    memory: 512Mi
  requests:
    cpu: 100m
    memory: 128Mi

# Node selector
nodeSelector: {}

# Tolerations
tolerations: []

# Affinity
affinity: {}

# Autoscaling
autoscaling:
  enabled: false
  minReplicas: 1
  maxReplicas: 100
  targetCPUUtilizationPercentage: 80

# Ingress
ingress:
  enabled: false
  className: ""
  annotations: {}
  hosts:
    - host: s3-gateway.local
      paths:
        - path: /
          pathType: ImplementationSpecific
  tls: []
