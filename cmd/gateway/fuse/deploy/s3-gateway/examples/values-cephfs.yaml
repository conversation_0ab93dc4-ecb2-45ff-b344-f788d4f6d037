# Example values for CephFS deployment
filesystem:
  type: "cephfs"
  mountPath: "/data"

replicaCount: 1

image:
  repository: registry.cn-beijing.aliyuncs.com/kubegems/s3-gateway
  tag: "v0.0.1"
  pullPolicy: Always

service:
  type: ClusterIP
  port: 9000

# Filesystem configurations
filesystems:
  cephfs:
    image:
      repository: registry.cn-beijing.aliyuncs.com/kubegems/cephfs-mount
      tag: latest
      pullPolicy: IfNotPresent
    config:
      fsid: "12345678-1234-1234-1234-123456789abc"
      monitors: "ceph-monitor1:6789,ceph-monitor2:6789,ceph-monitor3:6789"
      user: "admin"
      secretKey: "AQD1+..."  # Base64 encoded Ceph secret key
      filesystem: "cephfs"
      authClusterRequired: "cephx"
      authServiceRequired: "cephx"
      authClientRequired: "cephx"

# Volume configuration
fuseVolume:
  hostPath:
    path: "/var/lib/kubegems/cephfs"
    type: DirectoryOrCreate

# Readiness probe
readinessProbe:
  exec:
    command: 
    - /bin/sh
    - -c
    - mountpoint -q /data
  initialDelaySeconds: 10
  periodSeconds: 5
  failureThreshold: 3

# Service account
serviceAccount:
  create: true
  automount: true
  annotations: {}
  name: ""

# Pod security context
podSecurityContext: {}

# Container security context
securityContext: {}

# Resources
resources:
  limits:
    cpu: 500m
    memory: 512Mi
  requests:
    cpu: 100m
    memory: 128Mi

# Node selector
nodeSelector: {}

# Tolerations
tolerations: []

# Affinity
affinity: {}

# Autoscaling
autoscaling:
  enabled: false
  minReplicas: 1
  maxReplicas: 100
  targetCPUUtilizationPercentage: 80

# Ingress
ingress:
  enabled: false
  className: ""
  annotations: {}
  hosts:
    - host: s3-gateway.local
      paths:
        - path: /
          pathType: ImplementationSpecific
  tls: []
