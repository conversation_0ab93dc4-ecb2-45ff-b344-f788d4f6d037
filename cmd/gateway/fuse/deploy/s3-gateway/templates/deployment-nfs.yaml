{{- if .Values.cephfs.enabled -}}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "s3-gateway.fullname" . }}
  labels:
    {{- include "s3-gateway.labels" . | nindent 4 }}
spec:
  {{- if not .Values.autoscaling.enabled }}
  replicas: {{ .Values.replicaCount }}
  {{- end }}
  selector:
    matchLabels:
      {{- include "s3-gateway.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      {{- with .Values.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        {{- include "s3-gateway.labels" . | nindent 8 }}
        {{- with .Values.podLabels }}
        {{- toYaml . | nindent 8 }}
        {{- end }}
    spec:
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "s3-gateway.serviceAccountName" . }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      containers:
        - name: {{ .Chart.Name }}-fuse
          securityContext:
            privileged: true
            runAsUser: 0
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          command:
            - /bin/sh
            - -c
            - |
              /usr/local/bin/juicefs format --storage=s3 --bucket={{ .Values.juicefs.bucket }} --access-key={{ .Values.juicefs.accessKey }} --secret-key={{ .Values.juicefs.secretKey }} {{ .Values.juicefs.name }}-{{ .Release.Namespace }}-redis-master.juicefs-system/0 {{ .Values.juicefs.name }} --capacity={{ .Values.juicefs.capacity }}
              /usr/local/bin/juicefs mount {{ .Values.juicefs.name }}-{{ .Release.Namespace }}-redis-master.juicefs-system/0 /fuse
          lifecycle:
            preStop:
              exec:
                command:
                  - /bin/sh
                  - -c
                  - umount /fuse && rmdir /fuse
          volumeMounts:
            - name: fuse
              mountPath: /fuse
              mountPropagation: Bidirectional
        - name: {{ .Chart.Name }}
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          ports:
            - name: http
              containerPort: {{ .Values.service.port }}
              protocol: TCP
          livenessProbe:
            {{- toYaml .Values.livenessProbe | nindent 12 }}
          readinessProbe:
            {{- toYaml .Values.readinessProbe | nindent 12 }}
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
          {{- with .Values.volumeMounts }}
          volumeMounts:
            {{- toYaml . | nindent 12 }}
          {{- end }}
      {{- with .Values.volumes }}
      volumes:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
{{- end }}
