1. Get the application URL by running these commands:
{{- if .Values.ingress.enabled }}
{{- range $host := .Values.ingress.hosts }}
  {{- range .paths }}
  http{{ if $.Values.ingress.tls }}s{{ end }}://{{ $host.host }}{{ .path }}
  {{- end }}
{{- end }}
{{- else if contains "NodePort" .Values.service.type }}
  export NODE_PORT=$(kubectl get --namespace {{ .Release.Namespace }} -o jsonpath="{.spec.ports[0].nodePort}" services {{ include "s3-gateway.fullname" . }})
  export NODE_IP=$(kubectl get nodes --namespace {{ .Release.Namespace }} -o jsonpath="{.items[0].status.addresses[0].address}")
  echo http://$NODE_IP:$NODE_PORT
{{- else if contains "LoadBalancer" .Values.service.type }}
     NOTE: It may take a few minutes for the LoadBalancer IP to be available.
           You can watch the status of by running 'kubectl get --namespace {{ .Release.Namespace }} svc -w {{ include "s3-gateway.fullname" . }}'
  export SERVICE_IP=$(kubectl get svc --namespace {{ .Release.Namespace }} {{ include "s3-gateway.fullname" . }} --template "{{"{{ range (index .status.loadBalancer.ingress 0) }}{{.}}{{ end }}"}}")
  echo http://$SERVICE_IP:{{ .Values.service.port }}
{{- else if contains "ClusterIP" .Values.service.type }}
  export POD_NAME=$(kubectl get pods --namespace {{ .Release.Namespace }} -l "app.kubernetes.io/name={{ include "s3-gateway.name" . }},app.kubernetes.io/instance={{ .Release.Name }}" -o jsonpath="{.items[0].metadata.name}")
  export CONTAINER_PORT=$(kubectl get pod --namespace {{ .Release.Namespace }} $POD_NAME -o jsonpath="{.spec.containers[1].ports[0].containerPort}")
  echo "Visit http://127.0.0.1:9000 to use your application"
  kubectl --namespace {{ .Release.Namespace }} port-forward $POD_NAME 9000:$CONTAINER_PORT
{{- end }}

2. S3 Gateway Configuration:
   - Filesystem Type: {{ .Values.filesystem.type }}
   - Mount Path: {{ .Values.filesystem.mountPath }}
   - Service Port: {{ .Values.service.port }}

3. Check the status of your deployment:
   kubectl get pods --namespace {{ .Release.Namespace }} -l "app.kubernetes.io/name={{ include "s3-gateway.name" . }},app.kubernetes.io/instance={{ .Release.Name }}"

4. Check if the filesystem is properly mounted:
   kubectl exec --namespace {{ .Release.Namespace }} -it $(kubectl get pods --namespace {{ .Release.Namespace }} -l "app.kubernetes.io/name={{ include "s3-gateway.name" . }},app.kubernetes.io/instance={{ .Release.Name }}" -o jsonpath="{.items[0].metadata.name}") -c {{ .Chart.Name }} -- mountpoint {{ .Values.filesystem.mountPath }}

5. View logs:
   # Main container logs
   kubectl logs --namespace {{ .Release.Namespace }} -l "app.kubernetes.io/name={{ include "s3-gateway.name" . }},app.kubernetes.io/instance={{ .Release.Name }}" -c {{ .Chart.Name }}
   
   # Sidecar container logs
   kubectl logs --namespace {{ .Release.Namespace }} -l "app.kubernetes.io/name={{ include "s3-gateway.name" . }},app.kubernetes.io/instance={{ .Release.Name }}" -c {{ .Values.filesystem.type }}-mount

{{- $fsConfig := index .Values.filesystems .Values.filesystem.type }}
{{- if eq .Values.filesystem.type "juicefs" }}
6. JuiceFS specific information:
   - JuiceFS Name: {{ $fsConfig.config.name }}
   - Redis Connection: {{ $fsConfig.config.redis }}
   - S3 Bucket: {{ $fsConfig.config.bucket }}
   - Capacity: {{ $fsConfig.config.capacity }}GB
   - Cache Directory: {{ $fsConfig.config.cacheDir }}
   - Cache Size: {{ $fsConfig.config.cacheSize }}MB
{{- else if eq .Values.filesystem.type "cephfs" }}
6. CephFS specific information:
   - Monitors: {{ $fsConfig.config.monitors }}
   - User: {{ $fsConfig.config.user }}
   - Filesystem: {{ $fsConfig.config.filesystem }}
   - FSID: {{ $fsConfig.config.fsid }}
{{- else if eq .Values.filesystem.type "nfs" }}
6. NFS specific information:
   - Server: {{ $fsConfig.config.server }}
   - Export Path: {{ $fsConfig.config.exportPath }}
   - NFS Version: {{ $fsConfig.config.version }}
   - Read Size: {{ $fsConfig.config.rsize }}
   - Write Size: {{ $fsConfig.config.wsize }}
{{- else if eq .Values.filesystem.type "glusterfs" }}
6. GlusterFS specific information:
   - Endpoints: {{ $fsConfig.config.endpoints }}
   - Volume: {{ $fsConfig.config.volume }}
   - Backup Servers: {{ $fsConfig.config.backupVolfileServers }}
   - Log Level: {{ $fsConfig.config.logLevel }}
{{- end }}

7. Configuration file location:
   kubectl get configmap --namespace {{ .Release.Namespace }} {{ include "s3-gateway.fullname" . }}-config -o yaml
