1. Get the application URL by running these commands:
{{- if .Values.ingress.enabled }}
{{- range $host := .Values.ingress.hosts }}
  {{- range .paths }}
  http{{ if $.Values.ingress.tls }}s{{ end }}://{{ $host.host }}{{ .path }}
  {{- end }}
{{- end }}
{{- else if contains "NodePort" .Values.service.type }}
  export NODE_PORT=$(kubectl get --namespace {{ .Release.Namespace }} -o jsonpath="{.spec.ports[0].nodePort}" services {{ include "s3-gateway.fullname" . }})
  export NODE_IP=$(kubectl get nodes --namespace {{ .Release.Namespace }} -o jsonpath="{.items[0].status.addresses[0].address}")
  echo http://$NODE_IP:$NODE_PORT
{{- else if contains "LoadBalancer" .Values.service.type }}
     NOTE: It may take a few minutes for the LoadBalancer IP to be available.
           You can watch the status of by running 'kubectl get --namespace {{ .Release.Namespace }} svc -w {{ include "s3-gateway.fullname" . }}'
  export SERVICE_IP=$(kubectl get svc --namespace {{ .Release.Namespace }} {{ include "s3-gateway.fullname" . }} --template "{{"{{ range (index .status.loadBalancer.ingress 0) }}{{.}}{{ end }}"}}")
  echo http://$SERVICE_IP:{{ .Values.service.port }}
{{- else if contains "ClusterIP" .Values.service.type }}
  export POD_NAME=$(kubectl get pods --namespace {{ .Release.Namespace }} -l "app.kubernetes.io/name={{ include "s3-gateway.name" . }},app.kubernetes.io/instance={{ .Release.Name }}" -o jsonpath="{.items[0].metadata.name}")
  export CONTAINER_PORT=$(kubectl get pod --namespace {{ .Release.Namespace }} $POD_NAME -o jsonpath="{.spec.containers[1].ports[0].containerPort}")
  echo "Visit http://127.0.0.1:9000 to use your application"
  kubectl --namespace {{ .Release.Namespace }} port-forward $POD_NAME 9000:$CONTAINER_PORT
{{- end }}

2. S3 Gateway Configuration:
   - Filesystem Type: {{ .Values.filesystem.type }}
   - Mount Path: {{ .Values.filesystem.mountPath }}
   - Service Port: {{ .Values.service.port }}

3. Check the status of your deployment:
   kubectl get pods --namespace {{ .Release.Namespace }} -l "app.kubernetes.io/name={{ include "s3-gateway.name" . }},app.kubernetes.io/instance={{ .Release.Name }}"

4. Check if the filesystem is properly mounted:
   kubectl exec --namespace {{ .Release.Namespace }} -it $(kubectl get pods --namespace {{ .Release.Namespace }} -l "app.kubernetes.io/name={{ include "s3-gateway.name" . }},app.kubernetes.io/instance={{ .Release.Name }}" -o jsonpath="{.items[0].metadata.name}") -c {{ .Chart.Name }} -- mountpoint {{ .Values.filesystem.mountPath }}

5. View logs:
   # Main container logs
   kubectl logs --namespace {{ .Release.Namespace }} -l "app.kubernetes.io/name={{ include "s3-gateway.name" . }},app.kubernetes.io/instance={{ .Release.Name }}" -c {{ .Chart.Name }}
   
   # Sidecar container logs
   {{- if eq .Values.filesystem.type "juicefs" }}
   kubectl logs --namespace {{ .Release.Namespace }} -l "app.kubernetes.io/name={{ include "s3-gateway.name" . }},app.kubernetes.io/instance={{ .Release.Name }}" -c juicefs-mount
   {{- else if eq .Values.filesystem.type "cephfs" }}
   kubectl logs --namespace {{ .Release.Namespace }} -l "app.kubernetes.io/name={{ include "s3-gateway.name" . }},app.kubernetes.io/instance={{ .Release.Name }}" -c cephfs-mount
   {{- else if eq .Values.filesystem.type "nfs" }}
   kubectl logs --namespace {{ .Release.Namespace }} -l "app.kubernetes.io/name={{ include "s3-gateway.name" . }},app.kubernetes.io/instance={{ .Release.Name }}" -c nfs-mount
   {{- else if eq .Values.filesystem.type "glusterfs" }}
   kubectl logs --namespace {{ .Release.Namespace }} -l "app.kubernetes.io/name={{ include "s3-gateway.name" . }},app.kubernetes.io/instance={{ .Release.Name }}" -c glusterfs-mount
   {{- end }}

{{- if eq .Values.filesystem.type "juicefs" }}
6. JuiceFS specific information:
   - JuiceFS Name: {{ .Values.juicefs.name }}
   - Redis Connection: {{ .Values.juicefs.redis }}
   - S3 Bucket: {{ .Values.juicefs.bucket }}
   - Capacity: {{ .Values.juicefs.capacity }}GB
{{- else if eq .Values.filesystem.type "cephfs" }}
6. CephFS specific information:
   - Monitors: {{ .Values.cephfs.monitors }}
   - User: {{ .Values.cephfs.user }}
   - Filesystem: {{ .Values.cephfs.filesystem }}
{{- else if eq .Values.filesystem.type "nfs" }}
6. NFS specific information:
   - Server: {{ .Values.nfs.server }}
   - Export Path: {{ .Values.nfs.exportPath }}
   - Mount Options: {{ .Values.nfs.mountOptions }}
{{- else if eq .Values.filesystem.type "glusterfs" }}
6. GlusterFS specific information:
   - Endpoints: {{ .Values.glusterfs.endpoints }}
   - Volume: {{ .Values.glusterfs.volume }}
   - Mount Options: {{ .Values.glusterfs.mountOptions }}
{{- end }}
