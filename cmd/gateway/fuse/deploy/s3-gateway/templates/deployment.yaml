apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "s3-gateway.fullname" . }}
  labels:
    {{- include "s3-gateway.labels" . | nindent 4 }}
spec:
  {{- if not .Values.autoscaling.enabled }}
  replicas: {{ .Values.replicaCount }}
  {{- end }}
  selector:
    matchLabels:
      {{- include "s3-gateway.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      {{- with .Values.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        {{- include "s3-gateway.labels" . | nindent 8 }}
        {{- with .Values.podLabels }}
        {{- toYaml . | nindent 8 }}
        {{- end }}
    spec:
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "s3-gateway.serviceAccountName" . }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      containers:
        # Filesystem sidecar container
        {{- $fsConfig := index .Values.filesystems .Values.filesystem.type }}
        - name: {{ .Values.filesystem.type }}-mount
          securityContext:
            privileged: true
            runAsUser: 0
          image: "{{ $fsConfig.image.repository }}:{{ $fsConfig.image.tag }}"
          imagePullPolicy: {{ $fsConfig.image.pullPolicy }}
          lifecycle:
            preStop:
              exec:
                command:
                - /bin/sh
                - -c
                - umount /fuse && rmdir /fuse
          command:
            - /usr/local/bin/entrypoint.sh
          env:
            - name: FILESYSTEM_TYPE
              value: {{ .Values.filesystem.type | quote }}
          volumeMounts:
            - name: fuse
              mountPath: /fuse
              mountPropagation: Bidirectional
            - name: xpai-config
              mountPath: /etc/xpai
              readOnly: true
            {{- if eq .Values.filesystem.type "juicefs" }}
            - name: juicefs-cache
              mountPath: {{ $fsConfig.config.cacheDir | default "/var/jfsCache" }}
            {{- end }}
          
        
        # S3 Gateway main container
        - name: {{ .Chart.Name }}
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          args:
            - gateway
            - {{ .Values.filesystem.mountPath }}
          ports:
            - name: http
              containerPort: {{ .Values.service.port }}
              protocol: TCP
          readinessProbe:
            {{- toYaml .Values.readinessProbe | nindent 12 }}
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
          volumeMounts:
            - name: fuse
              mountPath: {{ .Values.filesystem.mountPath }}
              mountPropagation: HostToContainer
          readinessProbe:
            exec:
              command:
                - /bin/sh
                - -c
                - check_mount.sh {{ .Values.filesystem.mountPath }} fuse.juicefs
            initialDelaySeconds: 10
            periodSeconds: 5
            timeoutSeconds: 5
            failureThreshold: 3
      volumes:
        - name: fuse
          {{- toYaml .Values.fuseVolume | nindent 10 }}
        - name: xpai-config
          configMap:
            name: {{ include "s3-gateway.fullname" . }}-config
        {{- if eq .Values.filesystem.type "juicefs" }}
        - name: juicefs-cache
          {{- $fsConfig := index .Values.filesystems .Values.filesystem.type }}
          {{- if .Values.fuseVolume.hostPath }}
          hostPath:
            path: {{ .Values.fuseVolume.hostPath.path | replace "/fuse" "/cache" }}
            type: DirectoryOrCreate
          {{- else }}
          emptyDir:
            sizeLimit: {{ $fsConfig.config.cacheSize | default "1024" }}Mi
          {{- end }}
        {{- end }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
