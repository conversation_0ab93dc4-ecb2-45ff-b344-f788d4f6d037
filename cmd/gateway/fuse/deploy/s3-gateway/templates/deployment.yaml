apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "s3-gateway.fullname" . }}
  labels:
    {{- include "s3-gateway.labels" . | nindent 4 }}
spec:
  {{- if not .Values.autoscaling.enabled }}
  replicas: {{ .Values.replicaCount }}
  {{- end }}
  selector:
    matchLabels:
      {{- include "s3-gateway.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      {{- with .Values.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        {{- include "s3-gateway.labels" . | nindent 8 }}
        {{- with .Values.podLabels }}
        {{- toYaml . | nindent 8 }}
        {{- end }}
    spec:
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "s3-gateway.serviceAccountName" . }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      containers:
        # Filesystem sidecar container
        {{- if eq .Values.filesystem.type "juicefs" }}
        - name: juicefs-mount
          securityContext:
            privileged: true
            runAsUser: 0
          image: "{{ .Values.juicefs.image.repository }}:{{ .Values.juicefs.image.tag }}"
          imagePullPolicy: {{ .Values.juicefs.image.pullPolicy }}
          command:
            - /bin/sh
            - -c
            - |
              /usr/local/bin/juicefs format --storage=s3 --bucket={{ .Values.juicefs.bucket }} --access-key={{ .Values.juicefs.accessKey }} --secret-key={{ .Values.juicefs.secretKey }} {{ .Values.juicefs.redis }} {{ .Values.juicefs.name }} --capacity={{ .Values.juicefs.capacity }}
              /usr/local/bin/juicefs mount {{ .Values.juicefs.redis }} /fuse
          lifecycle:
            preStop:
              exec:
                command:
                  - /bin/sh
                  - -c
                  - umount /fuse && rmdir /fuse
          volumeMounts:
            - name: fuse
              mountPath: /fuse
              mountPropagation: Bidirectional
        {{- else if eq .Values.filesystem.type "cephfs" }}
        - name: cephfs-mount
          securityContext:
            privileged: true
            runAsUser: 0
          image: "{{ .Values.cephfs.image.repository }}:{{ .Values.cephfs.image.tag }}"
          imagePullPolicy: {{ .Values.cephfs.image.pullPolicy }}
          command:
            - /bin/sh
            - -c
            - |
              echo "{{ .Values.cephfs.secretKey }}" | base64 -d > /tmp/ceph.key
              mount -t ceph {{ .Values.cephfs.monitors }}:/ /fuse -o name={{ .Values.cephfs.user }},secretfile=/tmp/ceph.key,fs={{ .Values.cephfs.filesystem }}
              tail -f /dev/null
          lifecycle:
            preStop:
              exec:
                command:
                  - /bin/sh
                  - -c
                  - umount /fuse
          volumeMounts:
            - name: fuse
              mountPath: /fuse
              mountPropagation: Bidirectional
        {{- else if eq .Values.filesystem.type "nfs" }}
        - name: nfs-mount
          securityContext:
            privileged: true
            runAsUser: 0
          image: "{{ .Values.nfs.image.repository }}:{{ .Values.nfs.image.tag }}"
          imagePullPolicy: {{ .Values.nfs.image.pullPolicy }}
          command:
            - /bin/sh
            - -c
            - |
              mount -t nfs -o {{ .Values.nfs.mountOptions }} {{ .Values.nfs.server }}:{{ .Values.nfs.exportPath }} /fuse
              tail -f /dev/null
          lifecycle:
            preStop:
              exec:
                command:
                  - /bin/sh
                  - -c
                  - umount /fuse
          volumeMounts:
            - name: fuse
              mountPath: /fuse
              mountPropagation: Bidirectional
        {{- else if eq .Values.filesystem.type "glusterfs" }}
        - name: glusterfs-mount
          securityContext:
            privileged: true
            runAsUser: 0
          image: "{{ .Values.glusterfs.image.repository }}:{{ .Values.glusterfs.image.tag }}"
          imagePullPolicy: {{ .Values.glusterfs.image.pullPolicy }}
          command:
            - /bin/sh
            - -c
            - |
              mount -t glusterfs -o {{ .Values.glusterfs.mountOptions }} {{ .Values.glusterfs.endpoints }}:{{ .Values.glusterfs.volume }} /fuse
              tail -f /dev/null
          lifecycle:
            preStop:
              exec:
                command:
                  - /bin/sh
                  - -c
                  - umount /fuse
          volumeMounts:
            - name: fuse
              mountPath: /fuse
              mountPropagation: Bidirectional
        {{- end }}
        
        # S3 Gateway main container
        - name: {{ .Chart.Name }}
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          args:
            - gateway
            - {{ .Values.filesystem.type }}
          ports:
            - name: http
              containerPort: {{ .Values.service.port }}
              protocol: TCP
          readinessProbe:
            {{- toYaml .Values.readinessProbe | nindent 12 }}
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
          volumeMounts:
            - name: fuse
              mountPath: {{ .Values.filesystem.mountPath }}
              mountPropagation: HostToContainer
      volumes:
        - name: fuse
          {{- toYaml .Values.fuseVolume | nindent 10 }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
