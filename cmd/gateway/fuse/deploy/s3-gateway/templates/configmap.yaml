apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "s3-gateway.fullname" . }}-config
  labels:
    {{- include "s3-gateway.labels" . | nindent 4 }}
data:
  config.json: |
    {{- $fsConfig := index .Values.filesystems .Values.filesystem.type }}
    {{- if eq .Values.filesystem.type "juicefs" }}
    {
      "targetPath": {{ .Values.filesystem.mountPath | quote }},
      "mounts": [
        {
          "mountPoint": {{ $fsConfig.config.redis | quote }},
          "options": {
            "name": {{ $fsConfig.config.name | quote }},
            "storage": {{ $fsConfig.config.storage | quote }},
            "bucket": {{ $fsConfig.config.bucket | quote }},
            "accessKey": {{ $fsConfig.config.accessKey | quote }},
            "secretKey": {{ $fsConfig.config.secretKey | quote }},
            "capacity": {{ $fsConfig.config.capacity | quote }},
            "cache-dir": {{ $fsConfig.config.cacheDir | default "/var/jfsCache" | quote }},
            "cache-size": {{ $fsConfig.config.cacheSize | default "1024" | quote }},
            "free-space-ratio": {{ $fsConfig.config.freeSpaceRatio | default "0.1" | quote }}
          }
        }
      ]
    }
    {{- else if eq .Values.filesystem.type "cephfs" }}
    {
      "targetPath": {{ .Values.filesystem.mountPath | quote }},
      "mounts": [
        {
          "mountPoint": "ceph://{{ $fsConfig.config.monitors }}/",
          "options": {
            "fsid": {{ $fsConfig.config.fsid | quote }},
            "mon_initial_members": {{ $fsConfig.config.monitors | replace ":6789" "" | replace "," "," | quote }},
            "mon_host": {{ $fsConfig.config.monitors | quote }},
            "auth_cluster_required": {{ $fsConfig.config.authClusterRequired | default "cephx" | quote }},
            "auth_service_required": {{ $fsConfig.config.authServiceRequired | default "cephx" | quote }},
            "auth_client_required": {{ $fsConfig.config.authClientRequired | default "cephx" | quote }},
            "key": {{ $fsConfig.config.secretKey | quote }}
          }
        }
      ]
    }
    {{- else if eq .Values.filesystem.type "nfs" }}
    {
      "targetPath": {{ .Values.filesystem.mountPath | quote }},
      "mounts": [
        {
          "mountPoint": "{{ $fsConfig.config.server }}:{{ $fsConfig.config.exportPath }}",
          "options": {
            "version": {{ $fsConfig.config.version | default "4" | quote }},
            "rsize": {{ $fsConfig.config.rsize | default "1048576" | quote }},
            "wsize": {{ $fsConfig.config.wsize | default "1048576" | quote }},
            "hard": {{ $fsConfig.config.hard | default true }},
            "intr": {{ $fsConfig.config.intr | default true }},
            "timeo": {{ $fsConfig.config.timeo | default "600" | quote }}
          }
        }
      ]
    }
    {{- else if eq .Values.filesystem.type "glusterfs" }}
    {
      "targetPath": {{ .Values.filesystem.mountPath | quote }},
      "mounts": [
        {
          "mountPoint": "{{ $fsConfig.config.endpoints }}:{{ $fsConfig.config.volume }}",
          "options": {
            "volume": {{ $fsConfig.config.volume | quote }},
            "backup-volfile-servers": {{ $fsConfig.config.backupVolfileServers | default "" | quote }},
            "log-level": {{ $fsConfig.config.logLevel | default "WARNING" | quote }},
            "log-file": {{ $fsConfig.config.logFile | default "/var/log/glusterfs.log" | quote }}
          }
        }
      ]
    }
    {{- end }}
