{{/*
Expand the name of the chart.
*/}}
{{- define "s3-gateway.name" -}}
{{- default .Chart.Name .Values.nameOverride | trunc 63 | trimSuffix "-" }}
{{- end }}

{{/*
Create a default fully qualified app name.
We truncate at 63 chars because some Kubernetes name fields are limited to this (by the DNS naming spec).
If release name contains chart name it will be used as a full name.
*/}}
{{- define "s3-gateway.fullname" -}}
{{- if .Values.fullnameOverride }}
{{- .Values.fullnameOverride | trunc 63 | trimSuffix "-" }}
{{- else }}
{{- $name := default .Chart.Name .Values.nameOverride }}
{{- if contains $name .Release.Name }}
{{- .Release.Name | trunc 63 | trimSuffix "-" }}
{{- else }}
{{- printf "%s-%s" .Release.Name $name | trunc 63 | trimSuffix "-" }}
{{- end }}
{{- end }}
{{- end }}

{{/*
Create chart name and version as used by the chart label.
*/}}
{{- define "s3-gateway.chart" -}}
{{- printf "%s-%s" .Chart.Name .Chart.Version | replace "+" "_" | trunc 63 | trimSuffix "-" }}
{{- end }}

{{/*
Common labels
*/}}
{{- define "s3-gateway.labels" -}}
helm.sh/chart: {{ include "s3-gateway.chart" . }}
{{ include "s3-gateway.selectorLabels" . }}
{{- if .Chart.AppVersion }}
app.kubernetes.io/version: {{ .Chart.AppVersion | quote }}
{{- end }}
app.kubernetes.io/managed-by: {{ .Release.Service }}
{{- end }}

{{/*
Selector labels
*/}}
{{- define "s3-gateway.selectorLabels" -}}
app.kubernetes.io/name: {{ include "s3-gateway.name" . }}
app.kubernetes.io/instance: {{ .Release.Name }}
{{- end }}

{{/*
Create the name of the service account to use
*/}}
{{- define "s3-gateway.serviceAccountName" -}}
{{- if .Values.serviceAccount.create }}
{{- default (include "s3-gateway.fullname" .) .Values.serviceAccount.name }}
{{- else }}
{{- default "default" .Values.serviceAccount.name }}
{{- end }}
{{- end }}

{{/*
Get filesystem configuration based on type
*/}}
{{- define "s3-gateway.filesystemConfig" -}}
{{- if eq .Values.filesystem.type "juicefs" }}
{{- .Values.juicefs }}
{{- else if eq .Values.filesystem.type "cephfs" }}
{{- .Values.cephfs }}
{{- else if eq .Values.filesystem.type "nfs" }}
{{- .Values.nfs }}
{{- else if eq .Values.filesystem.type "glusterfs" }}
{{- .Values.glusterfs }}
{{- end }}
{{- end }}
