# S3 Gateway with FUSE Filesystem Support

This Helm chart deploys an S3 Gateway with support for multiple filesystem backends using sidecar containers. The sidecar containers mount different filesystems (JuiceFS, CephFS, NFS, GlusterFS) to a shared volume that the main S3 Gateway container can access.

## Features

- **Multi-filesystem support**: JuiceFS, CephFS, NFS, GlusterFS
- **Sidecar architecture**: Filesystem mounting is handled by dedicated sidecar containers
- **Flexible configuration**: Easy switching between different filesystem types
- **Production ready**: Includes readiness probes, resource limits, and security contexts

## Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                        Pod                                  │
│  ┌─────────────────┐              ┌─────────────────────┐   │
│  │  Sidecar        │              │  S3 Gateway         │   │
│  │  Container      │              │  Main Container     │   │
│  │                 │              │                     │   │
│  │  - <PERSON><PERSON><PERSON>      │    Shared    │  - Reads/Writes     │   │
│  │  - CephFS       │◄──► Volume ◄─┤    to /data         │   │
│  │  - NFS          │    (/fuse)   │  - Serves S3 API    │   │
│  │  - Gluster<PERSON>    │              │                     │   │
│  └─────────────────┘              └─────────────────────┘   │
└─────────────────────────────────────────────────────────────┘
```

## Installation

### Prerequisites

- Kubernetes 1.16+
- Helm 3.0+
- Appropriate filesystem infrastructure (Redis for JuiceFS, Ceph cluster for CephFS, etc.)

### Install with JuiceFS

```bash
helm install my-s3-gateway ./s3-gateway \
  --values examples/values-juicefs.yaml \
  --namespace kubegems-pai \
  --create-namespace
```

### Install with CephFS

```bash
helm install my-s3-gateway ./s3-gateway \
  --values examples/values-cephfs.yaml \
  --namespace kubegems-pai \
  --create-namespace
```

### Install with NFS

```bash
helm install my-s3-gateway ./s3-gateway \
  --values examples/values-nfs.yaml \
  --namespace kubegems-pai \
  --create-namespace
```

### Install with GlusterFS

```bash
helm install my-s3-gateway ./s3-gateway \
  --values examples/values-glusterfs.yaml \
  --namespace kubegems-pai \
  --create-namespace
```

## Configuration

### Filesystem Selection

Set the filesystem type in your values file:

```yaml
filesystem:
  type: "juicefs"  # Options: juicefs, cephfs, nfs, glusterfs
  mountPath: "/data"  # Mount path in the main container
```

### JuiceFS Configuration

```yaml
juicefs:
  image:
    repository: registry.cn-beijing.aliyuncs.com/kubegems/juicefs-mount
    tag: ce-v1.1.0
  name: "juicefs-tenant-default"
  redis: "juicefs-tenant-default-redis-master.juicefs-system/0"
  bucket: "http://xpai-minio.kubegems-pai:9000/pai-juicefs-tenant-default"
  accessKey: "minioadmin"
  secretKey: "minioadmin"
  capacity: "1000"
```

### CephFS Configuration

```yaml
cephfs:
  image:
    repository: registry.cn-beijing.aliyuncs.com/kubegems/cephfs-mount
    tag: latest
  monitors: "ceph-monitor1:6789,ceph-monitor2:6789,ceph-monitor3:6789"
  user: "admin"
  secretKey: "AQD1+..."  # Base64 encoded Ceph secret key
  filesystem: "cephfs"
```

### NFS Configuration

```yaml
nfs:
  image:
    repository: registry.cn-beijing.aliyuncs.com/kubegems/nfs-mount
    tag: latest
  server: "nfs-server.kubegems-pai"
  exportPath: "/exported/path"
  mountOptions: "rw,sync,no_subtree_check"
```

### GlusterFS Configuration

```yaml
glusterfs:
  image:
    repository: registry.cn-beijing.aliyuncs.com/kubegems/glusterfs-mount
    tag: latest
  endpoints: "gluster-node1,gluster-node2,gluster-node3"
  volume: "gv0"
  mountOptions: "defaults,_netdev"
```

## Monitoring

The chart includes readiness probes that check if the filesystem is properly mounted:

```yaml
readinessProbe:
  exec:
    command: 
    - /bin/sh
    - -c
    - mountpoint -q /data
  initialDelaySeconds: 10
  periodSeconds: 5
  failureThreshold: 3
```

## Security

- Sidecar containers run with `privileged: true` to allow filesystem mounting
- Main S3 Gateway container runs with configurable security context
- Service account can be customized with annotations

## Troubleshooting

### Check if filesystem is mounted

```bash
kubectl exec -it <pod-name> -c <main-container> -- mountpoint /data
```

### Check sidecar container logs

```bash
kubectl logs <pod-name> -c <sidecar-container-name>
```

### Common Issues

1. **Mount fails**: Check filesystem infrastructure (Redis for JuiceFS, Ceph cluster for CephFS, etc.)
2. **Permission denied**: Ensure proper security contexts and filesystem permissions
3. **Pod stuck in pending**: Check node selector, tolerations, and resource requirements

## Uninstallation

```bash
helm uninstall my-s3-gateway --namespace kubegems-pai
```
