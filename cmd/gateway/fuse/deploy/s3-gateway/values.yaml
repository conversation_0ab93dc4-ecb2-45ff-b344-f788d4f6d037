# Default values for s3-gateway-fuse.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

replicaCount: 1

# S3 Gateway main container configuration
image:
  repository: registry.cn-beijing.aliyuncs.com/kubegems/s3-gateway
  pullPolicy: Always
  tag: "v0.0.1"

imagePullSecrets: []
nameOverride: ""
fullnameOverride: ""

service:
  type: ClusterIP
  port: 9000

# Filesystem type to use (juicefs, cephfs, nfs, glusterfs)
filesystem:
  type: "juicefs"  # Options: juicefs, cephfs, nfs, glusterfs
  mountPath: "/data"  # Mount path in the main container

# Volume configuration for FUSE mount
fuseVolume:
  hostPath:
    path: "/var/lib/kubegems/fuse"
    type: DirectoryOrCreate


# Filesystem configurations
filesystems:
  # JuiceFS configuration
  juicefs:
    image:
      repository: registry.cn-beijing.aliyuncs.com/kubegems/juicefs-mount
      tag: ce-v1.1.0
      pullPolicy: Always
    config:
      # The name of the JuiceFS filesystem
      name: "juicefs-tenant-default"
      # Redis connection string for metadata storage
      redis: "juicefs-tenant-default-redis-master.juicefs-system/0"
      # Storage backend configuration
      storage: "s3"
      bucket: "http://xpai-minio.kubegems-pai:9000/pai-juicefs-tenant-default"
      accessKey: "minioadmin"
      secretKey: "minioadmin"
      # Capacity of the JuiceFS filesystem in GB
      capacity: "1000"
      # Cache configuration
      cacheDir: "/var/jfsCache"
      cacheSize: "1024"
      freeSpaceRatio: "0.1"

  # CephFS configuration
  cephfs:
    image:
      repository: registry.cn-beijing.aliyuncs.com/kubegems/cephfs-mount
      tag: latest
      pullPolicy: IfNotPresent
    config:
      # Ceph cluster configuration
      fsid: "12345678-1234-1234-1234-123456789abc"
      monitors: "ceph-monitor1:6789,ceph-monitor2:6789,ceph-monitor3:6789"
      user: "admin"
      # Base64 encoded Ceph secret key
      secretKey: "AQD1+..."
      filesystem: "cephfs"
      # Authentication settings
      authClusterRequired: "cephx"
      authServiceRequired: "cephx"
      authClientRequired: "cephx"

  # NFS configuration
  nfs:
    image:
      repository: registry.cn-beijing.aliyuncs.com/kubegems/nfs-mount
      tag: latest
      pullPolicy: IfNotPresent
    config:
      # NFS server and export path
      server: "nfs-server.kubegems-pai"
      exportPath: "/exported/path"
      # NFS mount options
      version: "4"
      rsize: "1048576"
      wsize: "1048576"
      hard: true
      intr: true
      timeo: "600"

  # GlusterFS configuration
  glusterfs:
    image:
      repository: registry.cn-beijing.aliyuncs.com/kubegems/glusterfs-mount
      tag: latest
      pullPolicy: IfNotPresent
    config:
      # GlusterFS cluster configuration
      endpoints: "gluster-node1,gluster-node2,gluster-node3"
      volume: "gv0"
      # Mount options
      backupVolfileServers: "gluster-node2,gluster-node3"
      logLevel: "WARNING"
      logFile: "/var/log/glusterfs.log"




serviceAccount:
  # Specifies whether a service account should be created
  create: true
  # Automatically mount a ServiceAccount's API credentials?
  automount: true
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: ""

podAnnotations: {}
podLabels: {}

podSecurityContext: {}
  # fsGroup: 2000

securityContext: {}
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
  # runAsUser: 1000



ingress:
  enabled: false
  className: ""
  annotations: {}
    # kubernetes.io/ingress.class: nginx
    # kubernetes.io/tls-acme: "true"
  hosts:
    - host: chart-example.local
      paths:
        - path: /
          pathType: ImplementationSpecific
  tls: []
  #  - secretName: chart-example-tls
  #    hosts:
  #      - chart-example.local

resources: {}
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as Minikube. If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
  # limits:
  #   cpu: 100m
  #   memory: 128Mi
  # requests:
  #   cpu: 100m
  #   memory: 128Mi



autoscaling:
  enabled: false
  minReplicas: 1
  maxReplicas: 100
  targetCPUUtilizationPercentage: 80
  # targetMemoryUtilizationPercentage: 80



nodeSelector: {}

tolerations: []

affinity: {}
