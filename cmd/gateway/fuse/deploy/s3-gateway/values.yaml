# Default values for s3-gateway.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

replicaCount: 1

image:
  repository: registry.cn-beijing.aliyuncs.com/kubegems/s3-gateway
  pullPolicy: Always
  # Overrides the image tag whose default is the chart appVersion.
  tag: "v0.0.1"

imagePullSecrets: []
nameOverride: ""
fullnameOverride: ""

service:
  type: ClusterIP
  port: 9000

# Additional volumes on the output Deployment definition.
volumes: 
  - name: fuse
    hostPath:
      path: /var/lib/xpai/juicefs/cluster-id-1/volume
      type: DirectoryOrCreate

# Additional volumeMounts on the output Deployment definition.
volumeMounts: 
  - name: fuse
    mountPath: /data
    mountPropagation: HostToContainer


readinessProbe:
  exec:
    command: 
    - /bin/sh
    - -c
    - check_mount.sh
  initialDelaySeconds: 5
  periodSeconds: 5
  failureThreshold: 3

juicefs:
  enabled: true
  image:
    repository: registry.cn-beijing.aliyuncs.com/kubegems/juicefs-mount
    tag: ce-v1.1.0
    pullPolicy: Always
  # The name of the JuiceFS filesystem
  name: "juicefs-tenant-default"
  # The S3 bucket to use for JuiceFS
  bucket: "http://xpai-minio.kubegems-pai:9000/pai-juicefs-tenant-default"
  # Access key for the S3 bucket
  accessKey: "minioadmin"
  # Secret key for the S3 bucket
  secretKey: "minioadmin"
  # Capacity of the JuiceFS filesystem in GB
  capacity: "1000"

cephfs:
  enabled: false
  image:
    repository: registry.cn-beijing.aliyuncs.com/kubegems/juicefs-mount
    tag: ce-v1.1.0
    pullPolicy: IfNotPresent
  # The name of the CephFS filesystem
  name: "cephfs-tenant-default"
  # The Ceph monitor addresses
  monitors: "ceph-monitor.kubegems-pai:6789"
  # The Ceph user to use for authentication
  user: "admin"
  # The Ceph secret key for the user
  secretKey: "AQD1+..."
  # The path to mount the CephFS filesystem
  mountPath: "/mnt/cephfs"

nfs:
  enabled: false
  image:
    repository: registry.cn-beijing.aliyuncs.com/kubegems/juicefs-mount
    tag: ce-v1.1.0
    pullPolicy: IfNotPresent
  # The NFS server address
  server: "nfs-server.kubegems-pai"
  # The NFS export path to mount
  exportPath: "/exported/path"
# The NFS mount options
  mountOptions: "rw,sync,no_subtree_check"




serviceAccount:
  # Specifies whether a service account should be created
  create: true
  # Automatically mount a ServiceAccount's API credentials?
  automount: true
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: ""

podAnnotations: {}
podLabels: {}

podSecurityContext: {}
  # fsGroup: 2000

securityContext: {}
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
  # runAsUser: 1000



ingress:
  enabled: false
  className: ""
  annotations: {}
    # kubernetes.io/ingress.class: nginx
    # kubernetes.io/tls-acme: "true"
  hosts:
    - host: chart-example.local
      paths:
        - path: /
          pathType: ImplementationSpecific
  tls: []
  #  - secretName: chart-example-tls
  #    hosts:
  #      - chart-example.local

resources: {}
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as Minikube. If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
  # limits:
  #   cpu: 100m
  #   memory: 128Mi
  # requests:
  #   cpu: 100m
  #   memory: 128Mi



autoscaling:
  enabled: false
  minReplicas: 1
  maxReplicas: 100
  targetCPUUtilizationPercentage: 80
  # targetMemoryUtilizationPercentage: 80



nodeSelector: {}

tolerations: []

affinity: {}
