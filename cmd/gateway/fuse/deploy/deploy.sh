#!/bin/bash

# S3 Gateway FUSE Deployment Script
# This script helps deploy the S3 Gateway with different filesystem backends

set -e

# Default values
NAMESPACE="kubegems-pai"
RELEASE_NAME="s3-gateway"
FILESYSTEM_TYPE="juicefs"
CHART_PATH="./s3-gateway"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    cat << EOF
Usage: $0 [OPTIONS]

Deploy S3 Gateway with FUSE filesystem support

OPTIONS:
    -f, --filesystem TYPE    Filesystem type (juicefs|cephfs|nfs|glusterfs) [default: juicefs]
    -n, --namespace NAME     Kubernetes namespace [default: kubegems-pai]
    -r, --release NAME       Helm release name [default: s3-gateway]
    -c, --chart PATH         Chart path [default: ./s3-gateway]
    --dry-run               Perform a dry run
    --upgrade               Upgrade existing release
    --uninstall             Uninstall the release
    -h, --help              Show this help message

EXAMPLES:
    # Deploy with JuiceFS
    $0 -f juicefs

    # Deploy with CephFS in custom namespace
    $0 -f cephfs -n my-namespace -r my-s3-gateway

    # Upgrade existing deployment
    $0 --upgrade -f nfs

    # Dry run deployment
    $0 --dry-run -f glusterfs

    # Uninstall
    $0 --uninstall

EOF
}

# Function to check prerequisites
check_prerequisites() {
    print_info "Checking prerequisites..."
    
    # Check if helm is installed
    if ! command -v helm &> /dev/null; then
        print_error "Helm is not installed. Please install Helm first."
        exit 1
    fi
    
    # Check if kubectl is installed
    if ! command -v kubectl &> /dev/null; then
        print_error "kubectl is not installed. Please install kubectl first."
        exit 1
    fi
    
    # Check if chart directory exists
    if [ ! -d "$CHART_PATH" ]; then
        print_error "Chart directory '$CHART_PATH' not found."
        exit 1
    fi
    
    print_info "Prerequisites check passed."
}

# Function to deploy
deploy() {
    local dry_run_flag=""
    local upgrade_flag=""
    
    if [ "$DRY_RUN" = "true" ]; then
        dry_run_flag="--dry-run"
        print_info "Performing dry run deployment..."
    fi
    
    if [ "$UPGRADE" = "true" ]; then
        upgrade_flag="upgrade"
        print_info "Upgrading release '$RELEASE_NAME'..."
    else
        upgrade_flag="install"
        print_info "Installing release '$RELEASE_NAME'..."
    fi
    
    # Check if values file exists
    local values_file="$CHART_PATH/examples/values-$FILESYSTEM_TYPE.yaml"
    if [ ! -f "$values_file" ]; then
        print_error "Values file for filesystem '$FILESYSTEM_TYPE' not found: $values_file"
        exit 1
    fi
    
    print_info "Using filesystem: $FILESYSTEM_TYPE"
    print_info "Using namespace: $NAMESPACE"
    print_info "Using values file: $values_file"
    
    # Create namespace if it doesn't exist (only for install, not upgrade)
    if [ "$UPGRADE" != "true" ] && [ "$DRY_RUN" != "true" ]; then
        kubectl create namespace "$NAMESPACE" --dry-run=client -o yaml | kubectl apply -f -
    fi
    
    # Deploy with helm
    helm $upgrade_flag "$RELEASE_NAME" "$CHART_PATH" \
        --namespace "$NAMESPACE" \
        --values "$values_file" \
        $dry_run_flag
    
    if [ "$DRY_RUN" != "true" ]; then
        print_info "Deployment completed successfully!"
        print_info "You can check the status with:"
        echo "  kubectl get pods -n $NAMESPACE"
        echo "  helm status $RELEASE_NAME -n $NAMESPACE"
    fi
}

# Function to uninstall
uninstall() {
    print_warn "Uninstalling release '$RELEASE_NAME' from namespace '$NAMESPACE'..."
    helm uninstall "$RELEASE_NAME" --namespace "$NAMESPACE"
    print_info "Uninstallation completed."
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -f|--filesystem)
            FILESYSTEM_TYPE="$2"
            shift 2
            ;;
        -n|--namespace)
            NAMESPACE="$2"
            shift 2
            ;;
        -r|--release)
            RELEASE_NAME="$2"
            shift 2
            ;;
        -c|--chart)
            CHART_PATH="$2"
            shift 2
            ;;
        --dry-run)
            DRY_RUN="true"
            shift
            ;;
        --upgrade)
            UPGRADE="true"
            shift
            ;;
        --uninstall)
            UNINSTALL="true"
            shift
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Validate filesystem type
case $FILESYSTEM_TYPE in
    juicefs|cephfs|nfs|glusterfs)
        ;;
    *)
        print_error "Invalid filesystem type: $FILESYSTEM_TYPE"
        print_error "Supported types: juicefs, cephfs, nfs, glusterfs"
        exit 1
        ;;
esac

# Main execution
if [ "$UNINSTALL" = "true" ]; then
    uninstall
else
    check_prerequisites
    deploy
fi
