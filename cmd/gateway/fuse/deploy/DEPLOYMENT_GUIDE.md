# S3 Gateway FUSE Deployment Guide

本指南详细说明如何使用 Helm chart 部署支持多种文件系统的 S3 Gateway。

## 架构概述

基于 `test-fuse.yaml` 的设计，该 chart 实现了以下架构：

```
┌─────────────────────────────────────────────────────────────┐
│                        Pod                                  │
│  ┌─────────────────┐              ┌─────────────────────┐   │
│  │  Sidecar        │              │  S3 Gateway         │   │
│  │  Container      │              │  Main Container     │   │
│  │                 │              │                     │   │
│  │  - JuiceFS      │    Shared    │  - 读写 /data       │   │
│  │  - CephFS       │◄──► Volume ◄─┤  - 提供 S3 API     │   │
│  │  - NFS          │    (/fuse)   │  - 端口 9000        │   │
│  │  - GlusterFS    │              │                     │   │
│  └─────────────────┘              └─────────────────────┘   │
└─────────────────────────────────────────────────────────────┘
```

## 快速开始

### 1. 使用部署脚本（推荐）

```bash
# 部署 JuiceFS 版本
./deploy.sh -f juicefs -n kubegems-pai

# 部署 CephFS 版本
./deploy.sh -f cephfs -n kubegems-pai

# 部署 NFS 版本
./deploy.sh -f nfs -n kubegems-pai

# 部署 GlusterFS 版本
./deploy.sh -f glusterfs -n kubegems-pai
```

### 2. 使用 Helm 命令

```bash
# JuiceFS
helm install s3-gateway-juicefs ./s3-gateway \
  --values s3-gateway/examples/values-juicefs.yaml \
  --namespace kubegems-pai \
  --create-namespace

# CephFS
helm install s3-gateway-cephfs ./s3-gateway \
  --values s3-gateway/examples/values-cephfs.yaml \
  --namespace kubegems-pai \
  --create-namespace
```

## 配置说明

### 核心配置

```yaml
# 选择文件系统类型
filesystem:
  type: "juicefs"  # 选项: juicefs, cephfs, nfs, glusterfs
  mountPath: "/data"  # 主容器中的挂载路径

# FUSE 卷配置
fuseVolume:
  hostPath:
    path: "/var/lib/kubegems/fuse"
    type: DirectoryOrCreate
```

### JuiceFS 配置示例

```yaml
juicefs:
  image:
    repository: registry.cn-beijing.aliyuncs.com/kubegems/juicefs-mount
    tag: ce-v1.1.0
  name: "juicefs-tenant-default"
  redis: "juicefs-tenant-default-redis-master.juicefs-system/0"
  bucket: "http://xpai-minio.kubegems-pai:9000/pai-juicefs-tenant-default"
  accessKey: "minioadmin"
  secretKey: "minioadmin"
  capacity: "1000"
```

### CephFS 配置示例

```yaml
cephfs:
  image:
    repository: registry.cn-beijing.aliyuncs.com/kubegems/cephfs-mount
    tag: latest
  monitors: "ceph-monitor1:6789,ceph-monitor2:6789,ceph-monitor3:6789"
  user: "admin"
  secretKey: "AQD1+..."  # Base64 编码的 Ceph 密钥
  filesystem: "cephfs"
```

## 验证部署

### 1. 检查 Pod 状态

```bash
kubectl get pods -n kubegems-pai -l app.kubernetes.io/name=s3-gateway-fuse
```

### 2. 检查挂载状态

```bash
# 获取 Pod 名称
POD_NAME=$(kubectl get pods -n kubegems-pai -l app.kubernetes.io/name=s3-gateway-fuse -o jsonpath="{.items[0].metadata.name}")

# 检查挂载点
kubectl exec -n kubegems-pai $POD_NAME -c s3-gateway-fuse -- mountpoint /data
```

### 3. 查看日志

```bash
# 主容器日志
kubectl logs -n kubegems-pai $POD_NAME -c s3-gateway-fuse

# Sidecar 容器日志（以 JuiceFS 为例）
kubectl logs -n kubegems-pai $POD_NAME -c juicefs-mount
```

### 4. 测试 S3 API

```bash
# 端口转发
kubectl port-forward -n kubegems-pai $POD_NAME 9000:9000

# 使用 AWS CLI 测试（在另一个终端）
aws s3 ls --endpoint-url http://localhost:9000
```

## 故障排除

### 常见问题

1. **挂载失败**
   - 检查文件系统基础设施（Redis for JuiceFS, Ceph 集群等）
   - 查看 sidecar 容器日志

2. **权限问题**
   - 确保 sidecar 容器有 `privileged: true`
   - 检查文件系统权限

3. **Pod 卡在 Pending 状态**
   - 检查节点选择器和容忍度
   - 检查资源需求

### 调试命令

```bash
# 进入主容器
kubectl exec -n kubegems-pai -it $POD_NAME -c s3-gateway-fuse -- /bin/sh

# 进入 sidecar 容器
kubectl exec -n kubegems-pai -it $POD_NAME -c juicefs-mount -- /bin/sh

# 查看事件
kubectl describe pod -n kubegems-pai $POD_NAME
```

## 升级和维护

### 升级部署

```bash
# 使用脚本升级
./deploy.sh --upgrade -f juicefs -n kubegems-pai

# 使用 Helm 升级
helm upgrade s3-gateway-juicefs ./s3-gateway \
  --values s3-gateway/examples/values-juicefs.yaml \
  --namespace kubegems-pai
```

### 卸载

```bash
# 使用脚本卸载
./deploy.sh --uninstall -n kubegems-pai

# 使用 Helm 卸载
helm uninstall s3-gateway-juicefs --namespace kubegems-pai
```

## 生产环境建议

1. **资源限制**: 根据实际负载调整 CPU 和内存限制
2. **持久化存储**: 确保底层存储系统的高可用性
3. **监控**: 添加 Prometheus 监控和告警
4. **备份**: 定期备份重要数据
5. **安全**: 使用 Secret 管理敏感信息

## 自定义配置

如需自定义配置，可以：

1. 复制相应的 `values-*.yaml` 文件
2. 修改配置参数
3. 使用自定义配置文件部署

```bash
cp s3-gateway/examples/values-juicefs.yaml my-values.yaml
# 编辑 my-values.yaml
helm install my-s3-gateway ./s3-gateway --values my-values.yaml
```
