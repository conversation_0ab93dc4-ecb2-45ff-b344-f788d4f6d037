package fuse

import (
	"context"

	madmin "github.com/minio/madmin-go"
	minio "github.com/minio/minio/cmd"
)

var _ minio.ObjectLayer = (*fuseObjects)(nil)

type fuseObjects struct {
	minio.ObjectLayer
}

func (f *fuseObjects) IsListenSupported() bool {
	return false
}

func (f *fuseObjects) StorageInfo(ctx context.Context) (si minio.StorageInfo, _ []error) {
	si, errs := f.ObjectLayer.StorageInfo(ctx)
	si.Backend.GatewayOnline = si.Backend.Type == madmin.FS
	si.Backend.Type = madmin.Gateway
	return si, errs
}

func (f *fuseObjects) MakeBucketWithLocation(ctx context.Context, bucket string, opts minio.BucketOptions) error {
	return minio.NotImplemented{}
}

func (f *fuseObjects) DeleteBucket(ctx context.Context, bucket string, opts minio.DeleteBucketOptions) error {
	return minio.NotImplemented{}
}

func (f *fuseObjects) IsTaggingSupported() bool {
	return true
}
