apiVersion: v1
kind: Pod
metadata:
  name: juicefs-test
  namespace: kubegems-pai
spec:
  restartPolicy: OnFailure
  containers:
  - name: mount
    image: registry.cn-beijing.aliyuncs.com/kubegems/juicefs-mount:ce-v1.1.0
    lifecycle:
      preStop:
        exec:
          command:
          - /bin/sh
          - -c
          - umount /fuse && rmdir /fuse
    command:
    - /bin/sh
    - -c
    - |
      /usr/local/bin/juicefs format --storage=s3 --bucket=http://xpai-minio.kubegems-pai:9000/pai-juicefs-tenant-default --access-key=minioadmin --secret-key=minioadmin juicefs-tenant-default-redis-master.juicefs-system/0 juicefs-tenant-default --capacity=1000
      /usr/local/bin/juicefs mount juicefs-tenant-default-redis-master.juicefs-system/0 /fuse
    securityContext:
        privileged: true
        runAsUser: 0
    volumeMounts:
    - name: fuse
      mountPath: /fuse
      mountPropagation: Bidirectional

  - command:
    - /bin/sh
    - -c
    - tail -f /dev/null
    image: registry.cn-beijing.aliyuncs.com/kubegems/s3-gateway:v0.0.1 ##这个镜像就是gateway的镜像
    readinessProbe:
      exec:
        command:
        - /bin/sh
        - -c
        - mountpoint -q /data #检查挂载点的脚本
      initialDelaySeconds: 5
      periodSeconds: 5
      failureThreshold: 3
    name: main
    volumeMounts:
    - mountPath: /data
      name: fuse
      mountPropagation: HostToContainer


  volumes:
  - hostPath:
      path: /var/lib/xpai/juicefs/cluster-id-1/volume
      type: DirectoryOrCreate
    name: fuse
  # - hostPath:
  #     path: /var/jfsCache
  #     type: DirectoryOrCreate
  #   name: cachedir-0