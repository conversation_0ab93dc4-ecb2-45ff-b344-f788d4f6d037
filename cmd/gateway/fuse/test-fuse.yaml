apiVersion: v1
kind: Pod
metadata:
  name: nfs-test
  namespace: kubegems-pai
spec:
  restartPolicy: OnFailure
  containers:
  - name: mount
    image: registry.cn-beijing.aliyuncs.com/kubegems/nfs-mount:v0.0.1
    # lifecycle:
    #   preStop:
    #     exec:
    #       command:
    #       - /bin/sh
    #       - -c
    #       - umount /fuse && rmdir /fuse
    command:
      # - /usr/local/bin/entrypoint.sh
      - /bin/sh
      - -c
      - tail -f /dev/null
    securityContext:
        privileged: true
        runAsUser: 0
    volumeMounts:
    - name: fuse
      mountPath: /fuse
      mountPropagation: Bidirectional
    - name: xpai-config
      mountPath: /etc/xpai
      readOnly: true

  - command:
    - /bin/sh
    - -c
    - tail -f /dev/null
    image: registry.cn-beijing.aliyuncs.com/kubegems/s3-gateway:v0.0.1 ##这个镜像就是gateway的镜像
    # readinessProbe:
    #   exec:
    #     command:
    #     - /bin/sh
    #     - -c
    #     - mountpoint -q /data #检查挂载点的脚本
    #   initialDelaySeconds: 5
    #   periodSeconds: 5
    #   failureThreshold: 3
    name: main
    volumeMounts:
    - mountPath: /data
      name: fuse
      mountPropagation: HostToContainer
  volumes:
  - hostPath:
      path: /var/lib/xpai/juicefs/cluster-id-1/volume
      type: DirectoryOrCreate
    name: fuse
  - name: xpai-config
    configMap:
      name: s3-gateway-config

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: s3-gateway-config
  namespace: kubegems-pai
data:
  config.json: |
    {
      targetPath: /fuse,
      mounts: [
        {
          mountPoint: ************:/nfs-export
        }
      ]
    }
---