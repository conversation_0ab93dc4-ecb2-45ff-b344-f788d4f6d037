apiVersion: v1
kind: Pod
metadata:
  name: nfs-test
  namespace: kubegems-pai
spec:
  restartPolicy: OnFailure
  containers:
  - name: mount
    imagePullPolicy: Always
    # image: registry.cn-beijing.aliyuncs.com/kubegems/nfs-mount:v0.0.1
    image: d3fk/nfs-client:latest
    # lifecycle:
    #   preStop:
    #     exec:
    #       command:
    #       - /bin/sh
    #       - -c
    #       - fusermount -u  /fuse && rmdir /fuse
    # command:
    #   - /usr/local/bin/entrypoint.sh
    securityContext:
        privileged: true
        runAsUser: 0
    env:
    - name: SERVER
      value: "192.168.0.49"
    - name: SHARE
      value: "/nfs-export"
    - name: MOUNTPOINT
      value: "/fuse"
    volumeMounts:
    - name: fuse
      mountPath: /fuse
      mountPropagation: Bidirectional
    - name: xpai-config
      mountPath: /etc/xpai
      readOnly: true

  - command:
    - /bin/sh
    - -c
    - tail -f /dev/null
    imagePullPolicy: Always
    image: registry.cn-beijing.aliyuncs.com/kubegems/s3-gateway:v0.0.1 ##这个镜像就是gateway的镜像
    readinessProbe:
      exec:
        command:
        - /bin/sh
        - -c
        - mountpoint -q /data #检查挂载点的脚本
      initialDelaySeconds: 5
      periodSeconds: 5
      failureThreshold: 3
    name: main
    volumeMounts:
    - mountPath: /data
      name: fuse
      mountPropagation: HostToContainer
  volumes:
  - hostPath:
      path: /var/lib/xpai/juicefs/cluster-id-1/volume
      type: DirectoryOrCreate
    name: fuse
  - name: xpai-config
    configMap:
      name: s3-gateway-config
