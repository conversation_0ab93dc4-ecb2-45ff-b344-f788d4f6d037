package fuse

import (
	"context"
	"os"
	"path"
	"time"

	"github.com/minio/cli"
	madmin "github.com/minio/madmin-go"
	minio "github.com/minio/minio/cmd"
	"github.com/minio/minio/internal/logger"
	etcd "go.etcd.io/etcd/client/v3"
)

const (
	dataDir          = "/data"
	symlinkDataDir   = "/data-symlink"
	storageSetPrefix = "/storageset/"
)

func init() {
	const fuseGatewayTemplate = `NAME:
  {{.HelpName}} - {{.Usage}}

USAGE:
  {{.HelpName}} {{if .VisibleFlags}}[FLAGS]{{end}} PATH
{{if .VisibleFlags}}
FLAGS:
  {{range .VisibleFlags}}{{.}}
  {{end}}{{end}}
PATH:
  path to CephFS mount point

EXAMPLES:
  1. Start minio gateway server for CephFS backend
     {{.Prompt}} {{.EnvVarSetCommand}} MINIO_ROOT_USER{{.AssignmentOperator}}accesskey
     {{.Prompt}} {{.EnvVarSetCommand}} MINIO_ROOT_PASSWORD{{.AssignmentOperator}}secretkey
     {{.Prompt}} {{.HelpName}} /data /data-mirror
`
	minio.RegisterGatewayCommand(cli.Command{
		Name:               minio.FuseGateway,
		Usage:              "User Space FileSystem Storage (Fuse)",
		Action:             fuseGatewayMain,
		CustomHelpTemplate: fuseGatewayTemplate,
		HideHelpCommand:    true,
		Flags: []cli.Flag{
			&cli.StringFlag{
				Name:  "metrics",
				Value: ":9567",
				Usage: "address to export metrics",
			},
		},
	})
}

func fuseGatewayMain(ctx *cli.Context) {
	if !ctx.Args().Present() || ctx.Args().First() == "help" {
		cli.ShowCommandHelpAndExit(ctx, minio.CephFSGateway, 1)
	}
	args := ctx.Args()
	if len(args) < 2 {
		cli.ShowCommandHelpAndExit(ctx, minio.CephFSGateway, 1)
	}
	minio.StartGateway(ctx, &fuse{})
}

var _ minio.Gateway = (*fuse)(nil)

type fuse struct {
	client *etcd.Client
}

// Name implements cmd.Gateway.
func (f *fuse) Name() string {
	return minio.FuseGateway
}

// NewGatewayLayer implements cmd.Gateway.
func (f *fuse) NewGatewayLayer(creds madmin.Credentials) (minio.ObjectLayer, error) {
	var err error
	metaDir := path.Join(dataDir, ".minio.sys")
	if err = os.MkdirAll(metaDir, 0o755); err != nil {
		return nil, err
	}
	if err = os.MkdirAll(symlinkDataDir, 0o755); err != nil {
		return nil, err
	}
	mirrorMetaDir := path.Join(symlinkDataDir, ".minio.sys")
	if _, err = os.Stat(mirrorMetaDir); os.IsNotExist(err) {
		err = os.Symlink(metaDir, mirrorMetaDir)
		if err != nil {
			return nil, err
		}
	}
	go f.watchStorageSet(context.Background())

	//todo
	newObject, err := minio.NewFSObjectLayer(symlinkDataDir)
	if err != nil {
		return nil, err
	}
	co := &fuseObjects{
		ObjectLayer: newObject,
	}
	// expose metrics
	return co, nil
}

// storageset在etcd中存储的结构
/*
key:   /tenants/default/storagesets/storgaeset-default
value: {
	accesskey: ""
	secretKey: ""
	permission: "readwrite"/"readonly"
}
*/
// 软链接就是storageset的名字的目录，链接的目标是csi创建的对应pvc的子目录
// 从etcd watch storageset
func (f *fuse) watchStorageSet(ctx context.Context) {
	ch := f.watch(ctx, storageSetPrefix)
	for event := range ch {
		switch event.action {
		case storageSetActionCreate:
			// 创建子目录
			subDir := path.Join(symlinkDataDir, event.keyPath[len(storageSetPrefix):])
			if _, err := os.Stat(subDir); os.IsNotExist(err) {
				if err := os.MkdirAll(subDir, 0o755); err != nil {
					logger.LogIf(ctx, err)
				}
			}
		case storageSetActionDelete:
			// 删除子目录
			subDir := path.Join(symlinkDataDir, event.keyPath[len(storageSetPrefix):])
			if err := os.RemoveAll(subDir); err != nil {
				logger.LogIf(ctx, err)
			}
		}
	}
}

func (f *fuse) watch(ctx context.Context, keyPrefix string) <-chan storageSetEvent {
	ch := make(chan storageSetEvent)
	go func() {
		for {
		outerLoop:
			watchCh := f.client.Watch(ctx, keyPrefix, etcd.WithPrefix(), etcd.WithKeysOnly())

			for {
				select {
				case <-ctx.Done():
					return
				case watchResp, ok := <-watchCh:
					if !ok {
						time.Sleep(1 * time.Second)
						goto outerLoop
					}
					if err := watchResp.Err(); err != nil {
						logger.LogIf(ctx, err)
						time.Sleep(1 * time.Second)
						goto outerLoop
					}
					for _, event := range watchResp.Events {
						var action storageSetAction

						if event.IsModify() || event.IsCreate() {
							action = storageSetActionCreate
						}
						if event.Type == etcd.EventTypeDelete {
							action = storageSetActionDelete
						}
						ch <- storageSetEvent{
							keyPath: string(event.Kv.Key),
							action:  action,
						}

					}
				}
			}
		}
	}()
	return ch
}

type storageSetAction int

const (
	_ storageSetAction = iota
	storageSetActionCreate
	storageSetActionDelete
)

type storageSetEvent struct {
	keyPath string
	action  storageSetAction
}

type storageset struct {
	name                  string    // 存储集名称
	storageClusterRefence string    // 存储集引用的存储集
	createTime            time.Time // 创建时间

	readOnlyAccessKey string // 只读访问密钥
	readOnlySecretKey string // 只读访问密钥

	readWriteAccessKey string // 读写访问密钥
	readWriteSecretKey string // 读写访问密钥
}
