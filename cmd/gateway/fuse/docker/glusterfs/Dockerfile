FROM ubuntu:jammy

# Install GlusterFS client and dependencies
RUN apt-get update && \
    apt-get install -y \
        glusterfs-client \
        fuse \
        python3 \
        bash \
        util-linux && \
    apt-get clean autoclean && \
    apt-get autoremove --yes && \
    rm -rf /var/lib/{apt,dpkg,cache,log}/

# Add configuration and entrypoint scripts
ADD ./config_init.py /
ADD ./entrypoint.sh /usr/local/bin

# Make entrypoint executable
RUN chmod u+x /usr/local/bin/entrypoint.sh

CMD ["/usr/local/bin/entrypoint.sh"]
