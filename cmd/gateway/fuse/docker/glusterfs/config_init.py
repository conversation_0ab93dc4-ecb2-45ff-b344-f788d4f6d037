#!/usr/bin/env python3

#  Copyright 2023 The Fluid Authors.
#
#  Licensed under the Apache License, Version 2.0 (the "License");
#  you may not use this file except in compliance with the License.
#  You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
#  Unless required by applicable law or agreed to in writing, software
#  distributed under the License is distributed on an "AS IS" BASIS,
#  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
#  See the License for the specific language governing permissions and
#  limitations under the License.

import json
import os
import sys
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def read_config():
    """Read and parse the xpai config.json file"""
    config_path = "/etc/xpai/config.json"
    try:
        with open(config_path, "r") as f:
            content = f.read().strip()
        
        if not content:
            raise ValueError("Config file is empty")
            
        config = json.loads(content)
        logger.info("Successfully loaded config from %s", config_path)
        return config
    except FileNotFoundError:
        logger.error("Config file not found: %s", config_path)
        sys.exit(1)
    except json.JSONDecodeError as e:
        logger.error("Invalid JSON in config file: %s", e)
        sys.exit(1)
    except Exception as e:
        logger.error("Error reading config file: %s", e)
        sys.exit(1)

def validate_config(config):
    """Validate the configuration structure"""
    required_fields = ['targetPath', 'mounts']
    for field in required_fields:
        if field not in config:
            logger.error("Missing required field: %s", field)
            sys.exit(1)
    
    if not config['mounts'] or len(config['mounts']) == 0:
        logger.error("No mounts configured")
        sys.exit(1)
    
    mount = config['mounts'][0]
    if 'mountPoint' not in mount:
        logger.error("Missing mountPoint in mount configuration")
        sys.exit(1)
    
    if 'options' not in mount:
        logger.error("Missing options in mount configuration")
        sys.exit(1)
    
    # Validate GlusterFS specific options
    options = mount['options']
    required_options = ['volume']
    for option in required_options:
        if option not in options:
            logger.error("Missing required GlusterFS option: %s", option)
            sys.exit(1)
    
    logger.info("Configuration validation passed")

def generate_mount_script(config):
    """Generate the GlusterFS mount script"""
    mount = config['mounts'][0]
    options = mount['options']
    target_path = config['targetPath']
    
    # Parse mount point: "gluster-node1,gluster-node2:volume"
    mount_point = mount['mountPoint']
    if ':' in mount_point:
        endpoints, volume = mount_point.rsplit(':', 1)
    else:
        logger.error("Invalid GlusterFS mount point format: %s", mount_point)
        sys.exit(1)
    
    # Extract configuration values
    volume_name = options['volume']
    backup_servers = options.get('backup-volfile-servers', '')
    log_level = options.get('log-level', 'WARNING')
    log_file = options.get('log-file', '/var/log/glusterfs.log')
    
    # Generate the mount script
    script_template = """#!/bin/sh
set -ex

# GlusterFS Configuration
ENDPOINTS="{endpoints}"
VOLUME="{volume}"
TARGET_PATH="{target_path}"
BACKUP_SERVERS="{backup_servers}"
LOG_LEVEL="{log_level}"
LOG_FILE="{log_file}"

# Cleanup function
cleanup() {{
    echo "Received termination signal, unmounting GlusterFS..."
    if mountpoint -q "$TARGET_PATH"; then
        umount "$TARGET_PATH" || fusermount -u "$TARGET_PATH"
    fi
    exit 0
}}

# Set up signal handlers
trap cleanup SIGTERM SIGINT

# Create necessary directories
mkdir -p "$TARGET_PATH"
mkdir -p "$(dirname "$LOG_FILE")"

# Build mount options
MOUNT_OPTIONS="defaults,_netdev"
if [ -n "$BACKUP_SERVERS" ]; then
    MOUNT_OPTIONS="$MOUNT_OPTIONS,backup-volfile-servers=$BACKUP_SERVERS"
fi
MOUNT_OPTIONS="$MOUNT_OPTIONS,log-level=$LOG_LEVEL,log-file=$LOG_FILE"

# Mount the filesystem
echo "Mounting GlusterFS filesystem..."
mount -t glusterfs -o "$MOUNT_OPTIONS" "$ENDPOINTS:$VOLUME" "$TARGET_PATH" &

# Wait for mount to be ready
echo "Waiting for mount to be ready..."
for i in $(seq 1 30); do
    if mountpoint -q "$TARGET_PATH"; then
        echo "GlusterFS mounted successfully at $TARGET_PATH"
        break
    fi
    echo "Waiting for mount... ($i/30)"
    sleep 2
done

if ! mountpoint -q "$TARGET_PATH"; then
    echo "Failed to mount GlusterFS after 60 seconds"
    exit 1
fi

# Keep the container running
echo "GlusterFS mount is ready, keeping container alive..."
while true; do
    if ! mountpoint -q "$TARGET_PATH"; then
        echo "Mount point lost, exiting..."
        exit 1
    fi
    sleep 30
done
""".format(
        endpoints=endpoints,
        volume=volume_name,
        target_path=target_path,
        backup_servers=backup_servers,
        log_level=log_level,
        log_file=log_file
    )
    
    return script_template

def write_mount_script(script_content):
    """Write the mount script to file"""
    script_path = "/mount-glusterfs.sh"
    try:
        with open(script_path, "w") as f:
            f.write(script_content)
        
        # Make the script executable
        os.chmod(script_path, 0o755)
        logger.info("Mount script written to %s", script_path)
    except Exception as e:
        logger.error("Error writing mount script: %s", e)
        sys.exit(1)

def main():
    """Main function"""
    logger.info("Starting GlusterFS configuration initialization...")
    
    # Read and validate configuration
    config = read_config()
    validate_config(config)
    
    # Generate and write mount script
    script_content = generate_mount_script(config)
    write_mount_script(script_content)
    
    logger.info("GlusterFS configuration initialization completed successfully")

if __name__ == '__main__':
    main()
