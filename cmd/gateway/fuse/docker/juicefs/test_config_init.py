#!/usr/bin/env python3

import json
import os
import tempfile
import unittest
import sys
from unittest.mock import patch, mock_open

# Add the current directory to the path to import config_init
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

import config_init

class TestJuiceFSConfigInit(unittest.TestCase):
    
    def setUp(self):
        """Set up test fixtures"""
        self.valid_config = {
            "targetPath": "/fuse",
            "mounts": [
                {
                    "mountPoint": "redis-master.juicefs-system/0",
                    "options": {
                        "name": "test-fs",
                        "storage": "s3",
                        "bucket": "http://minio:9000/test-bucket",
                        "accessKey": "testkey",
                        "secretKey": "testsecret",
                        "capacity": "1000",
                        "cache-dir": "/var/cache",
                        "cache-size": "512"
                    }
                }
            ]
        }
    
    def test_read_config_success(self):
        """Test successful config reading"""
        config_content = json.dumps(self.valid_config)
        
        with patch("builtins.open", mock_open(read_data=config_content)):
            config = config_init.read_config()
            self.assertEqual(config, self.valid_config)
    
    def test_read_config_file_not_found(self):
        """Test config file not found"""
        with patch("builtins.open", side_effect=FileNotFoundError):
            with self.assertRaises(SystemExit):
                config_init.read_config()
    
    def test_read_config_invalid_json(self):
        """Test invalid JSON in config file"""
        with patch("builtins.open", mock_open(read_data="invalid json")):
            with self.assertRaises(SystemExit):
                config_init.read_config()
    
    def test_validate_config_success(self):
        """Test successful config validation"""
        # Should not raise any exception
        config_init.validate_config(self.valid_config)
    
    def test_validate_config_missing_target_path(self):
        """Test validation with missing targetPath"""
        invalid_config = self.valid_config.copy()
        del invalid_config['targetPath']
        
        with self.assertRaises(SystemExit):
            config_init.validate_config(invalid_config)
    
    def test_validate_config_missing_mounts(self):
        """Test validation with missing mounts"""
        invalid_config = self.valid_config.copy()
        del invalid_config['mounts']
        
        with self.assertRaises(SystemExit):
            config_init.validate_config(invalid_config)
    
    def test_validate_config_empty_mounts(self):
        """Test validation with empty mounts"""
        invalid_config = self.valid_config.copy()
        invalid_config['mounts'] = []
        
        with self.assertRaises(SystemExit):
            config_init.validate_config(invalid_config)
    
    def test_validate_config_missing_mount_point(self):
        """Test validation with missing mountPoint"""
        invalid_config = self.valid_config.copy()
        del invalid_config['mounts'][0]['mountPoint']
        
        with self.assertRaises(SystemExit):
            config_init.validate_config(invalid_config)
    
    def test_validate_config_missing_options(self):
        """Test validation with missing options"""
        invalid_config = self.valid_config.copy()
        del invalid_config['mounts'][0]['options']
        
        with self.assertRaises(SystemExit):
            config_init.validate_config(invalid_config)
    
    def test_validate_config_missing_required_option(self):
        """Test validation with missing required JuiceFS option"""
        invalid_config = self.valid_config.copy()
        del invalid_config['mounts'][0]['options']['name']
        
        with self.assertRaises(SystemExit):
            config_init.validate_config(invalid_config)
    
    def test_generate_mount_script(self):
        """Test mount script generation"""
        script = config_init.generate_mount_script(self.valid_config)
        
        # Check that the script contains expected values
        self.assertIn("redis-master.juicefs-system/0", script)
        self.assertIn("test-fs", script)
        self.assertIn("s3", script)
        self.assertIn("http://minio:9000/test-bucket", script)
        self.assertIn("testkey", script)
        self.assertIn("testsecret", script)
        self.assertIn("/fuse", script)
        self.assertIn("/var/cache", script)
        self.assertIn("512", script)
        
        # Check that the script has proper structure
        self.assertIn("#!/bin/sh", script)
        self.assertIn("set -ex", script)
        self.assertIn("juicefs format", script)
        self.assertIn("juicefs mount", script)
        self.assertIn("cleanup()", script)
        self.assertIn("trap cleanup", script)
    
    def test_generate_mount_script_with_defaults(self):
        """Test mount script generation with default values"""
        config = self.valid_config.copy()
        # Remove optional fields
        del config['mounts'][0]['options']['cache-dir']
        del config['mounts'][0]['options']['cache-size']
        
        script = config_init.generate_mount_script(config)
        
        # Check that defaults are used
        self.assertIn("/var/jfsCache", script)  # Default cache-dir
        self.assertIn("1024", script)  # Default cache-size
        self.assertIn("0.1", script)   # Default free-space-ratio
    
    @patch('os.chmod')
    def test_write_mount_script(self, mock_chmod):
        """Test writing mount script to file"""
        script_content = "#!/bin/sh\necho 'test script'"
        
        with patch("builtins.open", mock_open()) as mock_file:
            config_init.write_mount_script(script_content)
            
            # Check that file was opened for writing
            mock_file.assert_called_once_with("/mount-juicefs.sh", "w")
            
            # Check that content was written
            mock_file().write.assert_called_once_with(script_content)
            
            # Check that file was made executable
            mock_chmod.assert_called_once_with("/mount-juicefs.sh", 0o755)

if __name__ == '__main__':
    # Run the tests
    unittest.main(verbosity=2)
