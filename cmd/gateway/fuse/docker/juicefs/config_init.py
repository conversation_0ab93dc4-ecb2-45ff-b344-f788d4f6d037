#  Copyright 2023 The Fluid Authors.
#
#  Licensed under the Apache License, Version 2.0 (the "License");
#  you may not use this file except in compliance with the License.
#  You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
#  Unless required by applicable law or agreed to in writing, software
#  distributed under the License is distributed on an "AS IS" BASIS,
#  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
#  See the License for the specific language governing permissions and
#  limitations under the License.

#!/usr/bin/env python3

import json
import os
import sys
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def read_config():
    """Read and parse the xpai config.json file"""
    config_path = "/etc/xpai/config.json"
    try:
        with open(config_path, "r") as f:
            content = f.read().strip()

        if not content:
            raise ValueError("Config file is empty")

        config = json.loads(content)
        logger.info("Successfully loaded config from %s", config_path)
        return config
    except FileNotFoundError:
        logger.error("Config file not found: %s", config_path)
        sys.exit(1)
    except json.JSONDecodeError as e:
        logger.error("Invalid JSON in config file: %s", e)
        sys.exit(1)
    except Exception as e:
        logger.error("Error reading config file: %s", e)
        sys.exit(1)

def validate_config(config):
    """Validate the configuration structure"""
    required_fields = ['targetPath', 'mounts']
    for field in required_fields:
        if field not in config:
            logger.error("Missing required field: %s", field)
            sys.exit(1)

    if not config['mounts'] or len(config['mounts']) == 0:
        logger.error("No mounts configured")
        sys.exit(1)

    mount = config['mounts'][0]
    if 'mountPoint' not in mount:
        logger.error("Missing mountPoint in mount configuration")
        sys.exit(1)

    if 'options' not in mount:
        logger.error("Missing options in mount configuration")
        sys.exit(1)

    # Validate JuiceFS specific options
    options = mount['options']
    required_options = ['name', 'storage', 'bucket', 'accessKey', 'secretKey']
    for option in required_options:
        if option not in options:
            logger.error("Missing required JuiceFS option: %s", option)
            sys.exit(1)

    logger.info("Configuration validation passed")

def generate_mount_script(config):
    """Generate the JuiceFS mount script"""
    mount = config['mounts'][0]
    options = mount['options']
    target_path = config['targetPath']

    # Extract configuration values
    redis_url = mount['mountPoint']
    fs_name = options['name']
    storage = options['storage']
    bucket = options['bucket']
    access_key = options['accessKey']
    secret_key = options['secretKey']
    capacity = options.get('capacity', '1000')  # Default capacity

    # Additional options
    cache_dir = options.get('cache-dir', '/var/jfsCache')
    cache_size = options.get('cache-size', '1024')
    free_space_ratio = options.get('free-space-ratio', '0.1')

    # Generate the mount script
    script_template = """#!/bin/sh
set -ex

# JuiceFS Configuration
REDIS_URL="{redis_url}"
FS_NAME="{fs_name}"
STORAGE="{storage}"
BUCKET="{bucket}"
ACCESS_KEY="{access_key}"
SECRET_KEY="{secret_key}"
CAPACITY="{capacity}"
TARGET_PATH="{target_path}"
CACHE_DIR="{cache_dir}"
CACHE_SIZE="{cache_size}"
FREE_SPACE_RATIO="{free_space_ratio}"

# Cleanup function
cleanup() {{
    echo "Received termination signal, unmounting JuiceFS..."
    if mountpoint -q "$TARGET_PATH"; then
        umount "$TARGET_PATH" || fusermount -u "$TARGET_PATH"
    fi
    exit 0
}}

# Set up signal handlers
trap cleanup SIGTERM SIGINT

# Create necessary directories
mkdir -p "$TARGET_PATH"
mkdir -p "$CACHE_DIR"

# Check if filesystem already exists, if not format it
if ! /usr/local/bin/juicefs status "$REDIS_URL" >/dev/null 2>&1; then
    echo "Formatting JuiceFS filesystem..."
    /usr/local/bin/juicefs format \\
        --storage="$STORAGE" \\
        --bucket="$BUCKET" \\
        --access-key="$ACCESS_KEY" \\
        --secret-key="$SECRET_KEY" \\
        --capacity="$CAPACITY" \\
        "$REDIS_URL" \\
        "$FS_NAME"
else
    echo "JuiceFS filesystem already exists, skipping format"
fi

# Mount the filesystem
echo "Mounting JuiceFS filesystem..."
/usr/local/bin/juicefs mount \\
    --cache-dir="$CACHE_DIR" \\
    --cache-size="$CACHE_SIZE" \\
    --free-space-ratio="$FREE_SPACE_RATIO" \\
    --no-usage-report \\
    "$REDIS_URL" \\
    "$TARGET_PATH" &

# Wait for mount to be ready
echo "Waiting for mount to be ready..."
for i in $(seq 1 30); do
    if mountpoint -q "$TARGET_PATH"; then
        echo "JuiceFS mounted successfully at $TARGET_PATH"
        break
    fi
    echo "Waiting for mount... ($i/30)"
    sleep 2
done

if ! mountpoint -q "$TARGET_PATH"; then
    echo "Failed to mount JuiceFS after 60 seconds"
    exit 1
fi

# Keep the container running
echo "JuiceFS mount is ready, keeping container alive..."
while true; do
    if ! mountpoint -q "$TARGET_PATH"; then
        echo "Mount point lost, exiting..."
        exit 1
    fi
    sleep 30
done
""".format(
        redis_url=redis_url,
        fs_name=fs_name,
        storage=storage,
        bucket=bucket,
        access_key=access_key,
        secret_key=secret_key,
        capacity=capacity,
        target_path=target_path,
        cache_dir=cache_dir,
        cache_size=cache_size,
        free_space_ratio=free_space_ratio
    )

    return script_template

def write_mount_script(script_content):
    """Write the mount script to file"""
    script_path = "/mount-juicefs.sh"
    try:
        with open(script_path, "w") as f:
            f.write(script_content)

        # Make the script executable
        os.chmod(script_path, 0o755)
        logger.info("Mount script written to %s", script_path)
    except Exception as e:
        logger.error("Error writing mount script: %s", e)
        sys.exit(1)

def main():
    """Main function"""
    logger.info("Starting JuiceFS configuration initialization...")

    # Read and validate configuration
    config = read_config()
    validate_config(config)

    # Generate and write mount script
    script_content = generate_mount_script(config)
    write_mount_script(script_content)

    logger.info("JuiceFS configuration initialization completed successfully")

if __name__ == '__main__':
    main()


# {
#   "targetPath": "/fuse",
#   "mounts": [
#     {
#       "mountPoint": "juicefs-tenant-default-redis-master.juicefs-system/0",
#       "options": {
#         "name": "juicefs-tenant-default",
#         "storage": "s3",
#         "bucket": "http://xpai-minio.kubegems-pai:9000/pai-juicefs-tenant-default",
#         "accessKey": "minioadmin",
#         "secretKey": "minioadmin",
#         "capacity": "1000"
#       }
#     }
#   ]
# }