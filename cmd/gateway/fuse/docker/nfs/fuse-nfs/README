fuse-nfs
========

This is a fuse module that implements the NFS protocol.

Building
========

$ sudo apt-get install libfuse-dev libnfs13 libnfs-dev libtool m4 automake libnfs-dev xsltproc
$ ./setup.sh
$ ./configure
$ make
$ sudo make install

FUSE OPTIONS
============

	[-?|--help]

	fuse-nfs options :
	[-U NFS_UID|--fusenfs_uid=NFS_UID]
		The uid passed within the rpc credentials within the mount point
		This is the same as passing the uid within the url, however if both are defined then the url's one is used
	[-G NFS_GID|--fusenfs_gid=NFS_GID]
		The gid passed within the rpc credentials within the mount point
		This is the same as passing the gid within the url, however if both are defined then the url's one is used
	[-o|--fusenfs_allow_other_own_ids]
		Allow fuse-nfs with allow_user activated to update the rpc credentials with the current (other) user credentials instead
		of using the mount user credentials or (if defined) the custom credentials defined with -U/-G / url
		This option activate allow_other, note that allow_other need user_allow_other to be defined in fuse.conf

	libnfs options :
	[-n SHARE|--nfs_share=SHARE]
		The server export to be mounted
	[-m MNTPOINT|--mountpoint=MNTPOINT]
		The client mount point

	fuse options (see man mount.fuse):
	[-p [0|1]|--default_permissions=[0|1]
		The fuse default_permissions option do not have any argument, 
		for compatibility with previous fuse-nfs version default is activated (1)
		with the possibility to overwrite this behavior (0)
	[-t [0|1]|--multithread=[0|1]]
		Single threaded by default (0) , may have issue with nfs and fuse multithread (1)
	[-a|--allow_other]
		This option overrides the security measure restricting file access to the filesystem owner, 
		so that all users (including root) can access the files.
	[-r|--allow_root]
		This option is similar to allow_other but file access is limited to the filesystem owner and root.  
		This option and allow_other are mutually exclusive.
	[-u FUSE_UID|--uid=FUSE_UID]
		Specifies the numeric uid of the mount owner. 
		Override the st_uid field set by the filesystem.
	[-g FUSE_GID|--gid=FUSE_GID]
		Specifies the numeric gid of the mount owner.
		Override the st_uid field set by the filesystem.
	[-K UMASK|--umask=UMASK]
		Override the permission bits in st_mode set by the filesystem. 
		The resulting permission bits are the ones missing from the given umask value.  
		The value is given in octal representation.
	[-d|--direct_io]
		This option disables the use of page cache (file content  cache) in the kernel for this filesystem. 
		This has several affects:
		       1.	Each read or write system call will initiate one or more read or write operations, data will not be cached in the kernel.
		       2.	The return value of the read and write system calls will correspond to the return values of the read and write operations. 
		       		This is useful for example if the file size is not known in advance (before reading it).
	[-k|--kernel_cache]
		This option disables flushing the cache of the file contents on every open.
		This should only be enabled on filesystems, where the file data is never changed externally (not through the mounted FUSE filesystem).  
		Thus it is not suitable for network filesystems and other "intermediate" filesystems.
	[-c|--auto_cache]
		This option is an alternative to kernel_cache. 
		Instead of unconditionally keeping cached data, the cached data is invalidated on open if the modification time or the size of the file has changed since it was last opened.
	[-E TIMEOUT|--entry_timeout=TIMEOUT]
		The timeout in seconds for which name lookups will be cached.
		The default is 1.0 second. For all the timeout options, it is possible to give fractions of a second as well (e.g. entry_timeout=2.8)
	[-N TIMEOUT|--negative_timeout=TIMEOUT]
		The timeout in seconds for which a negative lookup will be cached.
		This means, that if file did not exist (lookup retuned ENOENT), the lookup will only be redone after the timeout, and the file/directory will be assumed to not exist until then.
		The default is 0.0 second, meaning that caching negative lookups are disabled.		
	[-T TIMEOUT|--attr_timeout=TIMEOUT]
		The timeout in seconds for which file/directory attributes are cached. 
		The default is 1.0 second.
	[-C TIMEOUT|--ac_attr_timeout=TIMEOUT]
		The timeout in seconds for which file attributes are cached for the purpose of checking if auto_cache should flush the file data on open. 
		The default is the value of attr_timeout
	[-l|--large_read]
		This can improve performance for some filesystems, but can also degrade performance. 
		This option is only useful on 2.4.X kernels, as on 2.6 kernels requests size is automatically determined for optimum performance.

ROOT vs NON-ROOT
================
By default, most NFS servers will only allow access from clients that are
using a system port (i.e. a port < 1024).
Since these ports by default can only be used by the root user this will
prevent non-root users from conencting to the NFS server or mounting the
filesystem.

There are two ways to solve this:
1, cap_net_bind_service
-----------------------
On Linux, when this capability is set the kernel will allow that program
to use system ports for any user that runs that program.

  sudo setcap 'cap_net_bind_service=+ep' ./fuse/fuse-nfs

Setting this capability will allow normal non-root users to access the NFS
server.

2, insecure
-----------
Alternatively you can often disable the "chack that the client connects from
a system port" in the NFS server itself.
On Linux NFS servers this is done by adding the "insecure" keyword to
the /etc/exports file.


LIBNFS URL-FORMAT:
===========
Libnfs uses RFC2224 style URLs extended with libnfs specific url arguments some minor extensions.
The basic syntax of these URLs is :

nfs://<server|ipv4|ipv6>/path[?arg=val[&arg=val]*]

Arguments supported by libnfs are :
 tcp-syncnt=<int>  : Number of SYNs to send during the session establish
                     before failing setting up the tcp connection to the
                     server.
 uid=<int>         : UID value to use when talking to the server.
                     default it 65534 on Windows and getuid() on unixen.
 gid=<int>         : GID value to use when talking to the server.
                     default it 65534 on Windows and getgid() on unixen.
 readahead=<int>   : Enable readahead for files and set the maximum amount
                     of readahead to <int>.
 auto-traverse-mounts=<0|1>
                   : Should libnfs try to traverse across nested mounts
					automatically or not. Default is 1 == enabled.
 dircache=<0|1>    : Disable/enable directory caching. Enabled by default.
 if=<interface>    : Interface name (e.g., eth1) to bind; requires `root`
 version=<3|4>     : NFS version to use. Version 3 is the default.


To mount a filesystem:
======================
fuse-nfs -n nfs://127.0.0.1/data/tmp -m /my/mountpoint


To unmount a filesystem:
========================
fusermount -u /my/mountpoint


NFSv4 support:
==============
NFSv4 is supported when used with a recent enough version of libnfs.
To enable NFSv4 support you need to specify version=4 as an URL argument:
fuse-nfs -n nfs://127.0.0.1/data/tmp?version=4 -m /my/mountpoint


Windows
=======
The following are ports to windows:

* Daniel Abrech (https://github.com/Daniel-Abrecht) has ported this fuse module
  to windows using the dokany filesystem/fuse wrapper.

  Windows builds for this module can be downloaded from :
  https://github.com/Daniel-Abrecht/fuse-nfs-crossbuild-scripts/releases

* Bill Zissimopoulos (https://github.com/billziss-gh) has ported this fuse
  module to windows using WinFsp.

  The NFS-Win port (including MSI installers) can be found here:
  https://github.com/billziss-gh/nfs-win
