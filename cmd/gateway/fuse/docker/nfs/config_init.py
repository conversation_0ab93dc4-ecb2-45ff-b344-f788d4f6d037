#!/usr/bin/env python3

import json
import os
import sys
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def read_config():
    """Read and parse the xpai config.json file"""
    config_path = "/etc/xpai/config.json"
    try:
        with open(config_path, "r") as f:
            content = f.read().strip()

        if not content:
            raise ValueError("Config file is empty")

        config = json.loads(content)
        logger.info("Successfully loaded config from %s", config_path)
        return config
    except FileNotFoundError:
        logger.error("Config file not found: %s", config_path)
        sys.exit(1)
    except json.JSONDecodeError as e:
        logger.error("Invalid JSON in config file: %s", e)
        logger.error("Config file content: %s", content if 'content' in locals() else "Unable to read")
        sys.exit(1)
    except Exception as e:
        logger.error("Error reading config file: %s", e)
        sys.exit(1)

def validate_config(config):
    """Validate the configuration structure"""
    required_fields = ['targetPath', 'mounts']
    for field in required_fields:
        if field not in config:
            logger.error("Missing required field: %s", field)
            sys.exit(1)

    if not config['mounts'] or len(config['mounts']) == 0:
        logger.error("No mounts configured")
        sys.exit(1)

    mount = config['mounts'][0]
    if 'mountPoint' not in mount:
        logger.error("Missing mountPoint in mount configuration")
        sys.exit(1)

    logger.info("Configuration validation passed")

def generate_mount_script(config):
    """Generate the NFS mount script"""
    mount = config['mounts'][0]
    target_path = config['targetPath'] #/fuse
    mount_point = mount['mountPoint'] #************:/nfs-export
    # Generate the mount script
    script_template = """#!/bin/sh
set -ex

# NFS Configuration
NFS_MOUNTPOINT="{mount_point}"
TARGET_PATH="{target_path}"


# Set up signal handlers
trap "fusermount -u $TARGET_PATH" SIGTERM

# Create target directory
mkdir -p "$TARGET_PATH"


# Mount using fuse-nfs
echo "Mounting NFS filesystem using fuse-nfs..."
fuse-nfs -n "nfs://$NFS_MOUNTPOINT" -m "$TARGET_PATH"
sleep inf
""".format(
        mount_point=mount_point,
        target_path=target_path,
    )

    return script_template

def write_mount_script(script_content):
    """Write the mount script to file"""
    script_path = "/mount-nfs.sh"
    try:
        with open(script_path, "w") as f:
            f.write(script_content)

        # Make the script executable
        os.chmod(script_path, 0o755)
        logger.info("Mount script written to %s", script_path)
    except Exception as e:
        logger.error("Error writing mount script: %s", e)
        sys.exit(1)

def main():
    """Main function"""
    logger.info("Starting NFS configuration initialization...")

    # Read and validate configuration
    config = read_config()
    validate_config(config)

    # Generate and write mount script
    script_content = generate_mount_script(config)
    write_mount_script(script_content)

    logger.info("NFS configuration initialization completed successfully")

if __name__ == '__main__':
    main()

