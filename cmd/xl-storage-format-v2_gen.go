package cmd

// Code generated by github.com/tinylib/msgp DO NOT EDIT.

import (
	"github.com/tinylib/msgp/msgp"
)

// DecodeMsg implements msgp.Decodable
func (z *ChecksumAlgo) DecodeMsg(dc *msgp.Reader) (err error) {
	{
		var zb0001 uint8
		zb0001, err = dc.ReadUint8()
		if err != nil {
			err = msgp.WrapError(err)
			return
		}
		(*z) = ChecksumAlgo(zb0001)
	}
	return
}

// EncodeMsg implements msgp.Encodable
func (z ChecksumAlgo) EncodeMsg(en *msgp.Writer) (err error) {
	err = en.WriteUint8(uint8(z))
	if err != nil {
		err = msgp.WrapError(err)
		return
	}
	return
}

// MarshalMsg implements msgp.Marshaler
func (z ChecksumAlgo) MarshalMsg(b []byte) (o []byte, err error) {
	o = msgp.Require(b, z.Msgsize())
	o = msgp.AppendUint8(o, uint8(z))
	return
}

// UnmarshalMsg implements msgp.Unmarshaler
func (z *ChecksumAlgo) UnmarshalMsg(bts []byte) (o []byte, err error) {
	{
		var zb0001 uint8
		zb0001, bts, err = msgp.ReadUint8Bytes(bts)
		if err != nil {
			err = msgp.WrapError(err)
			return
		}
		(*z) = ChecksumAlgo(zb0001)
	}
	o = bts
	return
}

// Msgsize returns an upper bound estimate of the number of bytes occupied by the serialized message
func (z ChecksumAlgo) Msgsize() (s int) {
	s = msgp.Uint8Size
	return
}

// DecodeMsg implements msgp.Decodable
func (z *ErasureAlgo) DecodeMsg(dc *msgp.Reader) (err error) {
	{
		var zb0001 uint8
		zb0001, err = dc.ReadUint8()
		if err != nil {
			err = msgp.WrapError(err)
			return
		}
		(*z) = ErasureAlgo(zb0001)
	}
	return
}

// EncodeMsg implements msgp.Encodable
func (z ErasureAlgo) EncodeMsg(en *msgp.Writer) (err error) {
	err = en.WriteUint8(uint8(z))
	if err != nil {
		err = msgp.WrapError(err)
		return
	}
	return
}

// MarshalMsg implements msgp.Marshaler
func (z ErasureAlgo) MarshalMsg(b []byte) (o []byte, err error) {
	o = msgp.Require(b, z.Msgsize())
	o = msgp.AppendUint8(o, uint8(z))
	return
}

// UnmarshalMsg implements msgp.Unmarshaler
func (z *ErasureAlgo) UnmarshalMsg(bts []byte) (o []byte, err error) {
	{
		var zb0001 uint8
		zb0001, bts, err = msgp.ReadUint8Bytes(bts)
		if err != nil {
			err = msgp.WrapError(err)
			return
		}
		(*z) = ErasureAlgo(zb0001)
	}
	o = bts
	return
}

// Msgsize returns an upper bound estimate of the number of bytes occupied by the serialized message
func (z ErasureAlgo) Msgsize() (s int) {
	s = msgp.Uint8Size
	return
}

// DecodeMsg implements msgp.Decodable
func (z *VersionType) DecodeMsg(dc *msgp.Reader) (err error) {
	{
		var zb0001 uint8
		zb0001, err = dc.ReadUint8()
		if err != nil {
			err = msgp.WrapError(err)
			return
		}
		(*z) = VersionType(zb0001)
	}
	return
}

// EncodeMsg implements msgp.Encodable
func (z VersionType) EncodeMsg(en *msgp.Writer) (err error) {
	err = en.WriteUint8(uint8(z))
	if err != nil {
		err = msgp.WrapError(err)
		return
	}
	return
}

// MarshalMsg implements msgp.Marshaler
func (z VersionType) MarshalMsg(b []byte) (o []byte, err error) {
	o = msgp.Require(b, z.Msgsize())
	o = msgp.AppendUint8(o, uint8(z))
	return
}

// UnmarshalMsg implements msgp.Unmarshaler
func (z *VersionType) UnmarshalMsg(bts []byte) (o []byte, err error) {
	{
		var zb0001 uint8
		zb0001, bts, err = msgp.ReadUint8Bytes(bts)
		if err != nil {
			err = msgp.WrapError(err)
			return
		}
		(*z) = VersionType(zb0001)
	}
	o = bts
	return
}

// Msgsize returns an upper bound estimate of the number of bytes occupied by the serialized message
func (z VersionType) Msgsize() (s int) {
	s = msgp.Uint8Size
	return
}

// DecodeMsg implements msgp.Decodable
func (z *xlFlags) DecodeMsg(dc *msgp.Reader) (err error) {
	{
		var zb0001 uint8
		zb0001, err = dc.ReadUint8()
		if err != nil {
			err = msgp.WrapError(err)
			return
		}
		(*z) = xlFlags(zb0001)
	}
	return
}

// EncodeMsg implements msgp.Encodable
func (z xlFlags) EncodeMsg(en *msgp.Writer) (err error) {
	err = en.WriteUint8(uint8(z))
	if err != nil {
		err = msgp.WrapError(err)
		return
	}
	return
}

// MarshalMsg implements msgp.Marshaler
func (z xlFlags) MarshalMsg(b []byte) (o []byte, err error) {
	o = msgp.Require(b, z.Msgsize())
	o = msgp.AppendUint8(o, uint8(z))
	return
}

// UnmarshalMsg implements msgp.Unmarshaler
func (z *xlFlags) UnmarshalMsg(bts []byte) (o []byte, err error) {
	{
		var zb0001 uint8
		zb0001, bts, err = msgp.ReadUint8Bytes(bts)
		if err != nil {
			err = msgp.WrapError(err)
			return
		}
		(*z) = xlFlags(zb0001)
	}
	o = bts
	return
}

// Msgsize returns an upper bound estimate of the number of bytes occupied by the serialized message
func (z xlFlags) Msgsize() (s int) {
	s = msgp.Uint8Size
	return
}

// DecodeMsg implements msgp.Decodable
func (z *xlMetaBuf) DecodeMsg(dc *msgp.Reader) (err error) {
	{
		var zb0001 []byte
		zb0001, err = dc.ReadBytes([]byte((*z)))
		if err != nil {
			err = msgp.WrapError(err)
			return
		}
		(*z) = xlMetaBuf(zb0001)
	}
	return
}

// EncodeMsg implements msgp.Encodable
func (z xlMetaBuf) EncodeMsg(en *msgp.Writer) (err error) {
	err = en.WriteBytes([]byte(z))
	if err != nil {
		err = msgp.WrapError(err)
		return
	}
	return
}

// MarshalMsg implements msgp.Marshaler
func (z xlMetaBuf) MarshalMsg(b []byte) (o []byte, err error) {
	o = msgp.Require(b, z.Msgsize())
	o = msgp.AppendBytes(o, []byte(z))
	return
}

// UnmarshalMsg implements msgp.Unmarshaler
func (z *xlMetaBuf) UnmarshalMsg(bts []byte) (o []byte, err error) {
	{
		var zb0001 []byte
		zb0001, bts, err = msgp.ReadBytesBytes(bts, []byte((*z)))
		if err != nil {
			err = msgp.WrapError(err)
			return
		}
		(*z) = xlMetaBuf(zb0001)
	}
	o = bts
	return
}

// Msgsize returns an upper bound estimate of the number of bytes occupied by the serialized message
func (z xlMetaBuf) Msgsize() (s int) {
	s = msgp.BytesPrefixSize + len([]byte(z))
	return
}

// DecodeMsg implements msgp.Decodable
func (z *xlMetaDataDirDecoder) DecodeMsg(dc *msgp.Reader) (err error) {
	var field []byte
	_ = field
	var zb0001 uint32
	zb0001, err = dc.ReadMapHeader()
	if err != nil {
		err = msgp.WrapError(err)
		return
	}
	for zb0001 > 0 {
		zb0001--
		field, err = dc.ReadMapKeyPtr()
		if err != nil {
			err = msgp.WrapError(err)
			return
		}
		switch msgp.UnsafeString(field) {
		case "V2Obj":
			if dc.IsNil() {
				err = dc.ReadNil()
				if err != nil {
					err = msgp.WrapError(err, "ObjectV2")
					return
				}
				z.ObjectV2 = nil
			} else {
				if z.ObjectV2 == nil {
					z.ObjectV2 = new(struct {
						DataDir [16]byte `msg:"DDir"`
					})
				}
				var zb0002 uint32
				zb0002, err = dc.ReadMapHeader()
				if err != nil {
					err = msgp.WrapError(err, "ObjectV2")
					return
				}
				for zb0002 > 0 {
					zb0002--
					field, err = dc.ReadMapKeyPtr()
					if err != nil {
						err = msgp.WrapError(err, "ObjectV2")
						return
					}
					switch msgp.UnsafeString(field) {
					case "DDir":
						err = dc.ReadExactBytes((z.ObjectV2.DataDir)[:])
						if err != nil {
							err = msgp.WrapError(err, "ObjectV2", "DataDir")
							return
						}
					default:
						err = dc.Skip()
						if err != nil {
							err = msgp.WrapError(err, "ObjectV2")
							return
						}
					}
				}
			}
		default:
			err = dc.Skip()
			if err != nil {
				err = msgp.WrapError(err)
				return
			}
		}
	}
	return
}

// EncodeMsg implements msgp.Encodable
func (z *xlMetaDataDirDecoder) EncodeMsg(en *msgp.Writer) (err error) {
	// omitempty: check for empty values
	zb0001Len := uint32(1)
	var zb0001Mask uint8 /* 1 bits */
	if z.ObjectV2 == nil {
		zb0001Len--
		zb0001Mask |= 0x1
	}
	// variable map header, size zb0001Len
	err = en.Append(0x80 | uint8(zb0001Len))
	if err != nil {
		return
	}
	if zb0001Len == 0 {
		return
	}
	if (zb0001Mask & 0x1) == 0 { // if not empty
		// write "V2Obj"
		err = en.Append(0xa5, 0x56, 0x32, 0x4f, 0x62, 0x6a)
		if err != nil {
			return
		}
		if z.ObjectV2 == nil {
			err = en.WriteNil()
			if err != nil {
				return
			}
		} else {
			// map header, size 1
			// write "DDir"
			err = en.Append(0x81, 0xa4, 0x44, 0x44, 0x69, 0x72)
			if err != nil {
				return
			}
			err = en.WriteBytes((z.ObjectV2.DataDir)[:])
			if err != nil {
				err = msgp.WrapError(err, "ObjectV2", "DataDir")
				return
			}
		}
	}
	return
}

// MarshalMsg implements msgp.Marshaler
func (z *xlMetaDataDirDecoder) MarshalMsg(b []byte) (o []byte, err error) {
	o = msgp.Require(b, z.Msgsize())
	// omitempty: check for empty values
	zb0001Len := uint32(1)
	var zb0001Mask uint8 /* 1 bits */
	if z.ObjectV2 == nil {
		zb0001Len--
		zb0001Mask |= 0x1
	}
	// variable map header, size zb0001Len
	o = append(o, 0x80|uint8(zb0001Len))
	if zb0001Len == 0 {
		return
	}
	if (zb0001Mask & 0x1) == 0 { // if not empty
		// string "V2Obj"
		o = append(o, 0xa5, 0x56, 0x32, 0x4f, 0x62, 0x6a)
		if z.ObjectV2 == nil {
			o = msgp.AppendNil(o)
		} else {
			// map header, size 1
			// string "DDir"
			o = append(o, 0x81, 0xa4, 0x44, 0x44, 0x69, 0x72)
			o = msgp.AppendBytes(o, (z.ObjectV2.DataDir)[:])
		}
	}
	return
}

// UnmarshalMsg implements msgp.Unmarshaler
func (z *xlMetaDataDirDecoder) UnmarshalMsg(bts []byte) (o []byte, err error) {
	var field []byte
	_ = field
	var zb0001 uint32
	zb0001, bts, err = msgp.ReadMapHeaderBytes(bts)
	if err != nil {
		err = msgp.WrapError(err)
		return
	}
	for zb0001 > 0 {
		zb0001--
		field, bts, err = msgp.ReadMapKeyZC(bts)
		if err != nil {
			err = msgp.WrapError(err)
			return
		}
		switch msgp.UnsafeString(field) {
		case "V2Obj":
			if msgp.IsNil(bts) {
				bts, err = msgp.ReadNilBytes(bts)
				if err != nil {
					return
				}
				z.ObjectV2 = nil
			} else {
				if z.ObjectV2 == nil {
					z.ObjectV2 = new(struct {
						DataDir [16]byte `msg:"DDir"`
					})
				}
				var zb0002 uint32
				zb0002, bts, err = msgp.ReadMapHeaderBytes(bts)
				if err != nil {
					err = msgp.WrapError(err, "ObjectV2")
					return
				}
				for zb0002 > 0 {
					zb0002--
					field, bts, err = msgp.ReadMapKeyZC(bts)
					if err != nil {
						err = msgp.WrapError(err, "ObjectV2")
						return
					}
					switch msgp.UnsafeString(field) {
					case "DDir":
						bts, err = msgp.ReadExactBytes(bts, (z.ObjectV2.DataDir)[:])
						if err != nil {
							err = msgp.WrapError(err, "ObjectV2", "DataDir")
							return
						}
					default:
						bts, err = msgp.Skip(bts)
						if err != nil {
							err = msgp.WrapError(err, "ObjectV2")
							return
						}
					}
				}
			}
		default:
			bts, err = msgp.Skip(bts)
			if err != nil {
				err = msgp.WrapError(err)
				return
			}
		}
	}
	o = bts
	return
}

// Msgsize returns an upper bound estimate of the number of bytes occupied by the serialized message
func (z *xlMetaDataDirDecoder) Msgsize() (s int) {
	s = 1 + 6
	if z.ObjectV2 == nil {
		s += msgp.NilSize
	} else {
		s += 1 + 5 + msgp.ArrayHeaderSize + (16 * (msgp.ByteSize))
	}
	return
}

// DecodeMsg implements msgp.Decodable
func (z *xlMetaV2DeleteMarker) DecodeMsg(dc *msgp.Reader) (err error) {
	var field []byte
	_ = field
	var zb0001 uint32
	zb0001, err = dc.ReadMapHeader()
	if err != nil {
		err = msgp.WrapError(err)
		return
	}
	for zb0001 > 0 {
		zb0001--
		field, err = dc.ReadMapKeyPtr()
		if err != nil {
			err = msgp.WrapError(err)
			return
		}
		switch msgp.UnsafeString(field) {
		case "ID":
			err = dc.ReadExactBytes((z.VersionID)[:])
			if err != nil {
				err = msgp.WrapError(err, "VersionID")
				return
			}
		case "MTime":
			z.ModTime, err = dc.ReadInt64()
			if err != nil {
				err = msgp.WrapError(err, "ModTime")
				return
			}
		case "MetaSys":
			var zb0002 uint32
			zb0002, err = dc.ReadMapHeader()
			if err != nil {
				err = msgp.WrapError(err, "MetaSys")
				return
			}
			if z.MetaSys == nil {
				z.MetaSys = make(map[string][]byte, zb0002)
			} else if len(z.MetaSys) > 0 {
				for key := range z.MetaSys {
					delete(z.MetaSys, key)
				}
			}
			for zb0002 > 0 {
				zb0002--
				var za0002 string
				var za0003 []byte
				za0002, err = dc.ReadString()
				if err != nil {
					err = msgp.WrapError(err, "MetaSys")
					return
				}
				za0003, err = dc.ReadBytes(za0003)
				if err != nil {
					err = msgp.WrapError(err, "MetaSys", za0002)
					return
				}
				z.MetaSys[za0002] = za0003
			}
		default:
			err = dc.Skip()
			if err != nil {
				err = msgp.WrapError(err)
				return
			}
		}
	}
	return
}

// EncodeMsg implements msgp.Encodable
func (z *xlMetaV2DeleteMarker) EncodeMsg(en *msgp.Writer) (err error) {
	// omitempty: check for empty values
	zb0001Len := uint32(3)
	var zb0001Mask uint8 /* 3 bits */
	if z.MetaSys == nil {
		zb0001Len--
		zb0001Mask |= 0x4
	}
	// variable map header, size zb0001Len
	err = en.Append(0x80 | uint8(zb0001Len))
	if err != nil {
		return
	}
	if zb0001Len == 0 {
		return
	}
	// write "ID"
	err = en.Append(0xa2, 0x49, 0x44)
	if err != nil {
		return
	}
	err = en.WriteBytes((z.VersionID)[:])
	if err != nil {
		err = msgp.WrapError(err, "VersionID")
		return
	}
	// write "MTime"
	err = en.Append(0xa5, 0x4d, 0x54, 0x69, 0x6d, 0x65)
	if err != nil {
		return
	}
	err = en.WriteInt64(z.ModTime)
	if err != nil {
		err = msgp.WrapError(err, "ModTime")
		return
	}
	if (zb0001Mask & 0x4) == 0 { // if not empty
		// write "MetaSys"
		err = en.Append(0xa7, 0x4d, 0x65, 0x74, 0x61, 0x53, 0x79, 0x73)
		if err != nil {
			return
		}
		err = en.WriteMapHeader(uint32(len(z.MetaSys)))
		if err != nil {
			err = msgp.WrapError(err, "MetaSys")
			return
		}
		for za0002, za0003 := range z.MetaSys {
			err = en.WriteString(za0002)
			if err != nil {
				err = msgp.WrapError(err, "MetaSys")
				return
			}
			err = en.WriteBytes(za0003)
			if err != nil {
				err = msgp.WrapError(err, "MetaSys", za0002)
				return
			}
		}
	}
	return
}

// MarshalMsg implements msgp.Marshaler
func (z *xlMetaV2DeleteMarker) MarshalMsg(b []byte) (o []byte, err error) {
	o = msgp.Require(b, z.Msgsize())
	// omitempty: check for empty values
	zb0001Len := uint32(3)
	var zb0001Mask uint8 /* 3 bits */
	if z.MetaSys == nil {
		zb0001Len--
		zb0001Mask |= 0x4
	}
	// variable map header, size zb0001Len
	o = append(o, 0x80|uint8(zb0001Len))
	if zb0001Len == 0 {
		return
	}
	// string "ID"
	o = append(o, 0xa2, 0x49, 0x44)
	o = msgp.AppendBytes(o, (z.VersionID)[:])
	// string "MTime"
	o = append(o, 0xa5, 0x4d, 0x54, 0x69, 0x6d, 0x65)
	o = msgp.AppendInt64(o, z.ModTime)
	if (zb0001Mask & 0x4) == 0 { // if not empty
		// string "MetaSys"
		o = append(o, 0xa7, 0x4d, 0x65, 0x74, 0x61, 0x53, 0x79, 0x73)
		o = msgp.AppendMapHeader(o, uint32(len(z.MetaSys)))
		for za0002, za0003 := range z.MetaSys {
			o = msgp.AppendString(o, za0002)
			o = msgp.AppendBytes(o, za0003)
		}
	}
	return
}

// UnmarshalMsg implements msgp.Unmarshaler
func (z *xlMetaV2DeleteMarker) UnmarshalMsg(bts []byte) (o []byte, err error) {
	var field []byte
	_ = field
	var zb0001 uint32
	zb0001, bts, err = msgp.ReadMapHeaderBytes(bts)
	if err != nil {
		err = msgp.WrapError(err)
		return
	}
	for zb0001 > 0 {
		zb0001--
		field, bts, err = msgp.ReadMapKeyZC(bts)
		if err != nil {
			err = msgp.WrapError(err)
			return
		}
		switch msgp.UnsafeString(field) {
		case "ID":
			bts, err = msgp.ReadExactBytes(bts, (z.VersionID)[:])
			if err != nil {
				err = msgp.WrapError(err, "VersionID")
				return
			}
		case "MTime":
			z.ModTime, bts, err = msgp.ReadInt64Bytes(bts)
			if err != nil {
				err = msgp.WrapError(err, "ModTime")
				return
			}
		case "MetaSys":
			var zb0002 uint32
			zb0002, bts, err = msgp.ReadMapHeaderBytes(bts)
			if err != nil {
				err = msgp.WrapError(err, "MetaSys")
				return
			}
			if z.MetaSys == nil {
				z.MetaSys = make(map[string][]byte, zb0002)
			} else if len(z.MetaSys) > 0 {
				for key := range z.MetaSys {
					delete(z.MetaSys, key)
				}
			}
			for zb0002 > 0 {
				var za0002 string
				var za0003 []byte
				zb0002--
				za0002, bts, err = msgp.ReadStringBytes(bts)
				if err != nil {
					err = msgp.WrapError(err, "MetaSys")
					return
				}
				za0003, bts, err = msgp.ReadBytesBytes(bts, za0003)
				if err != nil {
					err = msgp.WrapError(err, "MetaSys", za0002)
					return
				}
				z.MetaSys[za0002] = za0003
			}
		default:
			bts, err = msgp.Skip(bts)
			if err != nil {
				err = msgp.WrapError(err)
				return
			}
		}
	}
	o = bts
	return
}

// Msgsize returns an upper bound estimate of the number of bytes occupied by the serialized message
func (z *xlMetaV2DeleteMarker) Msgsize() (s int) {
	s = 1 + 3 + msgp.ArrayHeaderSize + (16 * (msgp.ByteSize)) + 6 + msgp.Int64Size + 8 + msgp.MapHeaderSize
	if z.MetaSys != nil {
		for za0002, za0003 := range z.MetaSys {
			_ = za0003
			s += msgp.StringPrefixSize + len(za0002) + msgp.BytesPrefixSize + len(za0003)
		}
	}
	return
}

// DecodeMsg implements msgp.Decodable
func (z *xlMetaV2Object) DecodeMsg(dc *msgp.Reader) (err error) {
	var field []byte
	_ = field
	var zb0001 uint32
	zb0001, err = dc.ReadMapHeader()
	if err != nil {
		err = msgp.WrapError(err)
		return
	}
	for zb0001 > 0 {
		zb0001--
		field, err = dc.ReadMapKeyPtr()
		if err != nil {
			err = msgp.WrapError(err)
			return
		}
		switch msgp.UnsafeString(field) {
		case "ID":
			err = dc.ReadExactBytes((z.VersionID)[:])
			if err != nil {
				err = msgp.WrapError(err, "VersionID")
				return
			}
		case "DDir":
			err = dc.ReadExactBytes((z.DataDir)[:])
			if err != nil {
				err = msgp.WrapError(err, "DataDir")
				return
			}
		case "EcAlgo":
			{
				var zb0002 uint8
				zb0002, err = dc.ReadUint8()
				if err != nil {
					err = msgp.WrapError(err, "ErasureAlgorithm")
					return
				}
				z.ErasureAlgorithm = ErasureAlgo(zb0002)
			}
		case "EcM":
			z.ErasureM, err = dc.ReadInt()
			if err != nil {
				err = msgp.WrapError(err, "ErasureM")
				return
			}
		case "EcN":
			z.ErasureN, err = dc.ReadInt()
			if err != nil {
				err = msgp.WrapError(err, "ErasureN")
				return
			}
		case "EcBSize":
			z.ErasureBlockSize, err = dc.ReadInt64()
			if err != nil {
				err = msgp.WrapError(err, "ErasureBlockSize")
				return
			}
		case "EcIndex":
			z.ErasureIndex, err = dc.ReadInt()
			if err != nil {
				err = msgp.WrapError(err, "ErasureIndex")
				return
			}
		case "EcDist":
			var zb0003 uint32
			zb0003, err = dc.ReadArrayHeader()
			if err != nil {
				err = msgp.WrapError(err, "ErasureDist")
				return
			}
			if cap(z.ErasureDist) >= int(zb0003) {
				z.ErasureDist = (z.ErasureDist)[:zb0003]
			} else {
				z.ErasureDist = make([]uint8, zb0003)
			}
			for za0003 := range z.ErasureDist {
				z.ErasureDist[za0003], err = dc.ReadUint8()
				if err != nil {
					err = msgp.WrapError(err, "ErasureDist", za0003)
					return
				}
			}
		case "CSumAlgo":
			{
				var zb0004 uint8
				zb0004, err = dc.ReadUint8()
				if err != nil {
					err = msgp.WrapError(err, "BitrotChecksumAlgo")
					return
				}
				z.BitrotChecksumAlgo = ChecksumAlgo(zb0004)
			}
		case "PartNums":
			var zb0005 uint32
			zb0005, err = dc.ReadArrayHeader()
			if err != nil {
				err = msgp.WrapError(err, "PartNumbers")
				return
			}
			if cap(z.PartNumbers) >= int(zb0005) {
				z.PartNumbers = (z.PartNumbers)[:zb0005]
			} else {
				z.PartNumbers = make([]int, zb0005)
			}
			for za0004 := range z.PartNumbers {
				z.PartNumbers[za0004], err = dc.ReadInt()
				if err != nil {
					err = msgp.WrapError(err, "PartNumbers", za0004)
					return
				}
			}
		case "PartETags":
			if dc.IsNil() {
				err = dc.ReadNil()
				if err != nil {
					err = msgp.WrapError(err, "PartETags")
					return
				}
				z.PartETags = nil
			} else {
				var zb0006 uint32
				zb0006, err = dc.ReadArrayHeader()
				if err != nil {
					err = msgp.WrapError(err, "PartETags")
					return
				}
				if cap(z.PartETags) >= int(zb0006) {
					z.PartETags = (z.PartETags)[:zb0006]
				} else {
					z.PartETags = make([]string, zb0006)
				}
				for za0005 := range z.PartETags {
					z.PartETags[za0005], err = dc.ReadString()
					if err != nil {
						err = msgp.WrapError(err, "PartETags", za0005)
						return
					}
				}
			}
		case "PartSizes":
			var zb0007 uint32
			zb0007, err = dc.ReadArrayHeader()
			if err != nil {
				err = msgp.WrapError(err, "PartSizes")
				return
			}
			if cap(z.PartSizes) >= int(zb0007) {
				z.PartSizes = (z.PartSizes)[:zb0007]
			} else {
				z.PartSizes = make([]int64, zb0007)
			}
			for za0006 := range z.PartSizes {
				z.PartSizes[za0006], err = dc.ReadInt64()
				if err != nil {
					err = msgp.WrapError(err, "PartSizes", za0006)
					return
				}
			}
		case "PartASizes":
			if dc.IsNil() {
				err = dc.ReadNil()
				if err != nil {
					err = msgp.WrapError(err, "PartActualSizes")
					return
				}
				z.PartActualSizes = nil
			} else {
				var zb0008 uint32
				zb0008, err = dc.ReadArrayHeader()
				if err != nil {
					err = msgp.WrapError(err, "PartActualSizes")
					return
				}
				if cap(z.PartActualSizes) >= int(zb0008) {
					z.PartActualSizes = (z.PartActualSizes)[:zb0008]
				} else {
					z.PartActualSizes = make([]int64, zb0008)
				}
				for za0007 := range z.PartActualSizes {
					z.PartActualSizes[za0007], err = dc.ReadInt64()
					if err != nil {
						err = msgp.WrapError(err, "PartActualSizes", za0007)
						return
					}
				}
			}
		case "Size":
			z.Size, err = dc.ReadInt64()
			if err != nil {
				err = msgp.WrapError(err, "Size")
				return
			}
		case "MTime":
			z.ModTime, err = dc.ReadInt64()
			if err != nil {
				err = msgp.WrapError(err, "ModTime")
				return
			}
		case "MetaSys":
			if dc.IsNil() {
				err = dc.ReadNil()
				if err != nil {
					err = msgp.WrapError(err, "MetaSys")
					return
				}
				z.MetaSys = nil
			} else {
				var zb0009 uint32
				zb0009, err = dc.ReadMapHeader()
				if err != nil {
					err = msgp.WrapError(err, "MetaSys")
					return
				}
				if z.MetaSys == nil {
					z.MetaSys = make(map[string][]byte, zb0009)
				} else if len(z.MetaSys) > 0 {
					for key := range z.MetaSys {
						delete(z.MetaSys, key)
					}
				}
				for zb0009 > 0 {
					zb0009--
					var za0008 string
					var za0009 []byte
					za0008, err = dc.ReadString()
					if err != nil {
						err = msgp.WrapError(err, "MetaSys")
						return
					}
					za0009, err = dc.ReadBytes(za0009)
					if err != nil {
						err = msgp.WrapError(err, "MetaSys", za0008)
						return
					}
					z.MetaSys[za0008] = za0009
				}
			}
		case "MetaUsr":
			if dc.IsNil() {
				err = dc.ReadNil()
				if err != nil {
					err = msgp.WrapError(err, "MetaUser")
					return
				}
				z.MetaUser = nil
			} else {
				var zb0010 uint32
				zb0010, err = dc.ReadMapHeader()
				if err != nil {
					err = msgp.WrapError(err, "MetaUser")
					return
				}
				if z.MetaUser == nil {
					z.MetaUser = make(map[string]string, zb0010)
				} else if len(z.MetaUser) > 0 {
					for key := range z.MetaUser {
						delete(z.MetaUser, key)
					}
				}
				for zb0010 > 0 {
					zb0010--
					var za0010 string
					var za0011 string
					za0010, err = dc.ReadString()
					if err != nil {
						err = msgp.WrapError(err, "MetaUser")
						return
					}
					za0011, err = dc.ReadString()
					if err != nil {
						err = msgp.WrapError(err, "MetaUser", za0010)
						return
					}
					z.MetaUser[za0010] = za0011
				}
			}
		default:
			err = dc.Skip()
			if err != nil {
				err = msgp.WrapError(err)
				return
			}
		}
	}
	return
}

// EncodeMsg implements msgp.Encodable
func (z *xlMetaV2Object) EncodeMsg(en *msgp.Writer) (err error) {
	// map header, size 17
	// write "ID"
	err = en.Append(0xde, 0x0, 0x11, 0xa2, 0x49, 0x44)
	if err != nil {
		return
	}
	err = en.WriteBytes((z.VersionID)[:])
	if err != nil {
		err = msgp.WrapError(err, "VersionID")
		return
	}
	// write "DDir"
	err = en.Append(0xa4, 0x44, 0x44, 0x69, 0x72)
	if err != nil {
		return
	}
	err = en.WriteBytes((z.DataDir)[:])
	if err != nil {
		err = msgp.WrapError(err, "DataDir")
		return
	}
	// write "EcAlgo"
	err = en.Append(0xa6, 0x45, 0x63, 0x41, 0x6c, 0x67, 0x6f)
	if err != nil {
		return
	}
	err = en.WriteUint8(uint8(z.ErasureAlgorithm))
	if err != nil {
		err = msgp.WrapError(err, "ErasureAlgorithm")
		return
	}
	// write "EcM"
	err = en.Append(0xa3, 0x45, 0x63, 0x4d)
	if err != nil {
		return
	}
	err = en.WriteInt(z.ErasureM)
	if err != nil {
		err = msgp.WrapError(err, "ErasureM")
		return
	}
	// write "EcN"
	err = en.Append(0xa3, 0x45, 0x63, 0x4e)
	if err != nil {
		return
	}
	err = en.WriteInt(z.ErasureN)
	if err != nil {
		err = msgp.WrapError(err, "ErasureN")
		return
	}
	// write "EcBSize"
	err = en.Append(0xa7, 0x45, 0x63, 0x42, 0x53, 0x69, 0x7a, 0x65)
	if err != nil {
		return
	}
	err = en.WriteInt64(z.ErasureBlockSize)
	if err != nil {
		err = msgp.WrapError(err, "ErasureBlockSize")
		return
	}
	// write "EcIndex"
	err = en.Append(0xa7, 0x45, 0x63, 0x49, 0x6e, 0x64, 0x65, 0x78)
	if err != nil {
		return
	}
	err = en.WriteInt(z.ErasureIndex)
	if err != nil {
		err = msgp.WrapError(err, "ErasureIndex")
		return
	}
	// write "EcDist"
	err = en.Append(0xa6, 0x45, 0x63, 0x44, 0x69, 0x73, 0x74)
	if err != nil {
		return
	}
	err = en.WriteArrayHeader(uint32(len(z.ErasureDist)))
	if err != nil {
		err = msgp.WrapError(err, "ErasureDist")
		return
	}
	for za0003 := range z.ErasureDist {
		err = en.WriteUint8(z.ErasureDist[za0003])
		if err != nil {
			err = msgp.WrapError(err, "ErasureDist", za0003)
			return
		}
	}
	// write "CSumAlgo"
	err = en.Append(0xa8, 0x43, 0x53, 0x75, 0x6d, 0x41, 0x6c, 0x67, 0x6f)
	if err != nil {
		return
	}
	err = en.WriteUint8(uint8(z.BitrotChecksumAlgo))
	if err != nil {
		err = msgp.WrapError(err, "BitrotChecksumAlgo")
		return
	}
	// write "PartNums"
	err = en.Append(0xa8, 0x50, 0x61, 0x72, 0x74, 0x4e, 0x75, 0x6d, 0x73)
	if err != nil {
		return
	}
	err = en.WriteArrayHeader(uint32(len(z.PartNumbers)))
	if err != nil {
		err = msgp.WrapError(err, "PartNumbers")
		return
	}
	for za0004 := range z.PartNumbers {
		err = en.WriteInt(z.PartNumbers[za0004])
		if err != nil {
			err = msgp.WrapError(err, "PartNumbers", za0004)
			return
		}
	}
	// write "PartETags"
	err = en.Append(0xa9, 0x50, 0x61, 0x72, 0x74, 0x45, 0x54, 0x61, 0x67, 0x73)
	if err != nil {
		return
	}
	if z.PartETags == nil { // allownil: if nil
		err = en.WriteNil()
		if err != nil {
			return
		}
	} else {
		err = en.WriteArrayHeader(uint32(len(z.PartETags)))
		if err != nil {
			err = msgp.WrapError(err, "PartETags")
			return
		}
		for za0005 := range z.PartETags {
			err = en.WriteString(z.PartETags[za0005])
			if err != nil {
				err = msgp.WrapError(err, "PartETags", za0005)
				return
			}
		}
	}
	// write "PartSizes"
	err = en.Append(0xa9, 0x50, 0x61, 0x72, 0x74, 0x53, 0x69, 0x7a, 0x65, 0x73)
	if err != nil {
		return
	}
	err = en.WriteArrayHeader(uint32(len(z.PartSizes)))
	if err != nil {
		err = msgp.WrapError(err, "PartSizes")
		return
	}
	for za0006 := range z.PartSizes {
		err = en.WriteInt64(z.PartSizes[za0006])
		if err != nil {
			err = msgp.WrapError(err, "PartSizes", za0006)
			return
		}
	}
	// write "PartASizes"
	err = en.Append(0xaa, 0x50, 0x61, 0x72, 0x74, 0x41, 0x53, 0x69, 0x7a, 0x65, 0x73)
	if err != nil {
		return
	}
	if z.PartActualSizes == nil { // allownil: if nil
		err = en.WriteNil()
		if err != nil {
			return
		}
	} else {
		err = en.WriteArrayHeader(uint32(len(z.PartActualSizes)))
		if err != nil {
			err = msgp.WrapError(err, "PartActualSizes")
			return
		}
		for za0007 := range z.PartActualSizes {
			err = en.WriteInt64(z.PartActualSizes[za0007])
			if err != nil {
				err = msgp.WrapError(err, "PartActualSizes", za0007)
				return
			}
		}
	}
	// write "Size"
	err = en.Append(0xa4, 0x53, 0x69, 0x7a, 0x65)
	if err != nil {
		return
	}
	err = en.WriteInt64(z.Size)
	if err != nil {
		err = msgp.WrapError(err, "Size")
		return
	}
	// write "MTime"
	err = en.Append(0xa5, 0x4d, 0x54, 0x69, 0x6d, 0x65)
	if err != nil {
		return
	}
	err = en.WriteInt64(z.ModTime)
	if err != nil {
		err = msgp.WrapError(err, "ModTime")
		return
	}
	// write "MetaSys"
	err = en.Append(0xa7, 0x4d, 0x65, 0x74, 0x61, 0x53, 0x79, 0x73)
	if err != nil {
		return
	}
	if z.MetaSys == nil { // allownil: if nil
		err = en.WriteNil()
		if err != nil {
			return
		}
	} else {
		err = en.WriteMapHeader(uint32(len(z.MetaSys)))
		if err != nil {
			err = msgp.WrapError(err, "MetaSys")
			return
		}
		for za0008, za0009 := range z.MetaSys {
			err = en.WriteString(za0008)
			if err != nil {
				err = msgp.WrapError(err, "MetaSys")
				return
			}
			err = en.WriteBytes(za0009)
			if err != nil {
				err = msgp.WrapError(err, "MetaSys", za0008)
				return
			}
		}
	}
	// write "MetaUsr"
	err = en.Append(0xa7, 0x4d, 0x65, 0x74, 0x61, 0x55, 0x73, 0x72)
	if err != nil {
		return
	}
	if z.MetaUser == nil { // allownil: if nil
		err = en.WriteNil()
		if err != nil {
			return
		}
	} else {
		err = en.WriteMapHeader(uint32(len(z.MetaUser)))
		if err != nil {
			err = msgp.WrapError(err, "MetaUser")
			return
		}
		for za0010, za0011 := range z.MetaUser {
			err = en.WriteString(za0010)
			if err != nil {
				err = msgp.WrapError(err, "MetaUser")
				return
			}
			err = en.WriteString(za0011)
			if err != nil {
				err = msgp.WrapError(err, "MetaUser", za0010)
				return
			}
		}
	}
	return
}

// MarshalMsg implements msgp.Marshaler
func (z *xlMetaV2Object) MarshalMsg(b []byte) (o []byte, err error) {
	o = msgp.Require(b, z.Msgsize())
	// map header, size 17
	// string "ID"
	o = append(o, 0xde, 0x0, 0x11, 0xa2, 0x49, 0x44)
	o = msgp.AppendBytes(o, (z.VersionID)[:])
	// string "DDir"
	o = append(o, 0xa4, 0x44, 0x44, 0x69, 0x72)
	o = msgp.AppendBytes(o, (z.DataDir)[:])
	// string "EcAlgo"
	o = append(o, 0xa6, 0x45, 0x63, 0x41, 0x6c, 0x67, 0x6f)
	o = msgp.AppendUint8(o, uint8(z.ErasureAlgorithm))
	// string "EcM"
	o = append(o, 0xa3, 0x45, 0x63, 0x4d)
	o = msgp.AppendInt(o, z.ErasureM)
	// string "EcN"
	o = append(o, 0xa3, 0x45, 0x63, 0x4e)
	o = msgp.AppendInt(o, z.ErasureN)
	// string "EcBSize"
	o = append(o, 0xa7, 0x45, 0x63, 0x42, 0x53, 0x69, 0x7a, 0x65)
	o = msgp.AppendInt64(o, z.ErasureBlockSize)
	// string "EcIndex"
	o = append(o, 0xa7, 0x45, 0x63, 0x49, 0x6e, 0x64, 0x65, 0x78)
	o = msgp.AppendInt(o, z.ErasureIndex)
	// string "EcDist"
	o = append(o, 0xa6, 0x45, 0x63, 0x44, 0x69, 0x73, 0x74)
	o = msgp.AppendArrayHeader(o, uint32(len(z.ErasureDist)))
	for za0003 := range z.ErasureDist {
		o = msgp.AppendUint8(o, z.ErasureDist[za0003])
	}
	// string "CSumAlgo"
	o = append(o, 0xa8, 0x43, 0x53, 0x75, 0x6d, 0x41, 0x6c, 0x67, 0x6f)
	o = msgp.AppendUint8(o, uint8(z.BitrotChecksumAlgo))
	// string "PartNums"
	o = append(o, 0xa8, 0x50, 0x61, 0x72, 0x74, 0x4e, 0x75, 0x6d, 0x73)
	o = msgp.AppendArrayHeader(o, uint32(len(z.PartNumbers)))
	for za0004 := range z.PartNumbers {
		o = msgp.AppendInt(o, z.PartNumbers[za0004])
	}
	// string "PartETags"
	o = append(o, 0xa9, 0x50, 0x61, 0x72, 0x74, 0x45, 0x54, 0x61, 0x67, 0x73)
	if z.PartETags == nil { // allownil: if nil
		o = msgp.AppendNil(o)
	} else {
		o = msgp.AppendArrayHeader(o, uint32(len(z.PartETags)))
		for za0005 := range z.PartETags {
			o = msgp.AppendString(o, z.PartETags[za0005])
		}
	}
	// string "PartSizes"
	o = append(o, 0xa9, 0x50, 0x61, 0x72, 0x74, 0x53, 0x69, 0x7a, 0x65, 0x73)
	o = msgp.AppendArrayHeader(o, uint32(len(z.PartSizes)))
	for za0006 := range z.PartSizes {
		o = msgp.AppendInt64(o, z.PartSizes[za0006])
	}
	// string "PartASizes"
	o = append(o, 0xaa, 0x50, 0x61, 0x72, 0x74, 0x41, 0x53, 0x69, 0x7a, 0x65, 0x73)
	if z.PartActualSizes == nil { // allownil: if nil
		o = msgp.AppendNil(o)
	} else {
		o = msgp.AppendArrayHeader(o, uint32(len(z.PartActualSizes)))
		for za0007 := range z.PartActualSizes {
			o = msgp.AppendInt64(o, z.PartActualSizes[za0007])
		}
	}
	// string "Size"
	o = append(o, 0xa4, 0x53, 0x69, 0x7a, 0x65)
	o = msgp.AppendInt64(o, z.Size)
	// string "MTime"
	o = append(o, 0xa5, 0x4d, 0x54, 0x69, 0x6d, 0x65)
	o = msgp.AppendInt64(o, z.ModTime)
	// string "MetaSys"
	o = append(o, 0xa7, 0x4d, 0x65, 0x74, 0x61, 0x53, 0x79, 0x73)
	if z.MetaSys == nil { // allownil: if nil
		o = msgp.AppendNil(o)
	} else {
		o = msgp.AppendMapHeader(o, uint32(len(z.MetaSys)))
		for za0008, za0009 := range z.MetaSys {
			o = msgp.AppendString(o, za0008)
			o = msgp.AppendBytes(o, za0009)
		}
	}
	// string "MetaUsr"
	o = append(o, 0xa7, 0x4d, 0x65, 0x74, 0x61, 0x55, 0x73, 0x72)
	if z.MetaUser == nil { // allownil: if nil
		o = msgp.AppendNil(o)
	} else {
		o = msgp.AppendMapHeader(o, uint32(len(z.MetaUser)))
		for za0010, za0011 := range z.MetaUser {
			o = msgp.AppendString(o, za0010)
			o = msgp.AppendString(o, za0011)
		}
	}
	return
}

// UnmarshalMsg implements msgp.Unmarshaler
func (z *xlMetaV2Object) UnmarshalMsg(bts []byte) (o []byte, err error) {
	var field []byte
	_ = field
	var zb0001 uint32
	zb0001, bts, err = msgp.ReadMapHeaderBytes(bts)
	if err != nil {
		err = msgp.WrapError(err)
		return
	}
	for zb0001 > 0 {
		zb0001--
		field, bts, err = msgp.ReadMapKeyZC(bts)
		if err != nil {
			err = msgp.WrapError(err)
			return
		}
		switch msgp.UnsafeString(field) {
		case "ID":
			bts, err = msgp.ReadExactBytes(bts, (z.VersionID)[:])
			if err != nil {
				err = msgp.WrapError(err, "VersionID")
				return
			}
		case "DDir":
			bts, err = msgp.ReadExactBytes(bts, (z.DataDir)[:])
			if err != nil {
				err = msgp.WrapError(err, "DataDir")
				return
			}
		case "EcAlgo":
			{
				var zb0002 uint8
				zb0002, bts, err = msgp.ReadUint8Bytes(bts)
				if err != nil {
					err = msgp.WrapError(err, "ErasureAlgorithm")
					return
				}
				z.ErasureAlgorithm = ErasureAlgo(zb0002)
			}
		case "EcM":
			z.ErasureM, bts, err = msgp.ReadIntBytes(bts)
			if err != nil {
				err = msgp.WrapError(err, "ErasureM")
				return
			}
		case "EcN":
			z.ErasureN, bts, err = msgp.ReadIntBytes(bts)
			if err != nil {
				err = msgp.WrapError(err, "ErasureN")
				return
			}
		case "EcBSize":
			z.ErasureBlockSize, bts, err = msgp.ReadInt64Bytes(bts)
			if err != nil {
				err = msgp.WrapError(err, "ErasureBlockSize")
				return
			}
		case "EcIndex":
			z.ErasureIndex, bts, err = msgp.ReadIntBytes(bts)
			if err != nil {
				err = msgp.WrapError(err, "ErasureIndex")
				return
			}
		case "EcDist":
			var zb0003 uint32
			zb0003, bts, err = msgp.ReadArrayHeaderBytes(bts)
			if err != nil {
				err = msgp.WrapError(err, "ErasureDist")
				return
			}
			if cap(z.ErasureDist) >= int(zb0003) {
				z.ErasureDist = (z.ErasureDist)[:zb0003]
			} else {
				z.ErasureDist = make([]uint8, zb0003)
			}
			for za0003 := range z.ErasureDist {
				z.ErasureDist[za0003], bts, err = msgp.ReadUint8Bytes(bts)
				if err != nil {
					err = msgp.WrapError(err, "ErasureDist", za0003)
					return
				}
			}
		case "CSumAlgo":
			{
				var zb0004 uint8
				zb0004, bts, err = msgp.ReadUint8Bytes(bts)
				if err != nil {
					err = msgp.WrapError(err, "BitrotChecksumAlgo")
					return
				}
				z.BitrotChecksumAlgo = ChecksumAlgo(zb0004)
			}
		case "PartNums":
			var zb0005 uint32
			zb0005, bts, err = msgp.ReadArrayHeaderBytes(bts)
			if err != nil {
				err = msgp.WrapError(err, "PartNumbers")
				return
			}
			if cap(z.PartNumbers) >= int(zb0005) {
				z.PartNumbers = (z.PartNumbers)[:zb0005]
			} else {
				z.PartNumbers = make([]int, zb0005)
			}
			for za0004 := range z.PartNumbers {
				z.PartNumbers[za0004], bts, err = msgp.ReadIntBytes(bts)
				if err != nil {
					err = msgp.WrapError(err, "PartNumbers", za0004)
					return
				}
			}
		case "PartETags":
			if msgp.IsNil(bts) {
				bts = bts[1:]
				z.PartETags = nil
			} else {
				var zb0006 uint32
				zb0006, bts, err = msgp.ReadArrayHeaderBytes(bts)
				if err != nil {
					err = msgp.WrapError(err, "PartETags")
					return
				}
				if cap(z.PartETags) >= int(zb0006) {
					z.PartETags = (z.PartETags)[:zb0006]
				} else {
					z.PartETags = make([]string, zb0006)
				}
				for za0005 := range z.PartETags {
					z.PartETags[za0005], bts, err = msgp.ReadStringBytes(bts)
					if err != nil {
						err = msgp.WrapError(err, "PartETags", za0005)
						return
					}
				}
			}
		case "PartSizes":
			var zb0007 uint32
			zb0007, bts, err = msgp.ReadArrayHeaderBytes(bts)
			if err != nil {
				err = msgp.WrapError(err, "PartSizes")
				return
			}
			if cap(z.PartSizes) >= int(zb0007) {
				z.PartSizes = (z.PartSizes)[:zb0007]
			} else {
				z.PartSizes = make([]int64, zb0007)
			}
			for za0006 := range z.PartSizes {
				z.PartSizes[za0006], bts, err = msgp.ReadInt64Bytes(bts)
				if err != nil {
					err = msgp.WrapError(err, "PartSizes", za0006)
					return
				}
			}
		case "PartASizes":
			if msgp.IsNil(bts) {
				bts = bts[1:]
				z.PartActualSizes = nil
			} else {
				var zb0008 uint32
				zb0008, bts, err = msgp.ReadArrayHeaderBytes(bts)
				if err != nil {
					err = msgp.WrapError(err, "PartActualSizes")
					return
				}
				if cap(z.PartActualSizes) >= int(zb0008) {
					z.PartActualSizes = (z.PartActualSizes)[:zb0008]
				} else {
					z.PartActualSizes = make([]int64, zb0008)
				}
				for za0007 := range z.PartActualSizes {
					z.PartActualSizes[za0007], bts, err = msgp.ReadInt64Bytes(bts)
					if err != nil {
						err = msgp.WrapError(err, "PartActualSizes", za0007)
						return
					}
				}
			}
		case "Size":
			z.Size, bts, err = msgp.ReadInt64Bytes(bts)
			if err != nil {
				err = msgp.WrapError(err, "Size")
				return
			}
		case "MTime":
			z.ModTime, bts, err = msgp.ReadInt64Bytes(bts)
			if err != nil {
				err = msgp.WrapError(err, "ModTime")
				return
			}
		case "MetaSys":
			if msgp.IsNil(bts) {
				bts = bts[1:]
				z.MetaSys = nil
			} else {
				var zb0009 uint32
				zb0009, bts, err = msgp.ReadMapHeaderBytes(bts)
				if err != nil {
					err = msgp.WrapError(err, "MetaSys")
					return
				}
				if z.MetaSys == nil {
					z.MetaSys = make(map[string][]byte, zb0009)
				} else if len(z.MetaSys) > 0 {
					for key := range z.MetaSys {
						delete(z.MetaSys, key)
					}
				}
				for zb0009 > 0 {
					var za0008 string
					var za0009 []byte
					zb0009--
					za0008, bts, err = msgp.ReadStringBytes(bts)
					if err != nil {
						err = msgp.WrapError(err, "MetaSys")
						return
					}
					za0009, bts, err = msgp.ReadBytesBytes(bts, za0009)
					if err != nil {
						err = msgp.WrapError(err, "MetaSys", za0008)
						return
					}
					z.MetaSys[za0008] = za0009
				}
			}
		case "MetaUsr":
			if msgp.IsNil(bts) {
				bts = bts[1:]
				z.MetaUser = nil
			} else {
				var zb0010 uint32
				zb0010, bts, err = msgp.ReadMapHeaderBytes(bts)
				if err != nil {
					err = msgp.WrapError(err, "MetaUser")
					return
				}
				if z.MetaUser == nil {
					z.MetaUser = make(map[string]string, zb0010)
				} else if len(z.MetaUser) > 0 {
					for key := range z.MetaUser {
						delete(z.MetaUser, key)
					}
				}
				for zb0010 > 0 {
					var za0010 string
					var za0011 string
					zb0010--
					za0010, bts, err = msgp.ReadStringBytes(bts)
					if err != nil {
						err = msgp.WrapError(err, "MetaUser")
						return
					}
					za0011, bts, err = msgp.ReadStringBytes(bts)
					if err != nil {
						err = msgp.WrapError(err, "MetaUser", za0010)
						return
					}
					z.MetaUser[za0010] = za0011
				}
			}
		default:
			bts, err = msgp.Skip(bts)
			if err != nil {
				err = msgp.WrapError(err)
				return
			}
		}
	}
	o = bts
	return
}

// Msgsize returns an upper bound estimate of the number of bytes occupied by the serialized message
func (z *xlMetaV2Object) Msgsize() (s int) {
	s = 3 + 3 + msgp.ArrayHeaderSize + (16 * (msgp.ByteSize)) + 5 + msgp.ArrayHeaderSize + (16 * (msgp.ByteSize)) + 7 + msgp.Uint8Size + 4 + msgp.IntSize + 4 + msgp.IntSize + 8 + msgp.Int64Size + 8 + msgp.IntSize + 7 + msgp.ArrayHeaderSize + (len(z.ErasureDist) * (msgp.Uint8Size)) + 9 + msgp.Uint8Size + 9 + msgp.ArrayHeaderSize + (len(z.PartNumbers) * (msgp.IntSize)) + 10 + msgp.ArrayHeaderSize
	for za0005 := range z.PartETags {
		s += msgp.StringPrefixSize + len(z.PartETags[za0005])
	}
	s += 10 + msgp.ArrayHeaderSize + (len(z.PartSizes) * (msgp.Int64Size)) + 11 + msgp.ArrayHeaderSize + (len(z.PartActualSizes) * (msgp.Int64Size)) + 5 + msgp.Int64Size + 6 + msgp.Int64Size + 8 + msgp.MapHeaderSize
	if z.MetaSys != nil {
		for za0008, za0009 := range z.MetaSys {
			_ = za0009
			s += msgp.StringPrefixSize + len(za0008) + msgp.BytesPrefixSize + len(za0009)
		}
	}
	s += 8 + msgp.MapHeaderSize
	if z.MetaUser != nil {
		for za0010, za0011 := range z.MetaUser {
			_ = za0011
			s += msgp.StringPrefixSize + len(za0010) + msgp.StringPrefixSize + len(za0011)
		}
	}
	return
}

// DecodeMsg implements msgp.Decodable
func (z *xlMetaV2Version) DecodeMsg(dc *msgp.Reader) (err error) {
	var field []byte
	_ = field
	var zb0001 uint32
	zb0001, err = dc.ReadMapHeader()
	if err != nil {
		err = msgp.WrapError(err)
		return
	}
	for zb0001 > 0 {
		zb0001--
		field, err = dc.ReadMapKeyPtr()
		if err != nil {
			err = msgp.WrapError(err)
			return
		}
		switch msgp.UnsafeString(field) {
		case "Type":
			{
				var zb0002 uint8
				zb0002, err = dc.ReadUint8()
				if err != nil {
					err = msgp.WrapError(err, "Type")
					return
				}
				z.Type = VersionType(zb0002)
			}
		case "V1Obj":
			if dc.IsNil() {
				err = dc.ReadNil()
				if err != nil {
					err = msgp.WrapError(err, "ObjectV1")
					return
				}
				z.ObjectV1 = nil
			} else {
				if z.ObjectV1 == nil {
					z.ObjectV1 = new(xlMetaV1Object)
				}
				err = z.ObjectV1.DecodeMsg(dc)
				if err != nil {
					err = msgp.WrapError(err, "ObjectV1")
					return
				}
			}
		case "V2Obj":
			if dc.IsNil() {
				err = dc.ReadNil()
				if err != nil {
					err = msgp.WrapError(err, "ObjectV2")
					return
				}
				z.ObjectV2 = nil
			} else {
				if z.ObjectV2 == nil {
					z.ObjectV2 = new(xlMetaV2Object)
				}
				err = z.ObjectV2.DecodeMsg(dc)
				if err != nil {
					err = msgp.WrapError(err, "ObjectV2")
					return
				}
			}
		case "DelObj":
			if dc.IsNil() {
				err = dc.ReadNil()
				if err != nil {
					err = msgp.WrapError(err, "DeleteMarker")
					return
				}
				z.DeleteMarker = nil
			} else {
				if z.DeleteMarker == nil {
					z.DeleteMarker = new(xlMetaV2DeleteMarker)
				}
				err = z.DeleteMarker.DecodeMsg(dc)
				if err != nil {
					err = msgp.WrapError(err, "DeleteMarker")
					return
				}
			}
		default:
			err = dc.Skip()
			if err != nil {
				err = msgp.WrapError(err)
				return
			}
		}
	}
	return
}

// EncodeMsg implements msgp.Encodable
func (z *xlMetaV2Version) EncodeMsg(en *msgp.Writer) (err error) {
	// omitempty: check for empty values
	zb0001Len := uint32(4)
	var zb0001Mask uint8 /* 4 bits */
	if z.ObjectV1 == nil {
		zb0001Len--
		zb0001Mask |= 0x2
	}
	if z.ObjectV2 == nil {
		zb0001Len--
		zb0001Mask |= 0x4
	}
	if z.DeleteMarker == nil {
		zb0001Len--
		zb0001Mask |= 0x8
	}
	// variable map header, size zb0001Len
	err = en.Append(0x80 | uint8(zb0001Len))
	if err != nil {
		return
	}
	if zb0001Len == 0 {
		return
	}
	// write "Type"
	err = en.Append(0xa4, 0x54, 0x79, 0x70, 0x65)
	if err != nil {
		return
	}
	err = en.WriteUint8(uint8(z.Type))
	if err != nil {
		err = msgp.WrapError(err, "Type")
		return
	}
	if (zb0001Mask & 0x2) == 0 { // if not empty
		// write "V1Obj"
		err = en.Append(0xa5, 0x56, 0x31, 0x4f, 0x62, 0x6a)
		if err != nil {
			return
		}
		if z.ObjectV1 == nil {
			err = en.WriteNil()
			if err != nil {
				return
			}
		} else {
			err = z.ObjectV1.EncodeMsg(en)
			if err != nil {
				err = msgp.WrapError(err, "ObjectV1")
				return
			}
		}
	}
	if (zb0001Mask & 0x4) == 0 { // if not empty
		// write "V2Obj"
		err = en.Append(0xa5, 0x56, 0x32, 0x4f, 0x62, 0x6a)
		if err != nil {
			return
		}
		if z.ObjectV2 == nil {
			err = en.WriteNil()
			if err != nil {
				return
			}
		} else {
			err = z.ObjectV2.EncodeMsg(en)
			if err != nil {
				err = msgp.WrapError(err, "ObjectV2")
				return
			}
		}
	}
	if (zb0001Mask & 0x8) == 0 { // if not empty
		// write "DelObj"
		err = en.Append(0xa6, 0x44, 0x65, 0x6c, 0x4f, 0x62, 0x6a)
		if err != nil {
			return
		}
		if z.DeleteMarker == nil {
			err = en.WriteNil()
			if err != nil {
				return
			}
		} else {
			err = z.DeleteMarker.EncodeMsg(en)
			if err != nil {
				err = msgp.WrapError(err, "DeleteMarker")
				return
			}
		}
	}
	return
}

// MarshalMsg implements msgp.Marshaler
func (z *xlMetaV2Version) MarshalMsg(b []byte) (o []byte, err error) {
	o = msgp.Require(b, z.Msgsize())
	// omitempty: check for empty values
	zb0001Len := uint32(4)
	var zb0001Mask uint8 /* 4 bits */
	if z.ObjectV1 == nil {
		zb0001Len--
		zb0001Mask |= 0x2
	}
	if z.ObjectV2 == nil {
		zb0001Len--
		zb0001Mask |= 0x4
	}
	if z.DeleteMarker == nil {
		zb0001Len--
		zb0001Mask |= 0x8
	}
	// variable map header, size zb0001Len
	o = append(o, 0x80|uint8(zb0001Len))
	if zb0001Len == 0 {
		return
	}
	// string "Type"
	o = append(o, 0xa4, 0x54, 0x79, 0x70, 0x65)
	o = msgp.AppendUint8(o, uint8(z.Type))
	if (zb0001Mask & 0x2) == 0 { // if not empty
		// string "V1Obj"
		o = append(o, 0xa5, 0x56, 0x31, 0x4f, 0x62, 0x6a)
		if z.ObjectV1 == nil {
			o = msgp.AppendNil(o)
		} else {
			o, err = z.ObjectV1.MarshalMsg(o)
			if err != nil {
				err = msgp.WrapError(err, "ObjectV1")
				return
			}
		}
	}
	if (zb0001Mask & 0x4) == 0 { // if not empty
		// string "V2Obj"
		o = append(o, 0xa5, 0x56, 0x32, 0x4f, 0x62, 0x6a)
		if z.ObjectV2 == nil {
			o = msgp.AppendNil(o)
		} else {
			o, err = z.ObjectV2.MarshalMsg(o)
			if err != nil {
				err = msgp.WrapError(err, "ObjectV2")
				return
			}
		}
	}
	if (zb0001Mask & 0x8) == 0 { // if not empty
		// string "DelObj"
		o = append(o, 0xa6, 0x44, 0x65, 0x6c, 0x4f, 0x62, 0x6a)
		if z.DeleteMarker == nil {
			o = msgp.AppendNil(o)
		} else {
			o, err = z.DeleteMarker.MarshalMsg(o)
			if err != nil {
				err = msgp.WrapError(err, "DeleteMarker")
				return
			}
		}
	}
	return
}

// UnmarshalMsg implements msgp.Unmarshaler
func (z *xlMetaV2Version) UnmarshalMsg(bts []byte) (o []byte, err error) {
	var field []byte
	_ = field
	var zb0001 uint32
	zb0001, bts, err = msgp.ReadMapHeaderBytes(bts)
	if err != nil {
		err = msgp.WrapError(err)
		return
	}
	for zb0001 > 0 {
		zb0001--
		field, bts, err = msgp.ReadMapKeyZC(bts)
		if err != nil {
			err = msgp.WrapError(err)
			return
		}
		switch msgp.UnsafeString(field) {
		case "Type":
			{
				var zb0002 uint8
				zb0002, bts, err = msgp.ReadUint8Bytes(bts)
				if err != nil {
					err = msgp.WrapError(err, "Type")
					return
				}
				z.Type = VersionType(zb0002)
			}
		case "V1Obj":
			if msgp.IsNil(bts) {
				bts, err = msgp.ReadNilBytes(bts)
				if err != nil {
					return
				}
				z.ObjectV1 = nil
			} else {
				if z.ObjectV1 == nil {
					z.ObjectV1 = new(xlMetaV1Object)
				}
				bts, err = z.ObjectV1.UnmarshalMsg(bts)
				if err != nil {
					err = msgp.WrapError(err, "ObjectV1")
					return
				}
			}
		case "V2Obj":
			if msgp.IsNil(bts) {
				bts, err = msgp.ReadNilBytes(bts)
				if err != nil {
					return
				}
				z.ObjectV2 = nil
			} else {
				if z.ObjectV2 == nil {
					z.ObjectV2 = new(xlMetaV2Object)
				}
				bts, err = z.ObjectV2.UnmarshalMsg(bts)
				if err != nil {
					err = msgp.WrapError(err, "ObjectV2")
					return
				}
			}
		case "DelObj":
			if msgp.IsNil(bts) {
				bts, err = msgp.ReadNilBytes(bts)
				if err != nil {
					return
				}
				z.DeleteMarker = nil
			} else {
				if z.DeleteMarker == nil {
					z.DeleteMarker = new(xlMetaV2DeleteMarker)
				}
				bts, err = z.DeleteMarker.UnmarshalMsg(bts)
				if err != nil {
					err = msgp.WrapError(err, "DeleteMarker")
					return
				}
			}
		default:
			bts, err = msgp.Skip(bts)
			if err != nil {
				err = msgp.WrapError(err)
				return
			}
		}
	}
	o = bts
	return
}

// Msgsize returns an upper bound estimate of the number of bytes occupied by the serialized message
func (z *xlMetaV2Version) Msgsize() (s int) {
	s = 1 + 5 + msgp.Uint8Size + 6
	if z.ObjectV1 == nil {
		s += msgp.NilSize
	} else {
		s += z.ObjectV1.Msgsize()
	}
	s += 6
	if z.ObjectV2 == nil {
		s += msgp.NilSize
	} else {
		s += z.ObjectV2.Msgsize()
	}
	s += 7
	if z.DeleteMarker == nil {
		s += msgp.NilSize
	} else {
		s += z.DeleteMarker.Msgsize()
	}
	return
}

// DecodeMsg implements msgp.Decodable
func (z *xlMetaV2VersionHeader) DecodeMsg(dc *msgp.Reader) (err error) {
	var zb0001 uint32
	zb0001, err = dc.ReadArrayHeader()
	if err != nil {
		err = msgp.WrapError(err)
		return
	}
	if zb0001 != 5 {
		err = msgp.ArrayError{Wanted: 5, Got: zb0001}
		return
	}
	err = dc.ReadExactBytes((z.VersionID)[:])
	if err != nil {
		err = msgp.WrapError(err, "VersionID")
		return
	}
	z.ModTime, err = dc.ReadInt64()
	if err != nil {
		err = msgp.WrapError(err, "ModTime")
		return
	}
	err = dc.ReadExactBytes((z.Signature)[:])
	if err != nil {
		err = msgp.WrapError(err, "Signature")
		return
	}
	{
		var zb0002 uint8
		zb0002, err = dc.ReadUint8()
		if err != nil {
			err = msgp.WrapError(err, "Type")
			return
		}
		z.Type = VersionType(zb0002)
	}
	{
		var zb0003 uint8
		zb0003, err = dc.ReadUint8()
		if err != nil {
			err = msgp.WrapError(err, "Flags")
			return
		}
		z.Flags = xlFlags(zb0003)
	}
	return
}

// EncodeMsg implements msgp.Encodable
func (z *xlMetaV2VersionHeader) EncodeMsg(en *msgp.Writer) (err error) {
	// array header, size 5
	err = en.Append(0x95)
	if err != nil {
		return
	}
	err = en.WriteBytes((z.VersionID)[:])
	if err != nil {
		err = msgp.WrapError(err, "VersionID")
		return
	}
	err = en.WriteInt64(z.ModTime)
	if err != nil {
		err = msgp.WrapError(err, "ModTime")
		return
	}
	err = en.WriteBytes((z.Signature)[:])
	if err != nil {
		err = msgp.WrapError(err, "Signature")
		return
	}
	err = en.WriteUint8(uint8(z.Type))
	if err != nil {
		err = msgp.WrapError(err, "Type")
		return
	}
	err = en.WriteUint8(uint8(z.Flags))
	if err != nil {
		err = msgp.WrapError(err, "Flags")
		return
	}
	return
}

// MarshalMsg implements msgp.Marshaler
func (z *xlMetaV2VersionHeader) MarshalMsg(b []byte) (o []byte, err error) {
	o = msgp.Require(b, z.Msgsize())
	// array header, size 5
	o = append(o, 0x95)
	o = msgp.AppendBytes(o, (z.VersionID)[:])
	o = msgp.AppendInt64(o, z.ModTime)
	o = msgp.AppendBytes(o, (z.Signature)[:])
	o = msgp.AppendUint8(o, uint8(z.Type))
	o = msgp.AppendUint8(o, uint8(z.Flags))
	return
}

// UnmarshalMsg implements msgp.Unmarshaler
func (z *xlMetaV2VersionHeader) UnmarshalMsg(bts []byte) (o []byte, err error) {
	var zb0001 uint32
	zb0001, bts, err = msgp.ReadArrayHeaderBytes(bts)
	if err != nil {
		err = msgp.WrapError(err)
		return
	}
	if zb0001 != 5 {
		err = msgp.ArrayError{Wanted: 5, Got: zb0001}
		return
	}
	bts, err = msgp.ReadExactBytes(bts, (z.VersionID)[:])
	if err != nil {
		err = msgp.WrapError(err, "VersionID")
		return
	}
	z.ModTime, bts, err = msgp.ReadInt64Bytes(bts)
	if err != nil {
		err = msgp.WrapError(err, "ModTime")
		return
	}
	bts, err = msgp.ReadExactBytes(bts, (z.Signature)[:])
	if err != nil {
		err = msgp.WrapError(err, "Signature")
		return
	}
	{
		var zb0002 uint8
		zb0002, bts, err = msgp.ReadUint8Bytes(bts)
		if err != nil {
			err = msgp.WrapError(err, "Type")
			return
		}
		z.Type = VersionType(zb0002)
	}
	{
		var zb0003 uint8
		zb0003, bts, err = msgp.ReadUint8Bytes(bts)
		if err != nil {
			err = msgp.WrapError(err, "Flags")
			return
		}
		z.Flags = xlFlags(zb0003)
	}
	o = bts
	return
}

// Msgsize returns an upper bound estimate of the number of bytes occupied by the serialized message
func (z *xlMetaV2VersionHeader) Msgsize() (s int) {
	s = 1 + msgp.ArrayHeaderSize + (16 * (msgp.ByteSize)) + msgp.Int64Size + msgp.ArrayHeaderSize + (4 * (msgp.ByteSize)) + msgp.Uint8Size + msgp.Uint8Size
	return
}
