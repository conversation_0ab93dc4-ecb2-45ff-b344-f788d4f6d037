package cmd

// Code generated by github.com/tinylib/msgp DO NOT EDIT.

import (
	"github.com/tinylib/msgp/msgp"
)

// DecodeMsg implements msgp.Decodable
func (z *metacache) DecodeMsg(dc *msgp.Reader) (err error) {
	var field []byte
	_ = field
	var zb0001 uint32
	zb0001, err = dc.ReadMapHeader()
	if err != nil {
		err = msgp.WrapError(err)
		return
	}
	for zb0001 > 0 {
		zb0001--
		field, err = dc.ReadMapKeyPtr()
		if err != nil {
			err = msgp.WrapError(err)
			return
		}
		switch msgp.UnsafeString(field) {
		case "end":
			z.ended, err = dc.ReadTime()
			if err != nil {
				err = msgp.WrapError(err, "ended")
				return
			}
		case "st":
			z.started, err = dc.ReadTime()
			if err != nil {
				err = msgp.WrapError(err, "started")
				return
			}
		case "lh":
			z.lastHandout, err = dc.ReadTime()
			if err != nil {
				err = msgp.WrapError(err, "lastHandout")
				return
			}
		case "u":
			z.lastUpdate, err = dc.ReadTime()
			if err != nil {
				err = msgp.WrapError(err, "lastUpdate")
				return
			}
		case "b":
			z.bucket, err = dc.ReadString()
			if err != nil {
				err = msgp.WrapError(err, "bucket")
				return
			}
		case "flt":
			z.filter, err = dc.ReadString()
			if err != nil {
				err = msgp.WrapError(err, "filter")
				return
			}
		case "id":
			z.id, err = dc.ReadString()
			if err != nil {
				err = msgp.WrapError(err, "id")
				return
			}
		case "err":
			z.error, err = dc.ReadString()
			if err != nil {
				err = msgp.WrapError(err, "error")
				return
			}
		case "root":
			z.root, err = dc.ReadString()
			if err != nil {
				err = msgp.WrapError(err, "root")
				return
			}
		case "fnf":
			z.fileNotFound, err = dc.ReadBool()
			if err != nil {
				err = msgp.WrapError(err, "fileNotFound")
				return
			}
		case "stat":
			{
				var zb0002 uint8
				zb0002, err = dc.ReadUint8()
				if err != nil {
					err = msgp.WrapError(err, "status")
					return
				}
				z.status = scanStatus(zb0002)
			}
		case "rec":
			z.recursive, err = dc.ReadBool()
			if err != nil {
				err = msgp.WrapError(err, "recursive")
				return
			}
		case "v":
			z.dataVersion, err = dc.ReadUint8()
			if err != nil {
				err = msgp.WrapError(err, "dataVersion")
				return
			}
		default:
			err = dc.Skip()
			if err != nil {
				err = msgp.WrapError(err)
				return
			}
		}
	}
	return
}

// EncodeMsg implements msgp.Encodable
func (z *metacache) EncodeMsg(en *msgp.Writer) (err error) {
	// map header, size 13
	// write "end"
	err = en.Append(0x8d, 0xa3, 0x65, 0x6e, 0x64)
	if err != nil {
		return
	}
	err = en.WriteTime(z.ended)
	if err != nil {
		err = msgp.WrapError(err, "ended")
		return
	}
	// write "st"
	err = en.Append(0xa2, 0x73, 0x74)
	if err != nil {
		return
	}
	err = en.WriteTime(z.started)
	if err != nil {
		err = msgp.WrapError(err, "started")
		return
	}
	// write "lh"
	err = en.Append(0xa2, 0x6c, 0x68)
	if err != nil {
		return
	}
	err = en.WriteTime(z.lastHandout)
	if err != nil {
		err = msgp.WrapError(err, "lastHandout")
		return
	}
	// write "u"
	err = en.Append(0xa1, 0x75)
	if err != nil {
		return
	}
	err = en.WriteTime(z.lastUpdate)
	if err != nil {
		err = msgp.WrapError(err, "lastUpdate")
		return
	}
	// write "b"
	err = en.Append(0xa1, 0x62)
	if err != nil {
		return
	}
	err = en.WriteString(z.bucket)
	if err != nil {
		err = msgp.WrapError(err, "bucket")
		return
	}
	// write "flt"
	err = en.Append(0xa3, 0x66, 0x6c, 0x74)
	if err != nil {
		return
	}
	err = en.WriteString(z.filter)
	if err != nil {
		err = msgp.WrapError(err, "filter")
		return
	}
	// write "id"
	err = en.Append(0xa2, 0x69, 0x64)
	if err != nil {
		return
	}
	err = en.WriteString(z.id)
	if err != nil {
		err = msgp.WrapError(err, "id")
		return
	}
	// write "err"
	err = en.Append(0xa3, 0x65, 0x72, 0x72)
	if err != nil {
		return
	}
	err = en.WriteString(z.error)
	if err != nil {
		err = msgp.WrapError(err, "error")
		return
	}
	// write "root"
	err = en.Append(0xa4, 0x72, 0x6f, 0x6f, 0x74)
	if err != nil {
		return
	}
	err = en.WriteString(z.root)
	if err != nil {
		err = msgp.WrapError(err, "root")
		return
	}
	// write "fnf"
	err = en.Append(0xa3, 0x66, 0x6e, 0x66)
	if err != nil {
		return
	}
	err = en.WriteBool(z.fileNotFound)
	if err != nil {
		err = msgp.WrapError(err, "fileNotFound")
		return
	}
	// write "stat"
	err = en.Append(0xa4, 0x73, 0x74, 0x61, 0x74)
	if err != nil {
		return
	}
	err = en.WriteUint8(uint8(z.status))
	if err != nil {
		err = msgp.WrapError(err, "status")
		return
	}
	// write "rec"
	err = en.Append(0xa3, 0x72, 0x65, 0x63)
	if err != nil {
		return
	}
	err = en.WriteBool(z.recursive)
	if err != nil {
		err = msgp.WrapError(err, "recursive")
		return
	}
	// write "v"
	err = en.Append(0xa1, 0x76)
	if err != nil {
		return
	}
	err = en.WriteUint8(z.dataVersion)
	if err != nil {
		err = msgp.WrapError(err, "dataVersion")
		return
	}
	return
}

// MarshalMsg implements msgp.Marshaler
func (z *metacache) MarshalMsg(b []byte) (o []byte, err error) {
	o = msgp.Require(b, z.Msgsize())
	// map header, size 13
	// string "end"
	o = append(o, 0x8d, 0xa3, 0x65, 0x6e, 0x64)
	o = msgp.AppendTime(o, z.ended)
	// string "st"
	o = append(o, 0xa2, 0x73, 0x74)
	o = msgp.AppendTime(o, z.started)
	// string "lh"
	o = append(o, 0xa2, 0x6c, 0x68)
	o = msgp.AppendTime(o, z.lastHandout)
	// string "u"
	o = append(o, 0xa1, 0x75)
	o = msgp.AppendTime(o, z.lastUpdate)
	// string "b"
	o = append(o, 0xa1, 0x62)
	o = msgp.AppendString(o, z.bucket)
	// string "flt"
	o = append(o, 0xa3, 0x66, 0x6c, 0x74)
	o = msgp.AppendString(o, z.filter)
	// string "id"
	o = append(o, 0xa2, 0x69, 0x64)
	o = msgp.AppendString(o, z.id)
	// string "err"
	o = append(o, 0xa3, 0x65, 0x72, 0x72)
	o = msgp.AppendString(o, z.error)
	// string "root"
	o = append(o, 0xa4, 0x72, 0x6f, 0x6f, 0x74)
	o = msgp.AppendString(o, z.root)
	// string "fnf"
	o = append(o, 0xa3, 0x66, 0x6e, 0x66)
	o = msgp.AppendBool(o, z.fileNotFound)
	// string "stat"
	o = append(o, 0xa4, 0x73, 0x74, 0x61, 0x74)
	o = msgp.AppendUint8(o, uint8(z.status))
	// string "rec"
	o = append(o, 0xa3, 0x72, 0x65, 0x63)
	o = msgp.AppendBool(o, z.recursive)
	// string "v"
	o = append(o, 0xa1, 0x76)
	o = msgp.AppendUint8(o, z.dataVersion)
	return
}

// UnmarshalMsg implements msgp.Unmarshaler
func (z *metacache) UnmarshalMsg(bts []byte) (o []byte, err error) {
	var field []byte
	_ = field
	var zb0001 uint32
	zb0001, bts, err = msgp.ReadMapHeaderBytes(bts)
	if err != nil {
		err = msgp.WrapError(err)
		return
	}
	for zb0001 > 0 {
		zb0001--
		field, bts, err = msgp.ReadMapKeyZC(bts)
		if err != nil {
			err = msgp.WrapError(err)
			return
		}
		switch msgp.UnsafeString(field) {
		case "end":
			z.ended, bts, err = msgp.ReadTimeBytes(bts)
			if err != nil {
				err = msgp.WrapError(err, "ended")
				return
			}
		case "st":
			z.started, bts, err = msgp.ReadTimeBytes(bts)
			if err != nil {
				err = msgp.WrapError(err, "started")
				return
			}
		case "lh":
			z.lastHandout, bts, err = msgp.ReadTimeBytes(bts)
			if err != nil {
				err = msgp.WrapError(err, "lastHandout")
				return
			}
		case "u":
			z.lastUpdate, bts, err = msgp.ReadTimeBytes(bts)
			if err != nil {
				err = msgp.WrapError(err, "lastUpdate")
				return
			}
		case "b":
			z.bucket, bts, err = msgp.ReadStringBytes(bts)
			if err != nil {
				err = msgp.WrapError(err, "bucket")
				return
			}
		case "flt":
			z.filter, bts, err = msgp.ReadStringBytes(bts)
			if err != nil {
				err = msgp.WrapError(err, "filter")
				return
			}
		case "id":
			z.id, bts, err = msgp.ReadStringBytes(bts)
			if err != nil {
				err = msgp.WrapError(err, "id")
				return
			}
		case "err":
			z.error, bts, err = msgp.ReadStringBytes(bts)
			if err != nil {
				err = msgp.WrapError(err, "error")
				return
			}
		case "root":
			z.root, bts, err = msgp.ReadStringBytes(bts)
			if err != nil {
				err = msgp.WrapError(err, "root")
				return
			}
		case "fnf":
			z.fileNotFound, bts, err = msgp.ReadBoolBytes(bts)
			if err != nil {
				err = msgp.WrapError(err, "fileNotFound")
				return
			}
		case "stat":
			{
				var zb0002 uint8
				zb0002, bts, err = msgp.ReadUint8Bytes(bts)
				if err != nil {
					err = msgp.WrapError(err, "status")
					return
				}
				z.status = scanStatus(zb0002)
			}
		case "rec":
			z.recursive, bts, err = msgp.ReadBoolBytes(bts)
			if err != nil {
				err = msgp.WrapError(err, "recursive")
				return
			}
		case "v":
			z.dataVersion, bts, err = msgp.ReadUint8Bytes(bts)
			if err != nil {
				err = msgp.WrapError(err, "dataVersion")
				return
			}
		default:
			bts, err = msgp.Skip(bts)
			if err != nil {
				err = msgp.WrapError(err)
				return
			}
		}
	}
	o = bts
	return
}

// Msgsize returns an upper bound estimate of the number of bytes occupied by the serialized message
func (z *metacache) Msgsize() (s int) {
	s = 1 + 4 + msgp.TimeSize + 3 + msgp.TimeSize + 3 + msgp.TimeSize + 2 + msgp.TimeSize + 2 + msgp.StringPrefixSize + len(z.bucket) + 4 + msgp.StringPrefixSize + len(z.filter) + 3 + msgp.StringPrefixSize + len(z.id) + 4 + msgp.StringPrefixSize + len(z.error) + 5 + msgp.StringPrefixSize + len(z.root) + 4 + msgp.BoolSize + 5 + msgp.Uint8Size + 4 + msgp.BoolSize + 2 + msgp.Uint8Size
	return
}

// DecodeMsg implements msgp.Decodable
func (z *scanStatus) DecodeMsg(dc *msgp.Reader) (err error) {
	{
		var zb0001 uint8
		zb0001, err = dc.ReadUint8()
		if err != nil {
			err = msgp.WrapError(err)
			return
		}
		(*z) = scanStatus(zb0001)
	}
	return
}

// EncodeMsg implements msgp.Encodable
func (z scanStatus) EncodeMsg(en *msgp.Writer) (err error) {
	err = en.WriteUint8(uint8(z))
	if err != nil {
		err = msgp.WrapError(err)
		return
	}
	return
}

// MarshalMsg implements msgp.Marshaler
func (z scanStatus) MarshalMsg(b []byte) (o []byte, err error) {
	o = msgp.Require(b, z.Msgsize())
	o = msgp.AppendUint8(o, uint8(z))
	return
}

// UnmarshalMsg implements msgp.Unmarshaler
func (z *scanStatus) UnmarshalMsg(bts []byte) (o []byte, err error) {
	{
		var zb0001 uint8
		zb0001, bts, err = msgp.ReadUint8Bytes(bts)
		if err != nil {
			err = msgp.WrapError(err)
			return
		}
		(*z) = scanStatus(zb0001)
	}
	o = bts
	return
}

// Msgsize returns an upper bound estimate of the number of bytes occupied by the serialized message
func (z scanStatus) Msgsize() (s int) {
	s = msgp.Uint8Size
	return
}
