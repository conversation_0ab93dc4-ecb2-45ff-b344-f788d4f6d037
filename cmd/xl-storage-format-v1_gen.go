package cmd

// Code generated by github.com/tinylib/msgp DO NOT EDIT.

import (
	"github.com/tinylib/msgp/msgp"
)

// DecodeMsg implements msgp.Decodable
func (z *BitrotAlgorithm) DecodeMsg(dc *msgp.Reader) (err error) {
	{
		var zb0001 uint
		zb0001, err = dc.ReadUint()
		if err != nil {
			err = msgp.WrapError(err)
			return
		}
		(*z) = BitrotAlgorithm(zb0001)
	}
	return
}

// EncodeMsg implements msgp.Encodable
func (z BitrotAlgorithm) EncodeMsg(en *msgp.Writer) (err error) {
	err = en.WriteUint(uint(z))
	if err != nil {
		err = msgp.WrapError(err)
		return
	}
	return
}

// MarshalMsg implements msgp.Marshaler
func (z BitrotAlgorithm) MarshalMsg(b []byte) (o []byte, err error) {
	o = msgp.Require(b, z.Msgsize())
	o = msgp.AppendUint(o, uint(z))
	return
}

// UnmarshalMsg implements msgp.Unmarshaler
func (z *BitrotAlgorithm) UnmarshalMsg(bts []byte) (o []byte, err error) {
	{
		var zb0001 uint
		zb0001, bts, err = msgp.ReadUintBytes(bts)
		if err != nil {
			err = msgp.WrapError(err)
			return
		}
		(*z) = BitrotAlgorithm(zb0001)
	}
	o = bts
	return
}

// Msgsize returns an upper bound estimate of the number of bytes occupied by the serialized message
func (z BitrotAlgorithm) Msgsize() (s int) {
	s = msgp.UintSize
	return
}

// DecodeMsg implements msgp.Decodable
func (z *ChecksumInfo) DecodeMsg(dc *msgp.Reader) (err error) {
	var field []byte
	_ = field
	var zb0001 uint32
	zb0001, err = dc.ReadMapHeader()
	if err != nil {
		err = msgp.WrapError(err)
		return
	}
	for zb0001 > 0 {
		zb0001--
		field, err = dc.ReadMapKeyPtr()
		if err != nil {
			err = msgp.WrapError(err)
			return
		}
		switch msgp.UnsafeString(field) {
		case "PartNumber":
			z.PartNumber, err = dc.ReadInt()
			if err != nil {
				err = msgp.WrapError(err, "PartNumber")
				return
			}
		case "Algorithm":
			{
				var zb0002 uint
				zb0002, err = dc.ReadUint()
				if err != nil {
					err = msgp.WrapError(err, "Algorithm")
					return
				}
				z.Algorithm = BitrotAlgorithm(zb0002)
			}
		case "Hash":
			z.Hash, err = dc.ReadBytes(z.Hash)
			if err != nil {
				err = msgp.WrapError(err, "Hash")
				return
			}
		default:
			err = dc.Skip()
			if err != nil {
				err = msgp.WrapError(err)
				return
			}
		}
	}
	return
}

// EncodeMsg implements msgp.Encodable
func (z *ChecksumInfo) EncodeMsg(en *msgp.Writer) (err error) {
	// map header, size 3
	// write "PartNumber"
	err = en.Append(0x83, 0xaa, 0x50, 0x61, 0x72, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72)
	if err != nil {
		return
	}
	err = en.WriteInt(z.PartNumber)
	if err != nil {
		err = msgp.WrapError(err, "PartNumber")
		return
	}
	// write "Algorithm"
	err = en.Append(0xa9, 0x41, 0x6c, 0x67, 0x6f, 0x72, 0x69, 0x74, 0x68, 0x6d)
	if err != nil {
		return
	}
	err = en.WriteUint(uint(z.Algorithm))
	if err != nil {
		err = msgp.WrapError(err, "Algorithm")
		return
	}
	// write "Hash"
	err = en.Append(0xa4, 0x48, 0x61, 0x73, 0x68)
	if err != nil {
		return
	}
	err = en.WriteBytes(z.Hash)
	if err != nil {
		err = msgp.WrapError(err, "Hash")
		return
	}
	return
}

// MarshalMsg implements msgp.Marshaler
func (z *ChecksumInfo) MarshalMsg(b []byte) (o []byte, err error) {
	o = msgp.Require(b, z.Msgsize())
	// map header, size 3
	// string "PartNumber"
	o = append(o, 0x83, 0xaa, 0x50, 0x61, 0x72, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72)
	o = msgp.AppendInt(o, z.PartNumber)
	// string "Algorithm"
	o = append(o, 0xa9, 0x41, 0x6c, 0x67, 0x6f, 0x72, 0x69, 0x74, 0x68, 0x6d)
	o = msgp.AppendUint(o, uint(z.Algorithm))
	// string "Hash"
	o = append(o, 0xa4, 0x48, 0x61, 0x73, 0x68)
	o = msgp.AppendBytes(o, z.Hash)
	return
}

// UnmarshalMsg implements msgp.Unmarshaler
func (z *ChecksumInfo) UnmarshalMsg(bts []byte) (o []byte, err error) {
	var field []byte
	_ = field
	var zb0001 uint32
	zb0001, bts, err = msgp.ReadMapHeaderBytes(bts)
	if err != nil {
		err = msgp.WrapError(err)
		return
	}
	for zb0001 > 0 {
		zb0001--
		field, bts, err = msgp.ReadMapKeyZC(bts)
		if err != nil {
			err = msgp.WrapError(err)
			return
		}
		switch msgp.UnsafeString(field) {
		case "PartNumber":
			z.PartNumber, bts, err = msgp.ReadIntBytes(bts)
			if err != nil {
				err = msgp.WrapError(err, "PartNumber")
				return
			}
		case "Algorithm":
			{
				var zb0002 uint
				zb0002, bts, err = msgp.ReadUintBytes(bts)
				if err != nil {
					err = msgp.WrapError(err, "Algorithm")
					return
				}
				z.Algorithm = BitrotAlgorithm(zb0002)
			}
		case "Hash":
			z.Hash, bts, err = msgp.ReadBytesBytes(bts, z.Hash)
			if err != nil {
				err = msgp.WrapError(err, "Hash")
				return
			}
		default:
			bts, err = msgp.Skip(bts)
			if err != nil {
				err = msgp.WrapError(err)
				return
			}
		}
	}
	o = bts
	return
}

// Msgsize returns an upper bound estimate of the number of bytes occupied by the serialized message
func (z *ChecksumInfo) Msgsize() (s int) {
	s = 1 + 11 + msgp.IntSize + 10 + msgp.UintSize + 5 + msgp.BytesPrefixSize + len(z.Hash)
	return
}

// DecodeMsg implements msgp.Decodable
func (z *ErasureInfo) DecodeMsg(dc *msgp.Reader) (err error) {
	var field []byte
	_ = field
	var zb0001 uint32
	zb0001, err = dc.ReadMapHeader()
	if err != nil {
		err = msgp.WrapError(err)
		return
	}
	for zb0001 > 0 {
		zb0001--
		field, err = dc.ReadMapKeyPtr()
		if err != nil {
			err = msgp.WrapError(err)
			return
		}
		switch msgp.UnsafeString(field) {
		case "Algorithm":
			z.Algorithm, err = dc.ReadString()
			if err != nil {
				err = msgp.WrapError(err, "Algorithm")
				return
			}
		case "DataBlocks":
			z.DataBlocks, err = dc.ReadInt()
			if err != nil {
				err = msgp.WrapError(err, "DataBlocks")
				return
			}
		case "ParityBlocks":
			z.ParityBlocks, err = dc.ReadInt()
			if err != nil {
				err = msgp.WrapError(err, "ParityBlocks")
				return
			}
		case "BlockSize":
			z.BlockSize, err = dc.ReadInt64()
			if err != nil {
				err = msgp.WrapError(err, "BlockSize")
				return
			}
		case "Index":
			z.Index, err = dc.ReadInt()
			if err != nil {
				err = msgp.WrapError(err, "Index")
				return
			}
		case "Distribution":
			var zb0002 uint32
			zb0002, err = dc.ReadArrayHeader()
			if err != nil {
				err = msgp.WrapError(err, "Distribution")
				return
			}
			if cap(z.Distribution) >= int(zb0002) {
				z.Distribution = (z.Distribution)[:zb0002]
			} else {
				z.Distribution = make([]int, zb0002)
			}
			for za0001 := range z.Distribution {
				z.Distribution[za0001], err = dc.ReadInt()
				if err != nil {
					err = msgp.WrapError(err, "Distribution", za0001)
					return
				}
			}
		case "Checksums":
			var zb0003 uint32
			zb0003, err = dc.ReadArrayHeader()
			if err != nil {
				err = msgp.WrapError(err, "Checksums")
				return
			}
			if cap(z.Checksums) >= int(zb0003) {
				z.Checksums = (z.Checksums)[:zb0003]
			} else {
				z.Checksums = make([]ChecksumInfo, zb0003)
			}
			for za0002 := range z.Checksums {
				err = z.Checksums[za0002].DecodeMsg(dc)
				if err != nil {
					err = msgp.WrapError(err, "Checksums", za0002)
					return
				}
			}
		default:
			err = dc.Skip()
			if err != nil {
				err = msgp.WrapError(err)
				return
			}
		}
	}
	return
}

// EncodeMsg implements msgp.Encodable
func (z *ErasureInfo) EncodeMsg(en *msgp.Writer) (err error) {
	// map header, size 7
	// write "Algorithm"
	err = en.Append(0x87, 0xa9, 0x41, 0x6c, 0x67, 0x6f, 0x72, 0x69, 0x74, 0x68, 0x6d)
	if err != nil {
		return
	}
	err = en.WriteString(z.Algorithm)
	if err != nil {
		err = msgp.WrapError(err, "Algorithm")
		return
	}
	// write "DataBlocks"
	err = en.Append(0xaa, 0x44, 0x61, 0x74, 0x61, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x73)
	if err != nil {
		return
	}
	err = en.WriteInt(z.DataBlocks)
	if err != nil {
		err = msgp.WrapError(err, "DataBlocks")
		return
	}
	// write "ParityBlocks"
	err = en.Append(0xac, 0x50, 0x61, 0x72, 0x69, 0x74, 0x79, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x73)
	if err != nil {
		return
	}
	err = en.WriteInt(z.ParityBlocks)
	if err != nil {
		err = msgp.WrapError(err, "ParityBlocks")
		return
	}
	// write "BlockSize"
	err = en.Append(0xa9, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x53, 0x69, 0x7a, 0x65)
	if err != nil {
		return
	}
	err = en.WriteInt64(z.BlockSize)
	if err != nil {
		err = msgp.WrapError(err, "BlockSize")
		return
	}
	// write "Index"
	err = en.Append(0xa5, 0x49, 0x6e, 0x64, 0x65, 0x78)
	if err != nil {
		return
	}
	err = en.WriteInt(z.Index)
	if err != nil {
		err = msgp.WrapError(err, "Index")
		return
	}
	// write "Distribution"
	err = en.Append(0xac, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x69, 0x6f, 0x6e)
	if err != nil {
		return
	}
	err = en.WriteArrayHeader(uint32(len(z.Distribution)))
	if err != nil {
		err = msgp.WrapError(err, "Distribution")
		return
	}
	for za0001 := range z.Distribution {
		err = en.WriteInt(z.Distribution[za0001])
		if err != nil {
			err = msgp.WrapError(err, "Distribution", za0001)
			return
		}
	}
	// write "Checksums"
	err = en.Append(0xa9, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x73, 0x75, 0x6d, 0x73)
	if err != nil {
		return
	}
	err = en.WriteArrayHeader(uint32(len(z.Checksums)))
	if err != nil {
		err = msgp.WrapError(err, "Checksums")
		return
	}
	for za0002 := range z.Checksums {
		err = z.Checksums[za0002].EncodeMsg(en)
		if err != nil {
			err = msgp.WrapError(err, "Checksums", za0002)
			return
		}
	}
	return
}

// MarshalMsg implements msgp.Marshaler
func (z *ErasureInfo) MarshalMsg(b []byte) (o []byte, err error) {
	o = msgp.Require(b, z.Msgsize())
	// map header, size 7
	// string "Algorithm"
	o = append(o, 0x87, 0xa9, 0x41, 0x6c, 0x67, 0x6f, 0x72, 0x69, 0x74, 0x68, 0x6d)
	o = msgp.AppendString(o, z.Algorithm)
	// string "DataBlocks"
	o = append(o, 0xaa, 0x44, 0x61, 0x74, 0x61, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x73)
	o = msgp.AppendInt(o, z.DataBlocks)
	// string "ParityBlocks"
	o = append(o, 0xac, 0x50, 0x61, 0x72, 0x69, 0x74, 0x79, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x73)
	o = msgp.AppendInt(o, z.ParityBlocks)
	// string "BlockSize"
	o = append(o, 0xa9, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x53, 0x69, 0x7a, 0x65)
	o = msgp.AppendInt64(o, z.BlockSize)
	// string "Index"
	o = append(o, 0xa5, 0x49, 0x6e, 0x64, 0x65, 0x78)
	o = msgp.AppendInt(o, z.Index)
	// string "Distribution"
	o = append(o, 0xac, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x69, 0x6f, 0x6e)
	o = msgp.AppendArrayHeader(o, uint32(len(z.Distribution)))
	for za0001 := range z.Distribution {
		o = msgp.AppendInt(o, z.Distribution[za0001])
	}
	// string "Checksums"
	o = append(o, 0xa9, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x73, 0x75, 0x6d, 0x73)
	o = msgp.AppendArrayHeader(o, uint32(len(z.Checksums)))
	for za0002 := range z.Checksums {
		o, err = z.Checksums[za0002].MarshalMsg(o)
		if err != nil {
			err = msgp.WrapError(err, "Checksums", za0002)
			return
		}
	}
	return
}

// UnmarshalMsg implements msgp.Unmarshaler
func (z *ErasureInfo) UnmarshalMsg(bts []byte) (o []byte, err error) {
	var field []byte
	_ = field
	var zb0001 uint32
	zb0001, bts, err = msgp.ReadMapHeaderBytes(bts)
	if err != nil {
		err = msgp.WrapError(err)
		return
	}
	for zb0001 > 0 {
		zb0001--
		field, bts, err = msgp.ReadMapKeyZC(bts)
		if err != nil {
			err = msgp.WrapError(err)
			return
		}
		switch msgp.UnsafeString(field) {
		case "Algorithm":
			z.Algorithm, bts, err = msgp.ReadStringBytes(bts)
			if err != nil {
				err = msgp.WrapError(err, "Algorithm")
				return
			}
		case "DataBlocks":
			z.DataBlocks, bts, err = msgp.ReadIntBytes(bts)
			if err != nil {
				err = msgp.WrapError(err, "DataBlocks")
				return
			}
		case "ParityBlocks":
			z.ParityBlocks, bts, err = msgp.ReadIntBytes(bts)
			if err != nil {
				err = msgp.WrapError(err, "ParityBlocks")
				return
			}
		case "BlockSize":
			z.BlockSize, bts, err = msgp.ReadInt64Bytes(bts)
			if err != nil {
				err = msgp.WrapError(err, "BlockSize")
				return
			}
		case "Index":
			z.Index, bts, err = msgp.ReadIntBytes(bts)
			if err != nil {
				err = msgp.WrapError(err, "Index")
				return
			}
		case "Distribution":
			var zb0002 uint32
			zb0002, bts, err = msgp.ReadArrayHeaderBytes(bts)
			if err != nil {
				err = msgp.WrapError(err, "Distribution")
				return
			}
			if cap(z.Distribution) >= int(zb0002) {
				z.Distribution = (z.Distribution)[:zb0002]
			} else {
				z.Distribution = make([]int, zb0002)
			}
			for za0001 := range z.Distribution {
				z.Distribution[za0001], bts, err = msgp.ReadIntBytes(bts)
				if err != nil {
					err = msgp.WrapError(err, "Distribution", za0001)
					return
				}
			}
		case "Checksums":
			var zb0003 uint32
			zb0003, bts, err = msgp.ReadArrayHeaderBytes(bts)
			if err != nil {
				err = msgp.WrapError(err, "Checksums")
				return
			}
			if cap(z.Checksums) >= int(zb0003) {
				z.Checksums = (z.Checksums)[:zb0003]
			} else {
				z.Checksums = make([]ChecksumInfo, zb0003)
			}
			for za0002 := range z.Checksums {
				bts, err = z.Checksums[za0002].UnmarshalMsg(bts)
				if err != nil {
					err = msgp.WrapError(err, "Checksums", za0002)
					return
				}
			}
		default:
			bts, err = msgp.Skip(bts)
			if err != nil {
				err = msgp.WrapError(err)
				return
			}
		}
	}
	o = bts
	return
}

// Msgsize returns an upper bound estimate of the number of bytes occupied by the serialized message
func (z *ErasureInfo) Msgsize() (s int) {
	s = 1 + 10 + msgp.StringPrefixSize + len(z.Algorithm) + 11 + msgp.IntSize + 13 + msgp.IntSize + 10 + msgp.Int64Size + 6 + msgp.IntSize + 13 + msgp.ArrayHeaderSize + (len(z.Distribution) * (msgp.IntSize)) + 10 + msgp.ArrayHeaderSize
	for za0002 := range z.Checksums {
		s += z.Checksums[za0002].Msgsize()
	}
	return
}

// DecodeMsg implements msgp.Decodable
func (z *ObjectPartInfo) DecodeMsg(dc *msgp.Reader) (err error) {
	var field []byte
	_ = field
	var zb0001 uint32
	zb0001, err = dc.ReadMapHeader()
	if err != nil {
		err = msgp.WrapError(err)
		return
	}
	for zb0001 > 0 {
		zb0001--
		field, err = dc.ReadMapKeyPtr()
		if err != nil {
			err = msgp.WrapError(err)
			return
		}
		switch msgp.UnsafeString(field) {
		case "ETag":
			z.ETag, err = dc.ReadString()
			if err != nil {
				err = msgp.WrapError(err, "ETag")
				return
			}
		case "Number":
			z.Number, err = dc.ReadInt()
			if err != nil {
				err = msgp.WrapError(err, "Number")
				return
			}
		case "Size":
			z.Size, err = dc.ReadInt64()
			if err != nil {
				err = msgp.WrapError(err, "Size")
				return
			}
		case "ActualSize":
			z.ActualSize, err = dc.ReadInt64()
			if err != nil {
				err = msgp.WrapError(err, "ActualSize")
				return
			}
		default:
			err = dc.Skip()
			if err != nil {
				err = msgp.WrapError(err)
				return
			}
		}
	}
	return
}

// EncodeMsg implements msgp.Encodable
func (z *ObjectPartInfo) EncodeMsg(en *msgp.Writer) (err error) {
	// map header, size 4
	// write "ETag"
	err = en.Append(0x84, 0xa4, 0x45, 0x54, 0x61, 0x67)
	if err != nil {
		return
	}
	err = en.WriteString(z.ETag)
	if err != nil {
		err = msgp.WrapError(err, "ETag")
		return
	}
	// write "Number"
	err = en.Append(0xa6, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72)
	if err != nil {
		return
	}
	err = en.WriteInt(z.Number)
	if err != nil {
		err = msgp.WrapError(err, "Number")
		return
	}
	// write "Size"
	err = en.Append(0xa4, 0x53, 0x69, 0x7a, 0x65)
	if err != nil {
		return
	}
	err = en.WriteInt64(z.Size)
	if err != nil {
		err = msgp.WrapError(err, "Size")
		return
	}
	// write "ActualSize"
	err = en.Append(0xaa, 0x41, 0x63, 0x74, 0x75, 0x61, 0x6c, 0x53, 0x69, 0x7a, 0x65)
	if err != nil {
		return
	}
	err = en.WriteInt64(z.ActualSize)
	if err != nil {
		err = msgp.WrapError(err, "ActualSize")
		return
	}
	return
}

// MarshalMsg implements msgp.Marshaler
func (z *ObjectPartInfo) MarshalMsg(b []byte) (o []byte, err error) {
	o = msgp.Require(b, z.Msgsize())
	// map header, size 4
	// string "ETag"
	o = append(o, 0x84, 0xa4, 0x45, 0x54, 0x61, 0x67)
	o = msgp.AppendString(o, z.ETag)
	// string "Number"
	o = append(o, 0xa6, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72)
	o = msgp.AppendInt(o, z.Number)
	// string "Size"
	o = append(o, 0xa4, 0x53, 0x69, 0x7a, 0x65)
	o = msgp.AppendInt64(o, z.Size)
	// string "ActualSize"
	o = append(o, 0xaa, 0x41, 0x63, 0x74, 0x75, 0x61, 0x6c, 0x53, 0x69, 0x7a, 0x65)
	o = msgp.AppendInt64(o, z.ActualSize)
	return
}

// UnmarshalMsg implements msgp.Unmarshaler
func (z *ObjectPartInfo) UnmarshalMsg(bts []byte) (o []byte, err error) {
	var field []byte
	_ = field
	var zb0001 uint32
	zb0001, bts, err = msgp.ReadMapHeaderBytes(bts)
	if err != nil {
		err = msgp.WrapError(err)
		return
	}
	for zb0001 > 0 {
		zb0001--
		field, bts, err = msgp.ReadMapKeyZC(bts)
		if err != nil {
			err = msgp.WrapError(err)
			return
		}
		switch msgp.UnsafeString(field) {
		case "ETag":
			z.ETag, bts, err = msgp.ReadStringBytes(bts)
			if err != nil {
				err = msgp.WrapError(err, "ETag")
				return
			}
		case "Number":
			z.Number, bts, err = msgp.ReadIntBytes(bts)
			if err != nil {
				err = msgp.WrapError(err, "Number")
				return
			}
		case "Size":
			z.Size, bts, err = msgp.ReadInt64Bytes(bts)
			if err != nil {
				err = msgp.WrapError(err, "Size")
				return
			}
		case "ActualSize":
			z.ActualSize, bts, err = msgp.ReadInt64Bytes(bts)
			if err != nil {
				err = msgp.WrapError(err, "ActualSize")
				return
			}
		default:
			bts, err = msgp.Skip(bts)
			if err != nil {
				err = msgp.WrapError(err)
				return
			}
		}
	}
	o = bts
	return
}

// Msgsize returns an upper bound estimate of the number of bytes occupied by the serialized message
func (z *ObjectPartInfo) Msgsize() (s int) {
	s = 1 + 5 + msgp.StringPrefixSize + len(z.ETag) + 7 + msgp.IntSize + 5 + msgp.Int64Size + 11 + msgp.Int64Size
	return
}

// DecodeMsg implements msgp.Decodable
func (z *StatInfo) DecodeMsg(dc *msgp.Reader) (err error) {
	var field []byte
	_ = field
	var zb0001 uint32
	zb0001, err = dc.ReadMapHeader()
	if err != nil {
		err = msgp.WrapError(err)
		return
	}
	for zb0001 > 0 {
		zb0001--
		field, err = dc.ReadMapKeyPtr()
		if err != nil {
			err = msgp.WrapError(err)
			return
		}
		switch msgp.UnsafeString(field) {
		case "Size":
			z.Size, err = dc.ReadInt64()
			if err != nil {
				err = msgp.WrapError(err, "Size")
				return
			}
		case "ModTime":
			z.ModTime, err = dc.ReadTime()
			if err != nil {
				err = msgp.WrapError(err, "ModTime")
				return
			}
		case "Name":
			z.Name, err = dc.ReadString()
			if err != nil {
				err = msgp.WrapError(err, "Name")
				return
			}
		case "Dir":
			z.Dir, err = dc.ReadBool()
			if err != nil {
				err = msgp.WrapError(err, "Dir")
				return
			}
		case "Mode":
			z.Mode, err = dc.ReadUint32()
			if err != nil {
				err = msgp.WrapError(err, "Mode")
				return
			}
		default:
			err = dc.Skip()
			if err != nil {
				err = msgp.WrapError(err)
				return
			}
		}
	}
	return
}

// EncodeMsg implements msgp.Encodable
func (z *StatInfo) EncodeMsg(en *msgp.Writer) (err error) {
	// map header, size 5
	// write "Size"
	err = en.Append(0x85, 0xa4, 0x53, 0x69, 0x7a, 0x65)
	if err != nil {
		return
	}
	err = en.WriteInt64(z.Size)
	if err != nil {
		err = msgp.WrapError(err, "Size")
		return
	}
	// write "ModTime"
	err = en.Append(0xa7, 0x4d, 0x6f, 0x64, 0x54, 0x69, 0x6d, 0x65)
	if err != nil {
		return
	}
	err = en.WriteTime(z.ModTime)
	if err != nil {
		err = msgp.WrapError(err, "ModTime")
		return
	}
	// write "Name"
	err = en.Append(0xa4, 0x4e, 0x61, 0x6d, 0x65)
	if err != nil {
		return
	}
	err = en.WriteString(z.Name)
	if err != nil {
		err = msgp.WrapError(err, "Name")
		return
	}
	// write "Dir"
	err = en.Append(0xa3, 0x44, 0x69, 0x72)
	if err != nil {
		return
	}
	err = en.WriteBool(z.Dir)
	if err != nil {
		err = msgp.WrapError(err, "Dir")
		return
	}
	// write "Mode"
	err = en.Append(0xa4, 0x4d, 0x6f, 0x64, 0x65)
	if err != nil {
		return
	}
	err = en.WriteUint32(z.Mode)
	if err != nil {
		err = msgp.WrapError(err, "Mode")
		return
	}
	return
}

// MarshalMsg implements msgp.Marshaler
func (z *StatInfo) MarshalMsg(b []byte) (o []byte, err error) {
	o = msgp.Require(b, z.Msgsize())
	// map header, size 5
	// string "Size"
	o = append(o, 0x85, 0xa4, 0x53, 0x69, 0x7a, 0x65)
	o = msgp.AppendInt64(o, z.Size)
	// string "ModTime"
	o = append(o, 0xa7, 0x4d, 0x6f, 0x64, 0x54, 0x69, 0x6d, 0x65)
	o = msgp.AppendTime(o, z.ModTime)
	// string "Name"
	o = append(o, 0xa4, 0x4e, 0x61, 0x6d, 0x65)
	o = msgp.AppendString(o, z.Name)
	// string "Dir"
	o = append(o, 0xa3, 0x44, 0x69, 0x72)
	o = msgp.AppendBool(o, z.Dir)
	// string "Mode"
	o = append(o, 0xa4, 0x4d, 0x6f, 0x64, 0x65)
	o = msgp.AppendUint32(o, z.Mode)
	return
}

// UnmarshalMsg implements msgp.Unmarshaler
func (z *StatInfo) UnmarshalMsg(bts []byte) (o []byte, err error) {
	var field []byte
	_ = field
	var zb0001 uint32
	zb0001, bts, err = msgp.ReadMapHeaderBytes(bts)
	if err != nil {
		err = msgp.WrapError(err)
		return
	}
	for zb0001 > 0 {
		zb0001--
		field, bts, err = msgp.ReadMapKeyZC(bts)
		if err != nil {
			err = msgp.WrapError(err)
			return
		}
		switch msgp.UnsafeString(field) {
		case "Size":
			z.Size, bts, err = msgp.ReadInt64Bytes(bts)
			if err != nil {
				err = msgp.WrapError(err, "Size")
				return
			}
		case "ModTime":
			z.ModTime, bts, err = msgp.ReadTimeBytes(bts)
			if err != nil {
				err = msgp.WrapError(err, "ModTime")
				return
			}
		case "Name":
			z.Name, bts, err = msgp.ReadStringBytes(bts)
			if err != nil {
				err = msgp.WrapError(err, "Name")
				return
			}
		case "Dir":
			z.Dir, bts, err = msgp.ReadBoolBytes(bts)
			if err != nil {
				err = msgp.WrapError(err, "Dir")
				return
			}
		case "Mode":
			z.Mode, bts, err = msgp.ReadUint32Bytes(bts)
			if err != nil {
				err = msgp.WrapError(err, "Mode")
				return
			}
		default:
			bts, err = msgp.Skip(bts)
			if err != nil {
				err = msgp.WrapError(err)
				return
			}
		}
	}
	o = bts
	return
}

// Msgsize returns an upper bound estimate of the number of bytes occupied by the serialized message
func (z *StatInfo) Msgsize() (s int) {
	s = 1 + 5 + msgp.Int64Size + 8 + msgp.TimeSize + 5 + msgp.StringPrefixSize + len(z.Name) + 4 + msgp.BoolSize + 5 + msgp.Uint32Size
	return
}

// DecodeMsg implements msgp.Decodable
func (z *checksumInfoJSON) DecodeMsg(dc *msgp.Reader) (err error) {
	var field []byte
	_ = field
	var zb0001 uint32
	zb0001, err = dc.ReadMapHeader()
	if err != nil {
		err = msgp.WrapError(err)
		return
	}
	for zb0001 > 0 {
		zb0001--
		field, err = dc.ReadMapKeyPtr()
		if err != nil {
			err = msgp.WrapError(err)
			return
		}
		switch msgp.UnsafeString(field) {
		case "Name":
			z.Name, err = dc.ReadString()
			if err != nil {
				err = msgp.WrapError(err, "Name")
				return
			}
		case "Algorithm":
			z.Algorithm, err = dc.ReadString()
			if err != nil {
				err = msgp.WrapError(err, "Algorithm")
				return
			}
		case "Hash":
			z.Hash, err = dc.ReadString()
			if err != nil {
				err = msgp.WrapError(err, "Hash")
				return
			}
		default:
			err = dc.Skip()
			if err != nil {
				err = msgp.WrapError(err)
				return
			}
		}
	}
	return
}

// EncodeMsg implements msgp.Encodable
func (z checksumInfoJSON) EncodeMsg(en *msgp.Writer) (err error) {
	// map header, size 3
	// write "Name"
	err = en.Append(0x83, 0xa4, 0x4e, 0x61, 0x6d, 0x65)
	if err != nil {
		return
	}
	err = en.WriteString(z.Name)
	if err != nil {
		err = msgp.WrapError(err, "Name")
		return
	}
	// write "Algorithm"
	err = en.Append(0xa9, 0x41, 0x6c, 0x67, 0x6f, 0x72, 0x69, 0x74, 0x68, 0x6d)
	if err != nil {
		return
	}
	err = en.WriteString(z.Algorithm)
	if err != nil {
		err = msgp.WrapError(err, "Algorithm")
		return
	}
	// write "Hash"
	err = en.Append(0xa4, 0x48, 0x61, 0x73, 0x68)
	if err != nil {
		return
	}
	err = en.WriteString(z.Hash)
	if err != nil {
		err = msgp.WrapError(err, "Hash")
		return
	}
	return
}

// MarshalMsg implements msgp.Marshaler
func (z checksumInfoJSON) MarshalMsg(b []byte) (o []byte, err error) {
	o = msgp.Require(b, z.Msgsize())
	// map header, size 3
	// string "Name"
	o = append(o, 0x83, 0xa4, 0x4e, 0x61, 0x6d, 0x65)
	o = msgp.AppendString(o, z.Name)
	// string "Algorithm"
	o = append(o, 0xa9, 0x41, 0x6c, 0x67, 0x6f, 0x72, 0x69, 0x74, 0x68, 0x6d)
	o = msgp.AppendString(o, z.Algorithm)
	// string "Hash"
	o = append(o, 0xa4, 0x48, 0x61, 0x73, 0x68)
	o = msgp.AppendString(o, z.Hash)
	return
}

// UnmarshalMsg implements msgp.Unmarshaler
func (z *checksumInfoJSON) UnmarshalMsg(bts []byte) (o []byte, err error) {
	var field []byte
	_ = field
	var zb0001 uint32
	zb0001, bts, err = msgp.ReadMapHeaderBytes(bts)
	if err != nil {
		err = msgp.WrapError(err)
		return
	}
	for zb0001 > 0 {
		zb0001--
		field, bts, err = msgp.ReadMapKeyZC(bts)
		if err != nil {
			err = msgp.WrapError(err)
			return
		}
		switch msgp.UnsafeString(field) {
		case "Name":
			z.Name, bts, err = msgp.ReadStringBytes(bts)
			if err != nil {
				err = msgp.WrapError(err, "Name")
				return
			}
		case "Algorithm":
			z.Algorithm, bts, err = msgp.ReadStringBytes(bts)
			if err != nil {
				err = msgp.WrapError(err, "Algorithm")
				return
			}
		case "Hash":
			z.Hash, bts, err = msgp.ReadStringBytes(bts)
			if err != nil {
				err = msgp.WrapError(err, "Hash")
				return
			}
		default:
			bts, err = msgp.Skip(bts)
			if err != nil {
				err = msgp.WrapError(err)
				return
			}
		}
	}
	o = bts
	return
}

// Msgsize returns an upper bound estimate of the number of bytes occupied by the serialized message
func (z checksumInfoJSON) Msgsize() (s int) {
	s = 1 + 5 + msgp.StringPrefixSize + len(z.Name) + 10 + msgp.StringPrefixSize + len(z.Algorithm) + 5 + msgp.StringPrefixSize + len(z.Hash)
	return
}

// DecodeMsg implements msgp.Decodable
func (z *xlMetaV1Object) DecodeMsg(dc *msgp.Reader) (err error) {
	var field []byte
	_ = field
	var zb0001 uint32
	zb0001, err = dc.ReadMapHeader()
	if err != nil {
		err = msgp.WrapError(err)
		return
	}
	for zb0001 > 0 {
		zb0001--
		field, err = dc.ReadMapKeyPtr()
		if err != nil {
			err = msgp.WrapError(err)
			return
		}
		switch msgp.UnsafeString(field) {
		case "Version":
			z.Version, err = dc.ReadString()
			if err != nil {
				err = msgp.WrapError(err, "Version")
				return
			}
		case "Format":
			z.Format, err = dc.ReadString()
			if err != nil {
				err = msgp.WrapError(err, "Format")
				return
			}
		case "Stat":
			err = z.Stat.DecodeMsg(dc)
			if err != nil {
				err = msgp.WrapError(err, "Stat")
				return
			}
		case "Erasure":
			err = z.Erasure.DecodeMsg(dc)
			if err != nil {
				err = msgp.WrapError(err, "Erasure")
				return
			}
		case "Minio":
			var zb0002 uint32
			zb0002, err = dc.ReadMapHeader()
			if err != nil {
				err = msgp.WrapError(err, "Minio")
				return
			}
			for zb0002 > 0 {
				zb0002--
				field, err = dc.ReadMapKeyPtr()
				if err != nil {
					err = msgp.WrapError(err, "Minio")
					return
				}
				switch msgp.UnsafeString(field) {
				case "Release":
					z.Minio.Release, err = dc.ReadString()
					if err != nil {
						err = msgp.WrapError(err, "Minio", "Release")
						return
					}
				default:
					err = dc.Skip()
					if err != nil {
						err = msgp.WrapError(err, "Minio")
						return
					}
				}
			}
		case "Meta":
			var zb0003 uint32
			zb0003, err = dc.ReadMapHeader()
			if err != nil {
				err = msgp.WrapError(err, "Meta")
				return
			}
			if z.Meta == nil {
				z.Meta = make(map[string]string, zb0003)
			} else if len(z.Meta) > 0 {
				for key := range z.Meta {
					delete(z.Meta, key)
				}
			}
			for zb0003 > 0 {
				zb0003--
				var za0001 string
				var za0002 string
				za0001, err = dc.ReadString()
				if err != nil {
					err = msgp.WrapError(err, "Meta")
					return
				}
				za0002, err = dc.ReadString()
				if err != nil {
					err = msgp.WrapError(err, "Meta", za0001)
					return
				}
				z.Meta[za0001] = za0002
			}
		case "Parts":
			var zb0004 uint32
			zb0004, err = dc.ReadArrayHeader()
			if err != nil {
				err = msgp.WrapError(err, "Parts")
				return
			}
			if cap(z.Parts) >= int(zb0004) {
				z.Parts = (z.Parts)[:zb0004]
			} else {
				z.Parts = make([]ObjectPartInfo, zb0004)
			}
			for za0003 := range z.Parts {
				err = z.Parts[za0003].DecodeMsg(dc)
				if err != nil {
					err = msgp.WrapError(err, "Parts", za0003)
					return
				}
			}
		case "VersionID":
			z.VersionID, err = dc.ReadString()
			if err != nil {
				err = msgp.WrapError(err, "VersionID")
				return
			}
		case "DataDir":
			z.DataDir, err = dc.ReadString()
			if err != nil {
				err = msgp.WrapError(err, "DataDir")
				return
			}
		default:
			err = dc.Skip()
			if err != nil {
				err = msgp.WrapError(err)
				return
			}
		}
	}
	return
}

// EncodeMsg implements msgp.Encodable
func (z *xlMetaV1Object) EncodeMsg(en *msgp.Writer) (err error) {
	// map header, size 9
	// write "Version"
	err = en.Append(0x89, 0xa7, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e)
	if err != nil {
		return
	}
	err = en.WriteString(z.Version)
	if err != nil {
		err = msgp.WrapError(err, "Version")
		return
	}
	// write "Format"
	err = en.Append(0xa6, 0x46, 0x6f, 0x72, 0x6d, 0x61, 0x74)
	if err != nil {
		return
	}
	err = en.WriteString(z.Format)
	if err != nil {
		err = msgp.WrapError(err, "Format")
		return
	}
	// write "Stat"
	err = en.Append(0xa4, 0x53, 0x74, 0x61, 0x74)
	if err != nil {
		return
	}
	err = z.Stat.EncodeMsg(en)
	if err != nil {
		err = msgp.WrapError(err, "Stat")
		return
	}
	// write "Erasure"
	err = en.Append(0xa7, 0x45, 0x72, 0x61, 0x73, 0x75, 0x72, 0x65)
	if err != nil {
		return
	}
	err = z.Erasure.EncodeMsg(en)
	if err != nil {
		err = msgp.WrapError(err, "Erasure")
		return
	}
	// write "Minio"
	err = en.Append(0xa5, 0x4d, 0x69, 0x6e, 0x69, 0x6f)
	if err != nil {
		return
	}
	// map header, size 1
	// write "Release"
	err = en.Append(0x81, 0xa7, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65)
	if err != nil {
		return
	}
	err = en.WriteString(z.Minio.Release)
	if err != nil {
		err = msgp.WrapError(err, "Minio", "Release")
		return
	}
	// write "Meta"
	err = en.Append(0xa4, 0x4d, 0x65, 0x74, 0x61)
	if err != nil {
		return
	}
	err = en.WriteMapHeader(uint32(len(z.Meta)))
	if err != nil {
		err = msgp.WrapError(err, "Meta")
		return
	}
	for za0001, za0002 := range z.Meta {
		err = en.WriteString(za0001)
		if err != nil {
			err = msgp.WrapError(err, "Meta")
			return
		}
		err = en.WriteString(za0002)
		if err != nil {
			err = msgp.WrapError(err, "Meta", za0001)
			return
		}
	}
	// write "Parts"
	err = en.Append(0xa5, 0x50, 0x61, 0x72, 0x74, 0x73)
	if err != nil {
		return
	}
	err = en.WriteArrayHeader(uint32(len(z.Parts)))
	if err != nil {
		err = msgp.WrapError(err, "Parts")
		return
	}
	for za0003 := range z.Parts {
		err = z.Parts[za0003].EncodeMsg(en)
		if err != nil {
			err = msgp.WrapError(err, "Parts", za0003)
			return
		}
	}
	// write "VersionID"
	err = en.Append(0xa9, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x44)
	if err != nil {
		return
	}
	err = en.WriteString(z.VersionID)
	if err != nil {
		err = msgp.WrapError(err, "VersionID")
		return
	}
	// write "DataDir"
	err = en.Append(0xa7, 0x44, 0x61, 0x74, 0x61, 0x44, 0x69, 0x72)
	if err != nil {
		return
	}
	err = en.WriteString(z.DataDir)
	if err != nil {
		err = msgp.WrapError(err, "DataDir")
		return
	}
	return
}

// MarshalMsg implements msgp.Marshaler
func (z *xlMetaV1Object) MarshalMsg(b []byte) (o []byte, err error) {
	o = msgp.Require(b, z.Msgsize())
	// map header, size 9
	// string "Version"
	o = append(o, 0x89, 0xa7, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e)
	o = msgp.AppendString(o, z.Version)
	// string "Format"
	o = append(o, 0xa6, 0x46, 0x6f, 0x72, 0x6d, 0x61, 0x74)
	o = msgp.AppendString(o, z.Format)
	// string "Stat"
	o = append(o, 0xa4, 0x53, 0x74, 0x61, 0x74)
	o, err = z.Stat.MarshalMsg(o)
	if err != nil {
		err = msgp.WrapError(err, "Stat")
		return
	}
	// string "Erasure"
	o = append(o, 0xa7, 0x45, 0x72, 0x61, 0x73, 0x75, 0x72, 0x65)
	o, err = z.Erasure.MarshalMsg(o)
	if err != nil {
		err = msgp.WrapError(err, "Erasure")
		return
	}
	// string "Minio"
	o = append(o, 0xa5, 0x4d, 0x69, 0x6e, 0x69, 0x6f)
	// map header, size 1
	// string "Release"
	o = append(o, 0x81, 0xa7, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65)
	o = msgp.AppendString(o, z.Minio.Release)
	// string "Meta"
	o = append(o, 0xa4, 0x4d, 0x65, 0x74, 0x61)
	o = msgp.AppendMapHeader(o, uint32(len(z.Meta)))
	for za0001, za0002 := range z.Meta {
		o = msgp.AppendString(o, za0001)
		o = msgp.AppendString(o, za0002)
	}
	// string "Parts"
	o = append(o, 0xa5, 0x50, 0x61, 0x72, 0x74, 0x73)
	o = msgp.AppendArrayHeader(o, uint32(len(z.Parts)))
	for za0003 := range z.Parts {
		o, err = z.Parts[za0003].MarshalMsg(o)
		if err != nil {
			err = msgp.WrapError(err, "Parts", za0003)
			return
		}
	}
	// string "VersionID"
	o = append(o, 0xa9, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x44)
	o = msgp.AppendString(o, z.VersionID)
	// string "DataDir"
	o = append(o, 0xa7, 0x44, 0x61, 0x74, 0x61, 0x44, 0x69, 0x72)
	o = msgp.AppendString(o, z.DataDir)
	return
}

// UnmarshalMsg implements msgp.Unmarshaler
func (z *xlMetaV1Object) UnmarshalMsg(bts []byte) (o []byte, err error) {
	var field []byte
	_ = field
	var zb0001 uint32
	zb0001, bts, err = msgp.ReadMapHeaderBytes(bts)
	if err != nil {
		err = msgp.WrapError(err)
		return
	}
	for zb0001 > 0 {
		zb0001--
		field, bts, err = msgp.ReadMapKeyZC(bts)
		if err != nil {
			err = msgp.WrapError(err)
			return
		}
		switch msgp.UnsafeString(field) {
		case "Version":
			z.Version, bts, err = msgp.ReadStringBytes(bts)
			if err != nil {
				err = msgp.WrapError(err, "Version")
				return
			}
		case "Format":
			z.Format, bts, err = msgp.ReadStringBytes(bts)
			if err != nil {
				err = msgp.WrapError(err, "Format")
				return
			}
		case "Stat":
			bts, err = z.Stat.UnmarshalMsg(bts)
			if err != nil {
				err = msgp.WrapError(err, "Stat")
				return
			}
		case "Erasure":
			bts, err = z.Erasure.UnmarshalMsg(bts)
			if err != nil {
				err = msgp.WrapError(err, "Erasure")
				return
			}
		case "Minio":
			var zb0002 uint32
			zb0002, bts, err = msgp.ReadMapHeaderBytes(bts)
			if err != nil {
				err = msgp.WrapError(err, "Minio")
				return
			}
			for zb0002 > 0 {
				zb0002--
				field, bts, err = msgp.ReadMapKeyZC(bts)
				if err != nil {
					err = msgp.WrapError(err, "Minio")
					return
				}
				switch msgp.UnsafeString(field) {
				case "Release":
					z.Minio.Release, bts, err = msgp.ReadStringBytes(bts)
					if err != nil {
						err = msgp.WrapError(err, "Minio", "Release")
						return
					}
				default:
					bts, err = msgp.Skip(bts)
					if err != nil {
						err = msgp.WrapError(err, "Minio")
						return
					}
				}
			}
		case "Meta":
			var zb0003 uint32
			zb0003, bts, err = msgp.ReadMapHeaderBytes(bts)
			if err != nil {
				err = msgp.WrapError(err, "Meta")
				return
			}
			if z.Meta == nil {
				z.Meta = make(map[string]string, zb0003)
			} else if len(z.Meta) > 0 {
				for key := range z.Meta {
					delete(z.Meta, key)
				}
			}
			for zb0003 > 0 {
				var za0001 string
				var za0002 string
				zb0003--
				za0001, bts, err = msgp.ReadStringBytes(bts)
				if err != nil {
					err = msgp.WrapError(err, "Meta")
					return
				}
				za0002, bts, err = msgp.ReadStringBytes(bts)
				if err != nil {
					err = msgp.WrapError(err, "Meta", za0001)
					return
				}
				z.Meta[za0001] = za0002
			}
		case "Parts":
			var zb0004 uint32
			zb0004, bts, err = msgp.ReadArrayHeaderBytes(bts)
			if err != nil {
				err = msgp.WrapError(err, "Parts")
				return
			}
			if cap(z.Parts) >= int(zb0004) {
				z.Parts = (z.Parts)[:zb0004]
			} else {
				z.Parts = make([]ObjectPartInfo, zb0004)
			}
			for za0003 := range z.Parts {
				bts, err = z.Parts[za0003].UnmarshalMsg(bts)
				if err != nil {
					err = msgp.WrapError(err, "Parts", za0003)
					return
				}
			}
		case "VersionID":
			z.VersionID, bts, err = msgp.ReadStringBytes(bts)
			if err != nil {
				err = msgp.WrapError(err, "VersionID")
				return
			}
		case "DataDir":
			z.DataDir, bts, err = msgp.ReadStringBytes(bts)
			if err != nil {
				err = msgp.WrapError(err, "DataDir")
				return
			}
		default:
			bts, err = msgp.Skip(bts)
			if err != nil {
				err = msgp.WrapError(err)
				return
			}
		}
	}
	o = bts
	return
}

// Msgsize returns an upper bound estimate of the number of bytes occupied by the serialized message
func (z *xlMetaV1Object) Msgsize() (s int) {
	s = 1 + 8 + msgp.StringPrefixSize + len(z.Version) + 7 + msgp.StringPrefixSize + len(z.Format) + 5 + z.Stat.Msgsize() + 8 + z.Erasure.Msgsize() + 6 + 1 + 8 + msgp.StringPrefixSize + len(z.Minio.Release) + 5 + msgp.MapHeaderSize
	if z.Meta != nil {
		for za0001, za0002 := range z.Meta {
			_ = za0002
			s += msgp.StringPrefixSize + len(za0001) + msgp.StringPrefixSize + len(za0002)
		}
	}
	s += 6 + msgp.ArrayHeaderSize
	for za0003 := range z.Parts {
		s += z.Parts[za0003].Msgsize()
	}
	s += 10 + msgp.StringPrefixSize + len(z.VersionID) + 8 + msgp.StringPrefixSize + len(z.DataDir)
	return
}
