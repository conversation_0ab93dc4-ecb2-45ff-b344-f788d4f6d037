// Code generated by "stringer -type=format -trimprefix=format untar.go"; DO NOT EDIT.

package cmd

import "strconv"

func _() {
	// An "invalid array index" compiler error signifies that the constant values have changed.
	// Re-run the stringer command to generate them again.
	var x [1]struct{}
	_ = x[formatUnknown-0]
	_ = x[formatGzip-1]
	_ = x[formatZstd-2]
	_ = x[formatLZ4-3]
	_ = x[formatS2-4]
	_ = x[formatBZ2-5]
}

const _format_name = "UnknownGzipZstdLZ4S2BZ2"

var _format_index = [...]uint8{0, 7, 11, 15, 18, 20, 23}

func (i format) String() string {
	if i < 0 || i >= format(len(_format_index)-1) {
		return "format(" + strconv.FormatInt(int64(i), 10) + ")"
	}
	return _format_name[_format_index[i]:_format_index[i+1]]
}
