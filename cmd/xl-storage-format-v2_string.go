// Code generated by "stringer -type VersionType,ErasureAlgo -output=xl-storage-format-v2_string.go xl-storage-format-v2.go"; DO NOT EDIT.

package cmd

import "strconv"

func _() {
	// An "invalid array index" compiler error signifies that the constant values have changed.
	// Re-run the stringer command to generate them again.
	var x [1]struct{}
	_ = x[invalidVersionType-0]
	_ = x[ObjectType-1]
	_ = x[DeleteType-2]
	_ = x[LegacyType-3]
	_ = x[lastVersionType-4]
}

const _VersionType_name = "invalidVersionTypeObjectTypeDeleteTypeLegacyTypelastVersionType"

var _VersionType_index = [...]uint8{0, 18, 28, 38, 48, 63}

func (i VersionType) String() string {
	if i >= VersionType(len(_VersionType_index)-1) {
		return "VersionType(" + strconv.FormatInt(int64(i), 10) + ")"
	}
	return _VersionType_name[_VersionType_index[i]:_VersionType_index[i+1]]
}
func _() {
	// An "invalid array index" compiler error signifies that the constant values have changed.
	// Re-run the stringer command to generate them again.
	var x [1]struct{}
	_ = x[invalidErasureAlgo-0]
	_ = x[ReedSolomon-1]
	_ = x[lastErasureAlgo-2]
}

const _ErasureAlgo_name = "invalidErasureAlgoReedSolomonlastErasureAlgo"

var _ErasureAlgo_index = [...]uint8{0, 18, 29, 44}

func (i ErasureAlgo) String() string {
	if i >= ErasureAlgo(len(_ErasureAlgo_index)-1) {
		return "ErasureAlgo(" + strconv.FormatInt(int64(i), 10) + ")"
	}
	return _ErasureAlgo_name[_ErasureAlgo_index[i]:_ErasureAlgo_index[i+1]]
}
