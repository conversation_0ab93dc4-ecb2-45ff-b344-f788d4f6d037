# Generate CONTRIBUTORS.md: contributors.sh

# Tip for finding duplicates (besides scanning the output of CONTRIBUTORS.md for name
# duplicates that aren't also email duplicates): scan the output of:
#   git log --format='%aE - %aN' | sort -uf
#
# For explanation on this file format: man git-shortlog

<PERSON> (AB) Periasamy <<EMAIL>> <PERSON> (AB) Periasamy <<EMAIL>>
<PERSON> (AB) Periasamy <<EMAIL>> <<EMAIL>>
<PERSON><PERSON> <<EMAIL>>
<PERSON> IV <<EMAIL>> <<EMAIL>>
<PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>> <<EMAIL>>
<PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>> <<EMAIL>>
<PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>
<PERSON> <<EMAIL>> <<EMAIL>>
<PERSON> <<EMAIL>>
<PERSON> <<EMAIL>>